"""
工具基础接口定义
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from enum import Enum
from core.state import UniversalState

class ToolCategory(Enum):
    """工具类别枚举"""
    INPUT = "input"
    ANALYZER = "analyzer"
    STYLE_CONVERTER = "style_converter"
    CONTENT_PROCESSOR = "content_processor"
    GENERATOR = "generator"
    OUTPUT = "output"

class BaseTool(ABC):
    """工具基础类"""
    
    def __init__(self):
        self.name: str = self.__class__.__name__
        self.category: ToolCategory = ToolCategory.INPUT
        self.description: str = ""
        self.input_keys: List[str] = []  # 从状态中读取的字段
        self.output_keys: List[str] = []  # 向状态中写入的字段
        self.dependencies: List[str] = []  # 依赖的其他工具
    
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑，子类必须实现"""
        pass
    
    def extract_input(self, state: UniversalState) -> Dict[str, Any]:
        """从状态中提取输入数据"""
        input_data = {}
        for key in self.input_keys:
            if key in state:
                input_data[key] = state[key]
        return input_data
    
    def format_output(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """格式化输出结果"""
        formatted_result = {}
        
        # 首先检查并保留错误信息
        error_fields = ["error_message", "error"]
        for error_field in error_fields:
            if error_field in result:
                formatted_result[error_field] = result[error_field]
        
        # 然后保留正常的输出字段
        for key in self.output_keys:
            if key in result:
                formatted_result[key] = result[key]
        
        # 总是包含当前步骤信息
        formatted_result["current_step"] = self.name
        return formatted_result
    
    def execute(self, state: UniversalState) -> Dict[str, Any]:
        """标准执行接口"""
        try:
            # 提取输入
            input_data = self.extract_input(state)
            
            # 执行处理
            result = self.process(input_data)
            
            # 格式化输出
            return self.format_output(result)
            
        except Exception as e:
            return {
                "error_message": f"{self.name} 执行失败: {str(e)}",
                "current_step": self.name
            }
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        for key in self.input_keys:
            if key not in input_data:
                return False
        return True
    
    def get_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.name,
            "category": self.category.value,
            "description": self.description,
            "input_keys": self.input_keys,
            "output_keys": self.output_keys,
            "dependencies": self.dependencies
        }