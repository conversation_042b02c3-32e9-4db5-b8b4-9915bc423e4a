"""
视频脚本分析工具 - 基于Gemini多模态理解生成结构化视频脚本
"""
import json
import logging
import os
import requests
from typing import Dict, Any, List, Optional
from tools.base.tool_interface import BaseTool, ToolCategory

logger = logging.getLogger(__name__)

class VideoScriptAnalyzer(BaseTool):
    """视频脚本分析工具 - 将视频内容分析为结构化脚本JSON"""
    
    def __init__(self):
        super().__init__()
        self.name = "VideoScriptAnalyzer"
        self.category = ToolCategory.ANALYZER
        self.description = "分析视频内容并生成结构化的视频脚本JSON，包含元数据、分镜头信息等"
        self.input_keys = ["video_url", "analysis_requirements"]
        self.output_keys = ["video_script_json", "analysis_summary"]
        
        # 默认使用Gemini模型
        self.default_model = "gemini-2.5-pro-preview-06-05"
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理视频脚本分析"""
        try:
            video_url = input_data.get("video_url")
            if not video_url:
                return {"error_message": "请提供视频URL"}
            
            # 获取API配置
            api_config = self._get_api_config()
            if not api_config["success"]:
                return {"error_message": api_config["error"]}
            
            # 生成分析提示词
            analysis_prompt = self._build_analysis_prompt()
            
            # 调用Gemini API进行视频分析
            analysis_result = self._call_gemini_api(
                video_url, 
                analysis_prompt, 
                api_config
            )
            
            if not analysis_result["success"]:
                return {"error_message": analysis_result["error"]}
            
            # 解析并结构化结果
            script_json = self._parse_to_script_json(analysis_result["content"])
            
            return {
                "video_script_json": script_json,
                "analysis_summary": f"成功分析视频并生成{len(script_json.get('shots', []))}个镜头的脚本",
                "raw_analysis": analysis_result["content"]
            }
            
        except Exception as e:
            logger.error(f"视频脚本分析失败: {e}", exc_info=True)
            return {"error_message": f"视频脚本分析失败: {str(e)}"}
    
    def _get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        # 优先使用专用环境变量
        api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("VIDEO_UNDERSTAND_URL") or os.getenv("OPENAI_BASE_URL", "https://api.openai.com")
        
        if not api_key:
            return {
                "success": False,
                "error": "API密钥未配置。请设置环境变量：VIDEO_UNDERSTAND_KEY 或 OPENAI_API_KEY"
            }
        
        return {
            "success": True,
            "api_key": api_key,
            "base_url": base_url.rstrip('/').rstrip('/v1'),
            "model": self.default_model
        }
    
    def _build_analysis_prompt(self) -> str:
        """构建视频分析提示词"""
        return """你是一个专业的视频分析师。请仔细分析这个视频，并严格按照以下JSON格式输出结构化的视频脚本。

重要提示：
1. 必须返回有效的JSON格式，不要添加任何其他文字
2. 时间必须精确到秒
3. 镜头类型和摄像机运动必须使用指定的枚举值
4. 所有字段都必须填写，如果没有相关内容请填写空字符串或空数组

JSON格式要求：
{
  "videoMetaDataDTO": {
    "videoTitle": "为视频起一个吸引人的标题",
    "videoCover": "描述视频封面或开头画面",
    "videoTheme": "视频的主要主题或类型",
    "narrativeStructure": "叙事结构（如：线性叙事、倒叙、并行叙事、三幕式、起承转合等）",
    "best3sec": "最精彩3秒片段的具体时间点（如：1:23-1:26）和内容描述",
    "visualStyle": "视觉风格（如：现实主义、卡通风格、复古风格、极简主义等）",
    "bgmStyle": "背景音乐风格建议（如：轻松愉快、紧张刺激、抒情温馨等）",
    "characterSheet": [
      {
        "name": "角色名称或描述",
        "description": "角色的详细描述，包括外观、性格等",
        "promptCn": "用于AI生成该角色的中文提示词",
        "promptEn": "用于AI生成该角色的英文提示词"
      }
    ],
    "others": "其他重要信息：包括制作建议、技术要点、创意亮点等你认为重要的内容"
  },
  "shots": [
    {
      "shotNumber": 1,
      "startTime": 0,
      "endTime": 5,
      "duration": 5,
      "scene": "场景的详细描述，包括地点、环境、氛围",
      "characters": ["出现在此镜头中的角色名称"],
      "shotType": "必须是以下之一：EXTREME_LONG_SHOT, LONG_SHOT, MEDIUM_SHOT, CLOSE_UP, EXTREME_CLOSE_UP",
      "cameraMovement": "必须是以下之一：STATIC, PAN, TILT, ZOOM_IN, ZOOM_OUT, DOLLY, TRACKING",
      "description": "这个镜头的详细内容描述，包括动作、表情、重要细节",
      "bgmDescription": "背景音乐的描述，包括情绪、节奏、风格",
      "bgmSource": "音乐来源建议（如：原创配乐、经典音乐、流行歌曲等）",
      "dialogue": ["此镜头中的所有对话内容，每句话一个元素"],
      "onScreenText": ["屏幕上显示的文字内容，如字幕、标题、说明文字等"],
      "sfxDescription": ["音效描述，如脚步声、门响声、环境音等"],
      "prompts": {
        "ability": [
          {
            "abilityType": "根据镜头内容选择合适的能力类型",
            "parameters": {
              "根据选择的能力类型": "设置相应的参数"
            }
          }
        ],
        "cn": "用于生成此镜头画面的中文提示词，详细描述场景、角色、动作、光线、构图等",
        "en": "用于生成此镜头画面的英文提示词，详细描述场景、角色、动作、光线、构图等"
      }
    }
  ]
}

分析步骤：
1. 首先观看整个视频，理解总体内容和结构
2. 识别视频中的主要角色和场景
3. 按时间顺序分析每个镜头的切换点
4. 对每个镜头进行详细分析：
   - 确定精确的开始和结束时间（秒）
   - 识别镜头类型（远景、近景等）
   - 分析摄像机运动
   - 描述场景和角色
   - 提取对话和文字
   - 分析音效和背景音乐
   - 生成用于AI绘图的中英文提示词
5. 生成视频元数据和角色信息
6. 在others字段中添加你的专业建议

重要提示：
- prompts.cn 和 prompts.en 字段必须包含详细的画面描述，用于AI生成内容
- 提示词应该包括：场景环境、角色外观、动作姿态、光线效果、构图角度、艺术风格等
- 中文提示词要自然流畅，英文提示词要专业准确

ability字段说明 - 根据镜头内容选择合适的能力类型：

1. **TEXT_TO_IMAGE** (文生图)
   - 适用于：静态场景、角色特写、风景镜头
   - parameters: {"style": "艺术风格", "quality": "high", "aspect_ratio": "16:9"}

2. **IMAGE_TO_IMAGE** (图生图)
   - 适用于：需要基于参考图像进行风格转换的镜头
   - parameters: {"reference_image": "参考图像描述", "strength": 0.7, "style": "目标风格"}

3. **TEXT_TO_VIDEO** (文生视频)
   - 适用于：有明显动作、运动、变化的镜头
   - parameters: {"duration": "镜头时长", "fps": 24, "motion": "运动描述"}

4. **IMAGE_TO_VIDEO** (图生视频)
   - 适用于：从静态画面开始的动态镜头
   - parameters: {"base_image": "起始画面", "motion": "运动方式", "duration": "时长"}

5. **DIGITAL_HUMAN** (数字人)
   - 适用于：有角色对话、表情变化、人物特写的镜头
   - parameters: {"character": "角色名称", "emotion": "情感状态", "action": "动作描述"}

6. **MOTION_CAPTURE** (动作捕捉)
   - 适用于：复杂的角色动作、舞蹈、运动镜头
   - parameters: {"action_type": "动作类型", "character": "角色", "intensity": "强度"}

7. **FACE_SWAP** (换脸)
   - 适用于：角色替换、面部特效镜头
   - parameters: {"source_face": "源面部", "target_face": "目标面部", "blend_ratio": 0.8}

8. **VOICE_CLONE** (声音克隆)
   - 适用于：配音生成、语音合成镜头
   - parameters: {"voice_model": "声音模型", "text": "文本内容", "emotion": "情感"}

9. **STYLE_TRANSFER** (风格迁移)
   - 适用于：艺术风格转换、特效处理镜头
   - parameters: {"source_style": "源风格", "target_style": "目标风格", "strength": 0.7}

请根据每个镜头的具体内容选择最合适的ability类型，可以选择多个ability组合使用。

请确保返回的JSON格式完全正确，可以直接被程序解析。"""
    
    def _call_gemini_api(self, video_url: str, prompt: str, api_config: Dict[str, Any]) -> Dict[str, Any]:
        """调用Gemini API进行视频分析"""
        try:
            api_url = f"{api_config['base_url']}/v1/chat/completions"
            
            headers = {
                "Authorization": f"Bearer {api_config['api_key']}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": video_url
                            }
                        }
                    ]
                }
            ]
            
            payload = {
                "model": api_config["model"],
                "messages": messages,
                "stream": False,
                "temperature": 0.1  # 降低温度以获得更一致的结构化输出
            }
            
            logger.info(f"发送视频脚本分析请求到 {api_url}")
            response = requests.post(api_url, headers=headers, json=payload, timeout=180)
            response.raise_for_status()
            
            response_data = response.json()
            choices = response_data.get("choices", [])
            
            if not choices:
                return {
                    "success": False,
                    "error": "API响应中没有找到分析结果"
                }
            
            content = choices[0].get("message", {}).get("content", "")
            if not content:
                return {
                    "success": False,
                    "error": "API返回空内容"
                }
            
            return {
                "success": True,
                "content": content,
                "usage": response_data.get("usage", {})
            }
            
        except requests.exceptions.HTTPError as e:
            error_detail = "未知错误"
            try:
                error_response = e.response.json()
                error_detail = error_response.get("error", {}).get("message", str(e))
            except:
                error_detail = str(e)
            
            return {
                "success": False,
                "error": f"API请求失败: {error_detail}"
            }
        
        except Exception as e:
            return {
                "success": False,
                "error": f"API调用出错: {str(e)}"
            }
    
    def _parse_to_script_json(self, raw_content: str) -> Dict[str, Any]:
        """解析原始内容为结构化JSON"""
        try:
            # 尝试直接解析JSON
            # 移除可能的markdown代码块标记
            content = raw_content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.startswith("```"):
                content = content[3:]
            if content.endswith("```"):
                content = content[:-3]
            
            content = content.strip()
            
            # 解析JSON
            script_json = json.loads(content)
            
            # 验证必要字段
            if "videoMetaDataDTO" not in script_json:
                script_json["videoMetaDataDTO"] = {}
            if "shots" not in script_json:
                script_json["shots"] = []
            
            return script_json
            
        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败，尝试修复: {e}")
            # 如果JSON解析失败，返回一个基础结构
            return {
                "videoMetaDataDTO": {
                    "videoTitle": "解析失败",
                    "others": f"原始分析结果: {raw_content[:500]}..."
                },
                "shots": [],
                "parse_error": str(e)
            }
        
        except Exception as e:
            logger.error(f"脚本解析出错: {e}")
            return {
                "videoMetaDataDTO": {
                    "videoTitle": "解析出错",
                    "others": f"错误信息: {str(e)}"
                },
                "shots": []
            }
