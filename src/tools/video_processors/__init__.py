"""
视频处理工具模块
"""
from .video_input import VideoInputTool
from .video_script_analyzer import VideoScriptAnalyzer
from .script_validator import ScriptValidatorTool

# 注册工具到工具注册表
try:
    from tools.base.tool_registry import ToolRegistry
    from tools.base.tool_interface import ToolCategory

    # 手动注册视频处理工具
    ToolRegistry._tools["video_input"] = VideoInputTool
    ToolRegistry._tools["video_script_analyzer"] = VideoScriptAnalyzer
    ToolRegistry._tools["script_validator"] = ScriptValidatorTool

    # 添加到对应类别
    if ToolCategory.INPUT not in ToolRegistry._categories:
        ToolRegistry._categories[ToolCategory.INPUT] = []
    if ToolCategory.ANALYZER not in ToolRegistry._categories:
        ToolRegistry._categories[ToolCategory.ANALYZER] = []
    if ToolCategory.CONTENT_PROCESSOR not in ToolRegistry._categories:
        ToolRegistry._categories[ToolCategory.CONTENT_PROCESSOR] = []

    ToolRegistry._categories[ToolCategory.INPUT].append("video_input")
    ToolRegistry._categories[ToolCategory.ANALYZER].append("video_script_analyzer")
    ToolRegistry._categories[ToolCategory.CONTENT_PROCESSOR].append("script_validator")

except ImportError:
    # 如果工具注册表不存在，跳过注册
    pass

__all__ = ["VideoInputTool", "VideoScriptAnalyzer", "ScriptValidatorTool"]
