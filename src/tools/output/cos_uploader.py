"""
腾讯云COS上传工具
"""
import logging
import uuid
import os
from io import BytesIO
from typing import Dict, Any

from tools.base import BaseTool, ToolCategory, ToolRegistry

logger = logging.getLogger(__name__)

# 检查COS依赖
try:
    from qcloud_cos import CosConfig, CosS3Client
    COS_AVAILABLE = True
except ImportError:
    logger.warning("腾讯云COS依赖未安装，请执行: pip install cos-python-sdk-v5")
    COS_AVAILABLE = False

@ToolRegistry.register(ToolCategory.OUTPUT, "cos_uploader")
class CosUploaderTool(BaseTool):
    """腾讯云COS上传工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将文件上传到腾讯云COS并返回公网URL"
        self.input_keys = ["file_path", "file_content", "file_extension"]
        self.output_keys = ["cos_url", "upload_metadata"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """上传文件到腾讯云COS"""
        
        if not COS_AVAILABLE:
            return {"error_message": "腾讯云COS依赖未安装"}
        
        # 获取COS配置
        cos_config = self._get_cos_config()
        if not cos_config:
            return {"error_message": "COS配置缺失，请设置环境变量"}
        
        try:
            # 初始化COS客户端
            cos_client = CosS3Client(cos_config)
            
            # 获取文件内容
            file_bytes = self._get_file_content(input_data)
            if not file_bytes:
                return {"error_message": "无法获取文件内容"}
            
            # 确定文件扩展名
            file_extension = input_data.get("file_extension", "png")
            
            # 生成唯一文件名
            key = f"intelligent_assistant/{uuid.uuid4()}.{file_extension}"
            
            # 上传文件
            file_obj = BytesIO(file_bytes)
            bucket = os.getenv("COS_BUCKET")
            region = os.getenv("COS_REGION")
            
            cos_client.put_object(
                Bucket=bucket,
                Body=file_obj,
                Key=key,
            )
            
            # 构建公网URL
            cos_url = f"https://{bucket}.cos.{region}.myqcloud.com/{key}"
            
            logger.info(f"文件已成功上传到COS: {cos_url}")
            
            return {
                "cos_url": cos_url,
                "upload_metadata": {
                    "bucket": bucket,
                    "region": region,
                    "key": key,
                    "file_size": len(file_bytes),
                    "file_extension": file_extension
                }
            }
            
        except Exception as e:
            logger.error(f"上传文件到COS失败: {e}")
            return {"error_message": f"上传失败: {str(e)}"}
    
    def _get_cos_config(self):
        """获取COS配置"""
        secret_id = os.getenv("TENCENT_SECRET_ID")
        secret_key = os.getenv("TENCENT_SECRET_KEY")
        region = os.getenv("COS_REGION")
        
        if not all([secret_id, secret_key, region]):
            logger.error("COS配置缺失，需要设置环境变量: TENCENT_SECRET_ID, TENCENT_SECRET_KEY, COS_REGION, COS_BUCKET")
            return None
        
        return CosConfig(
            Region=region,
            SecretId=secret_id,
            SecretKey=secret_key,
            Scheme="https",
        )
    
    def _get_file_content(self, input_data: Dict[str, Any]) -> bytes:
        """获取文件内容"""
        # 优先使用直接传入的文件内容
        if "file_content" in input_data:
            content = input_data["file_content"]
            if isinstance(content, bytes):
                return content
            elif isinstance(content, str):
                return content.encode('utf-8')
        
        # 从文件路径读取
        if "file_path" in input_data:
            file_path = input_data["file_path"]
            try:
                with open(file_path, 'rb') as f:
                    return f.read()
            except Exception as e:
                logger.error(f"读取文件失败: {e}")
        
        return None

def test_cos_uploader_tool():
    """测试COS上传工具"""
    print("🧪 测试COS上传工具")
    
    if not COS_AVAILABLE:
        print("⚠️ 跳过测试：COS依赖未安装")
        return
    
    # 检查环境变量
    required_vars = ["TENCENT_SECRET_ID", "TENCENT_SECRET_KEY", "COS_REGION", "COS_BUCKET"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"⚠️ 跳过测试：缺少环境变量 {missing_vars}")
        return
    
    tool = CosUploaderTool()
    
    # 测试上传文本文件
    test_content = "这是一个测试文件内容"
    test_input = {
        "file_content": test_content,
        "file_extension": "txt"
    }
    
    result = tool.process(test_input)
    
    if result.get("error_message"):
        print(f"❌ 测试失败: {result['error_message']}")
    else:
        print(f"✅ 测试成功!")
        print(f"   COS URL: {result['cos_url']}")
        print(f"   文件大小: {result['upload_metadata']['file_size']} bytes")
    
    print("✅ CosUploaderTool 测试完成!\n")

if __name__ == "__main__":
    test_cos_uploader_tool()