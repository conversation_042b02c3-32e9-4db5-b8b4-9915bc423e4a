"""
简单输出工具
"""
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.OUTPUT, "image")
class SimpleOutputTool(BaseTool):
    """简单输出工具，用于测试"""
    
    def __init__(self):
        super().__init__()
        self.description = "简单的输出处理，用于测试框架"
        self.input_keys = ["raw_content", "processed_content"]
        self.output_keys = ["final_outputs"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理输出"""
        
        content = input_data.get("processed_content") or input_data.get("raw_content", "")
        
        # 模拟生成输出文件
        output_path = "output/test_result.txt"
        
        # 这里可以实际写文件或生成图片
        print(f"模拟生成输出文件: {output_path}")
        print(f"内容: {content}")
        
        return {
            "final_outputs": [output_path],
            "image_paths": [output_path]  # 向后兼容
        }