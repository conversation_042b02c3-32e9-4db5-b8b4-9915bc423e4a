"""
模拟图片转换工具 - 用于测试，不依赖playwright
"""
import os
from typing import Dict, Any, List
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.OUTPUT, "html2image_mock")
class MockImageConverterTool(BaseTool):
    """模拟HTML转图片工具（用于测试）"""
    
    def __init__(self):
        super().__init__()
        self.description = "模拟将HTML内容转换为图片文件（测试用）"
        self.input_keys = ["html_contents"]
        self.output_keys = ["image_paths", "final_outputs"]
        
        # 输出配置
        self.output_dir = "output/images"
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """模拟HTML转图片处理"""
        
        html_contents = input_data.get("html_contents")
        
        if not html_contents:
            return {
                "error_message": "缺少HTML内容"
            }
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        try:
            image_paths = []
            html_paths = []
            
            for i, html_content in enumerate(html_contents):
                print(f"模拟转换第 {i+1} 个HTML为图片...")
                
                # 保存HTML文件
                html_path = os.path.join(self.output_dir, f"card_{i+1}.html")
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(html_content)
                html_paths.append(html_path)
                
                # 模拟生成图片文件（实际只是创建一个占位符）
                image_path = os.path.join(self.output_dir, f"card_{i+1}_mock.png")
                with open(image_path, "w") as f:
                    f.write(f"Mock image file for card {i+1}")
                
                image_paths.append(image_path)
                print(f"模拟图片生成: {image_path}")
            
            print(f"模拟图片转换完成，共生成 {len(image_paths)} 张图片")
            
            return {
                "image_paths": image_paths,
                "final_outputs": image_paths,
                "conversion_metadata": {
                    "total_images": len(image_paths),
                    "html_files": html_paths,
                    "output_dir": self.output_dir,
                    "mock": True
                }
            }
            
        except Exception as e:
            return {
                "error_message": f"模拟图片转换失败: {str(e)}"
            }