"""
文本输入工具
"""
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.INPUT, "text_input")
class TextInputTool(BaseTool):
    """文本输入工具，处理用户输入的文本"""
    
    def __init__(self):
        super().__init__()
        self.description = "处理用户输入的文本内容"
        self.input_keys = ["user_request"]
        self.output_keys = ["raw_content"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理文本输入"""
        
        user_request = input_data.get("user_request", "")
        
        # 这里可以添加文本预处理逻辑
        # 比如清理、格式化等
        
        processed_text = user_request.strip()
        
        return {
            "raw_content": processed_text,
            "content_metadata": {
                "length": len(processed_text),
                "type": "text"
            }
        }

def test_text_input_tool():
    """测试文本输入工具"""
    print("🧪 测试 TextInputTool")
    
    tool = TextInputTool()
    
    # 测试用例1：正常文本
    test_input = {"user_request": "这是一个测试文本"}
    result = tool.process(test_input)
    
    print(f"输入: {test_input}")
    print(f"输出: {result}")
    
    assert "raw_content" in result
    assert result["raw_content"] == "这是一个测试文本"
    assert "content_metadata" in result
    print("✅ 测试1通过")
    
    # 测试用例2：空文本
    test_input2 = {"user_request": "   "}
    result2 = tool.process(test_input2)
    print(f"输入: {test_input2}")
    print(f"输出: {result2}")
    assert result2["raw_content"] == ""
    print("✅ 测试2通过")
    
    print("✅ TextInputTool 所有测试通过!\n")

if __name__ == "__main__":
    test_text_input_tool()