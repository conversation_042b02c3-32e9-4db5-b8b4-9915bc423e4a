"""
HTML生成工具 - 从现有html_generator.py迁移
"""
from typing import Dict, Any, List
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from langchain_core.prompts import PromptTemplate

@ToolRegistry.register(ToolCategory.GENERATOR, "html")
class HtmlGeneratorTool(BaseTool):
    """HTML卡片生成工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "为每个文本片段生成HTML卡片"
        self.input_keys = ["content_segments", "processed_content", "raw_content", "context"]
        self.output_keys = ["html_contents", "html_metadata"]
        
        # 通用封面页提示词模板
        self.cover_page_prompt = """
你是一位专业的视觉设计师，擅长根据不同的内容和风格要求创建精美的HTML卡片。

## 任务：为以下内容生成一张封面卡片

### 输入内容：
{RAW_INPUT_TEXT}

### 用户偏好：
{USER_PREFERENCES}

### 设计要求：
{CONTEXT}

## 设计指南：

1. **内容处理**：
   - 从输入内容中提炼核心信息，创建吸引人的标题
   - 根据内容性质调整语言风格（正式/轻松/幽默等）

2. **风格应用**：
   - **严格按照设计要求中的所有配置进行设计**
   - 如果提供了color_scheme，必须使用指定的颜色
   - 如果提供了typography，必须使用指定的字体和尺寸
   - 如果提供了elements配置，必须应用指定的边框、圆角、阴影等
   - 如果提供了visual_style描述，必须体现相应的视觉风格

3. **技术要求**：
   - 输出完整的HTML5文档，尺寸600x800px
   - 所有CSS写在<style>标签内
   - 使用现代布局技术（Flexbox/Grid）
   - 确保在指定尺寸内美观展示

4. **输出要求**：
   - 只输出HTML代码，无任何解释文字
   - 不使用```html标记
   - 代码可直接保存为.html文件

请根据以上要求生成卡片：
"""

        # 通用内容页提示词模板
        self.content_page_prompt = """
你是一位专业的视觉设计师，擅长创建易读且美观的内容展示卡片。

## 任务：为以下文本内容生成一张内容卡片

### 文本内容：
{text_segment}

### 设计要求：
{CONTEXT}

## 设计指南：

1. **内容展示**：
   - 确保文本清晰易读，合理分段
   - 突出重点信息，保持良好的视觉层次
   - 根据内容长度调整排版

2. **风格应用**：
   - **严格按照设计要求中的所有配置进行设计**
   - 使用指定的color_scheme中的颜色
   - 应用指定的typography字体和尺寸
   - 实现指定的elements样式（边框、圆角、阴影等）
   - 体现visual_style中描述的视觉风格

3. **技术要求**：
   - 输出完整的HTML5文档，尺寸600x800px
   - 所有CSS写在<style>标签内
   - 使用现代布局技术保证响应式效果
   - 优化阅读体验和视觉美感

4. **输出要求**：
   - 只输出HTML代码，无任何解释文字
   - 不使用```html标记
   - 代码可直接保存为.html文件

请根据以上要求生成卡片：
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成HTML卡片"""
        
        segments = input_data.get("content_segments")
        processed_content = input_data.get("processed_content", "")
        raw_content = input_data.get("raw_content", "")
        context = input_data.get("context", {})
        
        if not segments:
            return {
                "error_message": "缺少文本片段数据"
            }
        
        try:
            llm = get_llm(temperature=0.7)
            generated_htmls = []
            
            for i, segment in enumerate(segments):
                print(f"生成第 {i+1} 个HTML卡片...")
                
                if i == 0:  # 第一个片段作为封面
                    html_content = self._generate_cover_page(
                        llm, processed_content or raw_content, "", context
                    )
                else:  # 后续片段作为内容页
                    html_content = self._generate_content_page(
                        llm, segment, context
                    )
                
                if html_content:
                    generated_htmls.append(html_content)
                else:
                    print(f"第 {i+1} 个HTML生成失败")
            
            if not generated_htmls:
                return {
                    "error_message": "所有HTML卡片生成失败"
                }
            
            print(f"HTML生成完成，共生成 {len(generated_htmls)} 个卡片")
            
            return {
                "html_contents": generated_htmls,
                "html_metadata": {
                    "total_cards": len(generated_htmls),
                    "cover_pages": 1,
                    "content_pages": len(generated_htmls) - 1
                }
            }
            
        except Exception as e:
            return {
                "error_message": f"HTML生成失败: {str(e)}"
            }
    
    def _generate_cover_page(self, llm, content: str, preferences: str, context: dict) -> str:
        """生成封面页HTML"""
        
        try:
            input_variables = {
                "RAW_INPUT_TEXT": content,
                "USER_PREFERENCES": preferences,
                "CONTEXT": self._format_context(context)
            }
            
            prompt_template = PromptTemplate.from_template(self.cover_page_prompt.strip())
            chain = prompt_template | llm
            response = chain.invoke(input_variables)
            
            # 调试：保存原始输出
            raw_output = response.content
            print(f"\n=== 封面页LLM原始输出 ===")
            print(f"原始输出长度: {len(raw_output)}")
            print(f"原始输出前500字符: {raw_output[:500]}")
            print(f"原始输出后500字符: {raw_output[-500:]}")
            
            # 处理HTML
            cleaned_html = self._clean_html_response(raw_output)
            print(f"\n=== 封面页HTML处理结果 ===")
            print(f"处理后长度: {len(cleaned_html)}")
            print(f"处理后前500字符: {cleaned_html[:500]}")
            print(f"处理后后500字符: {cleaned_html[-500:]}")
            print(f"=== 对比结束 ===\n")
            
            return cleaned_html
            
        except Exception as e:
            print(f"封面页生成失败: {e}")
            return None
    
    def _generate_content_page(self, llm, segment: str, context: dict) -> str:
        """生成内容页HTML"""
        
        try:
            input_variables = {
                "text_segment": segment,
                "CONTEXT": self._format_context(context)
            }
            
            prompt_template = PromptTemplate.from_template(self.content_page_prompt.strip())
            chain = prompt_template | llm
            response = chain.invoke(input_variables)
            
            # 调试：保存原始输出
            raw_output = response.content
            print(f"\n=== 内容页LLM原始输出 ===")
            print(f"原始输出长度: {len(raw_output)}")
            print(f"原始输出前500字符: {raw_output[:500]}")
            print(f"原始输出后500字符: {raw_output[-500:]}")
            
            # 处理HTML
            cleaned_html = self._clean_html_response(raw_output)
            print(f"\n=== 内容页HTML处理结果 ===")
            print(f"处理后长度: {len(cleaned_html)}")
            print(f"处理后前500字符: {cleaned_html[:500]}")
            print(f"处理后后500字符: {cleaned_html[-500:]}")
            print(f"=== 对比结束 ===\n")
            
            return cleaned_html
            
        except Exception as e:
            print(f"内容页生成失败: {e}")
            return None
    
    def _format_context(self, context: dict) -> str:
        """格式化上下文信息为结构化字符串"""
        if not context:
            return ""
        
        formatted = "上下文信息：\n"
        for key, value in context.items():
            if value:  # 只包含有值的参数
                formatted += f"- {key}: {value}\n"
        
        return formatted.strip()
    
    def _clean_html_response(self, raw_response: str) -> str:
        """清理LLM返回的HTML内容"""
        
        raw_response = raw_response.strip()
        
        # 首先尝试移除Markdown代码块标记
        content = raw_response
        if content.startswith("```html"):
            content = content[len("```html"):].strip()
        elif content.startswith("```"):
            content = content[len("```"):].strip()
        
        if content.endswith("```"):
            content = content[:-len("```")].strip()
        
        # 检查HTML完整性的辅助函数
        def is_html_complete(html_content: str) -> bool:
            """检查HTML是否完整"""
            html_lower = html_content.lower()
            
            # 检查基本HTML结构
            has_html_tag = "<html" in html_lower and "</html>" in html_lower
            has_head_tag = "<head" in html_lower and "</head>" in html_lower  
            has_body_tag = "<body" in html_lower and "</body>" in html_lower
            
            # 检查CSS样式是否完整（没有未闭合的大括号）
            open_braces = html_content.count('{')
            close_braces = html_content.count('}')
            balanced_braces = open_braces == close_braces
            
            return has_html_tag and has_head_tag and has_body_tag and balanced_braces
        
        # 查找完整的HTML文档 - 支持多种格式
        html_patterns = [
            ("<!doctype html", "</html>"),
            ("<!DOCTYPE html", "</html>"),
            ("<!DOCTYPE HTML", "</html>"),
            ("<html", "</html>"),
            ("<HTML", "</HTML>")
        ]
        
        for start_marker, end_marker in html_patterns:
            start_idx = content.lower().find(start_marker.lower())
            if start_idx != -1:
                content_after_start = content[start_idx:]
                # 使用rfind查找最后一个</html>标记，确保获取完整HTML
                end_idx = content_after_start.lower().rfind(end_marker.lower())
                
                if end_idx != -1:
                    # 确保包含完整的结束标记
                    full_html = content_after_start[:end_idx + len(end_marker)]
                    
                    # 验证HTML是否完整
                    if is_html_complete(full_html):
                        return full_html
                    else:
                        # 如果不完整，尝试返回全部内容
                        print(f"警告: 检测到不完整的HTML，返回全部内容")
                        return content_after_start
                else:
                    # 找到开始标记但没有结束标记，返回从开始标记到结尾的内容
                    print(f"警告: 找到HTML开始标记但没有结束标记")
                    return content_after_start
        
        # 如果没有找到完整的HTML结构，但包含基本HTML标签，直接返回
        if any(tag in content.lower() for tag in ["<div", "<body", "<head", "<style"]):
            print(f"警告: 未找到完整HTML结构，但包含HTML标签")
            return content
        
        # 最后回退：返回原始内容
        print(f"警告: 无法识别HTML格式，返回原始内容")
        return content

class MockLLMHtml:
    """模拟LLM用于HTML生成测试"""
    def invoke(self, input_vars):
        class MockResponse:
            def __init__(self):
                self.content = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书风格卡片</title>
    <style>
        .card { width: 600px; height: 800px; background: linear-gradient(135deg, #ff6b6b, #feca57); padding: 20px; }
        .title { font-size: 24px; color: white; font-weight: bold; }
        .content { font-size: 16px; color: white; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="card">
        <div class="title">✨重磅！新一代AI芯片发布！🔥</div>
        <div class="content">💡 科技圈又炸锅啦！最新发布的AI芯片简直不要太强！</div>
    </div>
</body>
</html>"""
        return MockResponse()

def test_html_generator_tool():
    """测试HTML生成工具"""
    print("🧪 测试 HtmlGeneratorTool")
    
    tool = HtmlGeneratorTool()
    
    # 使用mock LLM避免实际API调用
    original_get_llm = None
    try:
        import llm_services
        original_get_llm = llm_services.get_llm
        llm_services.get_llm = lambda temperature=0.7: MockLLMHtml()
        
        # 测试用例1：正常HTML生成
        test_input = {
            "content_segments": [
                "✨重磅！新一代AI芯片发布，性能暴增50%！🔥",
                "💡 科技圈又炸锅啦！最新发布的AI芯片简直不要太强！",
                "🌟 性能提升50%是什么概念？手机运行速度飞起！"
            ],
            "processed_content": "✨重磅！新一代AI芯片发布，性能暴增50%！🔥",
            "raw_content": "科技创新推动产业升级"
        }
        result = tool.process(test_input)
        
        print(f"输入片段数: {len(test_input['content_segments'])}")
        print(f"输出: {result}")
        
        assert "html_contents" in result
        assert "html_metadata" in result
        assert isinstance(result["html_contents"], list)
        assert len(result["html_contents"]) == 3  # 应该生成3个HTML
        print("✅ 测试1通过")
        
        # 测试用例2：空片段
        test_input2 = {"content_segments": []}
        result2 = tool.process(test_input2)
        print(f"输入: {test_input2}")
        print(f"输出: {result2}")
        assert "error_message" in result2
        print("✅ 测试2通过")
        
        # 测试用例3：缺少参数
        test_input3 = {}
        result3 = tool.process(test_input3)
        print(f"输入: {test_input3}")
        print(f"输出: {result3}")
        assert "error_message" in result3
        print("✅ 测试3通过")
        
        print("✅ HtmlGeneratorTool 所有测试通过!\n")
        
    finally:
        # 恢复原始LLM
        if original_get_llm:
            llm_services.get_llm = original_get_llm

if __name__ == "__main__":
    test_html_generator_tool()