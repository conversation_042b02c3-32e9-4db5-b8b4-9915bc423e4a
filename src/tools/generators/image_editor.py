"""
图像编辑工具 - 基于Flux Kontext API
"""
import os
import time
import requests
import logging
from typing import Dict, Any, Optional

from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm

logger = logging.getLogger(__name__)

@ToolRegistry.register(ToolCategory.GENERATOR, "image_editor")
class ImageEditorTool(BaseTool):
    """图像编辑工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "基于文本指令编辑现有图像，支持风格转换、元素添加/移除等操作"
        self.input_keys = ["input_image_url", "edit_prompt", "model", "aspect_ratio"]
        self.output_keys = ["edited_image_url", "cos_url", "edit_metadata"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """编辑图像"""
        
        # 获取输入参数
        input_image_url = input_data.get("input_image_url", "")
        edit_prompt = input_data.get("edit_prompt", "")
        model = input_data.get("model", "black-forest-labs/flux-kontext-pro")
        aspect_ratio = input_data.get("aspect_ratio", "match_input_image")
        seed = input_data.get("seed", None)
        output_format = input_data.get("output_format", "png")
        enhance_prompt = input_data.get("enhance_prompt", True)
        
        # 参数验证
        if not input_image_url:
            return {"error_message": "输入图像URL不能为空"}
        
        if not input_image_url.startswith(('http://', 'https://')):
            return {"error_message": "输入图像URL必须是有效的公网URL"}
        
        if not edit_prompt:
            return {"error_message": "编辑指令不能为空"}
        
        # 增强编辑指令
        if enhance_prompt:
            enhanced_prompt = self._enhance_edit_prompt(edit_prompt)
        else:
            enhanced_prompt = edit_prompt
        
        # 调用Flux API编辑图像
        edit_result = self._edit_image_with_flux(
            enhanced_prompt, input_image_url, model, aspect_ratio, seed, output_format
        )
        
        if not edit_result or edit_result.get("error"):
            return {"error_message": f"图像编辑失败: {edit_result.get('error', '未知错误')}"}
        
        edited_image_url = edit_result["image_url"]
        
        # 上传到COS
        cos_result = self._upload_to_cos(edited_image_url, enhanced_prompt)
        
        return {
            "edited_image_url": edited_image_url,
            "cos_url": cos_result.get("cos_url", edited_image_url),
            "edit_metadata": {
                "original_prompt": edit_prompt,
                "enhanced_prompt": enhanced_prompt,
                "input_image_url": input_image_url,
                "model": model,
                "aspect_ratio": aspect_ratio,
                "output_format": output_format,
                "api_used": "flux_kontext"
            }
        }
    
    def _enhance_edit_prompt(self, prompt: str) -> str:
        """使用LLM增强编辑指令"""
        try:
            enhancement_prompt = f"""
作为专业的AI图像编辑指令优化师，请将以下编辑需求转换为详细的英文指令：

原始编辑需求：{prompt}

请生成一个优化后的专业英文编辑指令，要求：
1. 使用清晰、详细的英文描述
2. 包含具体的视觉效果要求
3. 适合AI图像编辑模型理解和执行
4. 保持原始需求的核心意图
5. 添加专业的图像处理术语

请直接返回优化后的英文指令，不要其他说明：
"""
            
            llm = get_llm()
            response = llm.invoke(enhancement_prompt)
            enhanced = response.content.strip()
            
            # 清理格式
            enhanced = enhanced.replace('\n', ' ').replace('\r', ' ')
            enhanced = ' '.join(enhanced.split())
            
            if len(enhanced) > 500:
                enhanced = enhanced[:500] + "..."
            
            logger.info("编辑指令增强成功")
            return enhanced
            
        except Exception as e:
            logger.warning(f"编辑指令增强失败: {e}，使用简单增强")
            return self._simple_enhance_edit_prompt(prompt)
    
    def _simple_enhance_edit_prompt(self, prompt: str) -> str:
        """简单的编辑指令增强"""
        # 基础质量要求
        quality_terms = "high quality, professional editing, detailed processing"
        
        # 检查是否已经是英文
        if any(ord(char) > 127 for char in prompt):
            # 包含中文，需要基础翻译
            translation_map = {
                "添加": "add",
                "移除": "remove",
                "删除": "delete",
                "改变": "change",
                "转换": "transform",
                "风格": "style",
                "颜色": "color",
                "背景": "background",
                "光线": "lighting",
                "效果": "effect"
            }
            
            english_prompt = prompt
            for zh, en in translation_map.items():
                english_prompt = english_prompt.replace(zh, en)
        else:
            english_prompt = prompt
        
        return f"{english_prompt}, {quality_terms}"
    
    def _edit_image_with_flux(self, prompt: str, input_image_url: str, model: str, 
                             aspect_ratio: str, seed: Optional[int], output_format: str) -> Optional[Dict[str, Any]]:
        """使用Flux API编辑图像"""
        try:
            # 获取API配置
            api_key = os.getenv("FLUX_API_KEY")
            base_url = os.getenv("FLUX_BASE_URL")
            
            if not api_key or not base_url:
                return {"error": "未配置FLUX_API_KEY或FLUX_BASE_URL"}
            
            # 构建API URL
            url = f"{base_url.rstrip('/')}/replicate/v1/models/{model}/predictions"
            
            # 构建请求payload
            payload = {
                "input": {
                    "prompt": prompt,
                    "input_image": input_image_url,
                    "aspect_ratio": aspect_ratio,
                    "output_format": output_format,
                    "safety_tolerance": 2,
                }
            }
            
            # 添加可选的seed参数
            if seed is not None:
                payload["input"]["seed"] = seed
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"正在编辑图像: {prompt[:50]}...")
            
            # 发送初始请求
            initial_response = requests.post(url, json=payload, headers=headers)
            initial_response.raise_for_status()
            result = initial_response.json()
            
            # 轮询获取结果
            task_id = result.get("id")
            polling_url = f"{base_url.rstrip('/')}/replicate/v1/predictions/{task_id}"
            
            max_attempts = 60  # 约2分钟
            for _ in range(max_attempts):
                poll_response = requests.get(polling_url, headers=headers)
                poll_response.raise_for_status()
                poll_result = poll_response.json()
                
                status = poll_result.get("status")
                if status == "succeeded":
                    output = poll_result.get("output")
                    final_image_url = ""
                    
                    if output and isinstance(output, list) and len(output) > 0:
                        final_image_url = output[0]
                    elif output and isinstance(output, str):
                        final_image_url = output
                    
                    if not final_image_url:
                        return {"error": f"任务完成但无有效输出: {poll_result}"}
                    
                    logger.info("图像编辑成功")
                    return {"image_url": final_image_url}
                
                if status in ["failed", "canceled"]:
                    error_message = poll_result.get('error', '未知错误')
                    return {"error": f"编辑任务失败: {error_message}"}
                
                time.sleep(2)  # 等待2秒后再次轮询
            
            return {"error": f"等待任务完成超时 ({max_attempts * 2}秒)"}
            
        except requests.exceptions.RequestException as e:
            error_content = e.response.text if e.response else "No response content"
            logger.error(f"Flux API请求错误: {e}")
            return {"error": f"API请求失败: {str(e)}"}
        except Exception as e:
            logger.error(f"图像编辑异常: {e}")
            return {"error": str(e)}
    
    def _upload_to_cos(self, image_url: str, prompt: str) -> Dict[str, Any]:
        """上传编辑后的图像到腾讯云COS"""
        try:
            # 导入COS上传工具
            from tools.output.cos_uploader import CosUploaderTool
            
            # 下载图像
            response = requests.get(image_url, timeout=120)
            response.raise_for_status()
            image_bytes = response.content
            
            # 确定文件扩展名
            content_type = response.headers.get('content-type', '').lower()
            extension_map = {
                'image/jpeg': 'jpg',
                'image/png': 'png',
                'image/gif': 'gif',
                'image/webp': 'webp',
            }
            file_extension = extension_map.get(content_type, 'png')
            
            # 使用COS上传工具
            cos_tool = CosUploaderTool()
            cos_input = {
                "file_content": image_bytes,
                "file_extension": file_extension
            }
            
            cos_result = cos_tool.process(cos_input)
            
            if cos_result.get("error_message"):
                logger.warning(f"COS上传失败: {cos_result['error_message']}")
                return {"cos_url": image_url}  # 返回原始URL
            else:
                logger.info(f"编辑后图像已上传到COS: {cos_result['cos_url']}")
                return cos_result
                
        except Exception as e:
            logger.error(f"上传到COS时发生错误: {e}")
            return {"cos_url": image_url}  # 返回原始URL

def test_image_editor_tool():
    """测试图像编辑工具"""
    print("🧪 测试图像编辑工具")
    
    # 检查API密钥
    if not os.getenv("FLUX_API_KEY") or not os.getenv("FLUX_BASE_URL"):
        print("⚠️ 跳过测试：未配置FLUX_API_KEY或FLUX_BASE_URL")
        return
    
    # 导入必要工具以触发注册
    import tools.output.cos_uploader
    
    tool = ImageEditorTool()
    
    # 测试编辑图像（需要一个有效的图像URL）
    test_input = {
        "input_image_url": "https://example.com/test-image.jpg",  # 请替换为实际的图像URL
        "edit_prompt": "将这张图片转换为卡通风格，添加明亮的色彩",
        "model": "black-forest-labs/flux-kontext-pro",
        "aspect_ratio": "match_input_image",
        "enhance_prompt": True
    }
    
    result = tool.process(test_input)
    
    if result.get("error_message"):
        print(f"❌ 测试失败: {result['error_message']}")
    else:
        print(f"✅ 测试成功!")
        print(f"   原始图像URL: {result['edited_image_url']}")
        print(f"   COS URL: {result['cos_url']}")
        print(f"   使用模型: {result['edit_metadata']['model']}")
        print(f"   增强指令: {result['edit_metadata']['enhanced_prompt'][:100]}...")
    
    print("✅ ImageEditorTool 测试完成!\n")

if __name__ == "__main__":
    test_image_editor_tool()