"""
封面页生成工具 - 专门用于生成封面页HTML
"""
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from langchain_core.prompts import PromptTemplate

@ToolRegistry.register(ToolCategory.GENERATOR, "cover_page")
class CoverPageGeneratorTool(BaseTool):
    """封面页生成工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "生成封面页HTML卡片"
        self.input_keys = ["raw_content", "processed_content", "context"]
        self.output_keys = ["cover_html"]
        
        # 封面页专用提示词模板
        self.cover_prompt = """
你是一位专业的视觉设计师，擅长根据不同的内容和风格要求创建精美的HTML卡片。

## 任务：为以下内容生成一张封面卡片

### 输入内容：
{RAW_INPUT_TEXT}

### 用户偏好：
{USER_PREFERENCES}

### 设计要求：
{CONTEXT}

## 设计指南：

1. **内容处理**：
   - 从输入内容中提炼核心信息，创建吸引人的标题
   - 根据内容性质调整语言风格（正式/轻松/幽默等）

2. **风格应用**：
   - **严格按照设计要求中的所有配置进行设计**
   - 如果提供了color_scheme，必须使用指定的颜色
   - 如果提供了typography，必须使用指定的字体和尺寸
   - 如果提供了elements配置，必须应用指定的边框、圆角、阴影等
   - 如果提供了visual_style描述，必须体现相应的视觉风格

3. **技术要求**：
   - 输出完整的HTML5文档，尺寸600x800px
   - 所有CSS写在<style>标签内
   - 使用现代布局技术（Flexbox/Grid）
   - 确保在指定尺寸内美观展示

4. **输出要求**：
   - 只输出HTML代码，无任何解释文字
   - 不使用```html标记
   - 代码可直接保存为.html文件

请根据以上要求生成卡片：
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成封面页HTML"""
        
        raw_content = input_data.get("raw_content", "")
        processed_content = input_data.get("processed_content", "")
        context = input_data.get("context", {})
        
        # 使用processed_content优先，如果没有则使用raw_content
        content = processed_content or raw_content
        
        if not content:
            return {
                "error_message": "缺少封面内容数据"
            }
        
        try:
            llm = get_llm(temperature=0.7)
            
            print(f"🎨 生成封面页...")
            
            # 准备输入变量
            input_variables = {
                "RAW_INPUT_TEXT": content,
                "USER_PREFERENCES": "",  # 目前为空，以后可扩展
                "CONTEXT": self._format_context(context)
            }
            
            # 调用LLM生成封面
            prompt_template = PromptTemplate.from_template(self.cover_prompt.strip())
            chain = prompt_template | llm
            response = chain.invoke(input_variables)
            
            # 调试：保存原始输出
            raw_output = response.content
            print(f"\n=== 封面页LLM原始输出 ===")
            print(f"原始输出长度: {len(raw_output)}")
            print(f"原始输出前500字符: {raw_output[:500]}")
            print(f"原始输出后500字符: {raw_output[-500:]}")
            
            # 清理HTML
            cleaned_html = self._clean_html_response(raw_output)
            print(f"\n=== 封面页HTML处理结果 ===")
            print(f"处理后长度: {len(cleaned_html)}")
            print(f"处理后前500字符: {cleaned_html[:500]}")
            print(f"处理后后500字符: {cleaned_html[-500:]}")
            print(f"=== 封面页处理完成 ===\n")
            
            if cleaned_html:
                print(f"✅ 封面页生成成功")
                return {
                    "cover_html": cleaned_html
                }
            else:
                return {
                    "error_message": "封面页HTML生成失败"
                }
                
        except Exception as e:
            return {
                "error_message": f"封面页生成失败: {str(e)}"
            }
    
    def _format_context(self, context: dict) -> str:
        """格式化上下文信息为结构化字符串"""
        if not context:
            return ""
        
        formatted = "上下文信息：\n"
        for key, value in context.items():
            if value:  # 只包含有值的参数
                formatted += f"- {key}: {value}\n"
        
        return formatted.strip()
    
    def _clean_html_response(self, raw_response: str) -> str:
        """清理LLM返回的HTML内容"""
        
        raw_response = raw_response.strip()
        
        # 首先尝试移除Markdown代码块标记
        content = raw_response
        if content.startswith("```html"):
            content = content[len("```html"):].strip()
        elif content.startswith("```"):
            content = content[len("```"):].strip()
        
        if content.endswith("```"):
            content = content[:-len("```")].strip()
        
        # 检查HTML完整性的辅助函数
        def is_html_complete(html_content: str) -> bool:
            """检查HTML是否完整"""
            html_lower = html_content.lower()
            
            # 检查基本HTML结构
            has_html_tag = "<html" in html_lower and "</html>" in html_lower
            has_head_tag = "<head" in html_lower and "</head>" in html_lower  
            has_body_tag = "<body" in html_lower and "</body>" in html_lower
            
            # 检查CSS样式是否完整（没有未闭合的大括号）
            open_braces = html_content.count('{')
            close_braces = html_content.count('}')
            balanced_braces = open_braces == close_braces
            
            return has_html_tag and has_head_tag and has_body_tag and balanced_braces
        
        # 查找完整的HTML文档 - 支持多种格式
        html_patterns = [
            ("<!doctype html", "</html>"),
            ("<!DOCTYPE html", "</html>"),
            ("<!DOCTYPE HTML", "</html>"),
            ("<html", "</html>"),
            ("<HTML", "</HTML>")
        ]
        
        for start_marker, end_marker in html_patterns:
            start_idx = content.lower().find(start_marker.lower())
            if start_idx != -1:
                content_after_start = content[start_idx:]
                # 使用rfind查找最后一个</html>标记，确保获取完整HTML
                end_idx = content_after_start.lower().rfind(end_marker.lower())
                
                if end_idx != -1:
                    # 确保包含完整的结束标记
                    full_html = content_after_start[:end_idx + len(end_marker)]
                    
                    # 验证HTML是否完整
                    if is_html_complete(full_html):
                        return full_html
                    else:
                        # 如果不完整，尝试返回全部内容
                        print(f"警告: 检测到不完整的HTML，返回全部内容")
                        return content_after_start
                else:
                    # 找到开始标记但没有结束标记，返回从开始标记到结尾的内容
                    print(f"警告: 找到HTML开始标记但没有结束标记")
                    return content_after_start
        
        # 如果没有找到完整的HTML结构，但包含基本HTML标签，直接返回
        if any(tag in content.lower() for tag in ["<div", "<body", "<head", "<style"]):
            print(f"警告: 未找到完整HTML结构，但包含HTML标签")
            return content
        
        # 最后回退：返回原始内容
        print(f"警告: 无法识别HTML格式，返回原始内容")
        return content

def test_cover_page_generator():
    """测试封面页生成工具"""
    print("🧪 测试封面页生成工具")
    
    tool = CoverPageGeneratorTool()
    
    # 测试用例
    test_input = {
        "raw_content": "程序员小王的一天：早上迟到，代码出bug，产品改需求，加班到深夜",
        "processed_content": "程序员小王的搞笑日常：从迟到到加班的心路历程",
        "context": {
            "html_theme": "幽默故事封面",
            "visual_style": "轻松幽默，手绘风格",
            "color_scheme": {
                "primary_bg": "#FFFFFF",
                "accent_1": "#FF6B6B"
            },
            "typography": {
                "main_font": "Comic Sans MS",
                "title_size": "32px"
            }
        }
    }
    
    result = tool.process(test_input)
    
    print(f"输入内容: {test_input['processed_content']}")
    print(f"输出: {result}")
    
    if result.get("cover_html"):
        print("✅ 封面页生成测试通过!")
        print(f"HTML长度: {len(result['cover_html'])}")
    else:
        print("❌ 封面页生成测试失败!")
        print(f"错误信息: {result.get('error_message')}")

if __name__ == "__main__":
    test_cover_page_generator()