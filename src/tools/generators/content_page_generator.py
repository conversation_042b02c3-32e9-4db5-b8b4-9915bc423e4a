"""
内容页生成工具 - 专门用于生成内容页HTML
"""
from typing import Dict, Any, List
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from langchain_core.prompts import PromptTemplate

@ToolRegistry.register(ToolCategory.GENERATOR, "content_page")
class ContentPageGeneratorTool(BaseTool):
    """内容页生成工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "生成内容页HTML卡片"
        self.input_keys = ["content_segments", "context"]
        self.output_keys = ["content_htmls", "content_metadata"]
        
        # 内容页专用提示词模板
        self.content_prompt = """
你是一位专业的视觉设计师，擅长创建易读且美观的内容展示卡片。

## 任务：为以下文本内容生成一张内容卡片

### 文本内容：
{text_segment}

### 设计要求：
{CONTEXT}

## 设计指南：

1. **内容展示**：
   - 确保文本清晰易读，合理分段
   - 突出重点信息，保持良好的视觉层次
   - 根据内容长度调整排版

2. **风格应用**：
   - **严格按照设计要求中的所有配置进行设计**
   - 使用指定的color_scheme中的颜色
   - 应用指定的typography字体和尺寸
   - 实现指定的elements样式（边框、圆角、阴影等）
   - 体现visual_style中描述的视觉风格

3. **技术要求**：
   - 输出完整的HTML5文档，尺寸600x800px
   - 所有CSS写在<style>标签内
   - 使用现代布局技术保证响应式效果
   - 优化阅读体验和视觉美感

4. **输出要求**：
   - 只输出HTML代码，无任何解释文字
   - 不使用```html标记
   - 代码可直接保存为.html文件

请根据以上要求生成卡片：
"""
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成内容页HTML"""
        
        content_segments = input_data.get("content_segments", [])
        context = input_data.get("context", {})
        
        if not content_segments:
            return {
                "error_message": "缺少内容片段数据"
            }
        
        try:
            llm = get_llm(temperature=0.7)
            generated_htmls = []
            
            for i, segment in enumerate(content_segments):
                print(f"🔖 生成第 {i+1} 个内容页...")
                
                # 准备输入变量
                input_variables = {
                    "text_segment": segment,
                    "CONTEXT": self._format_context(context)
                }
                
                # 调用LLM生成内容页
                prompt_template = PromptTemplate.from_template(self.content_prompt.strip())
                chain = prompt_template | llm
                response = chain.invoke(input_variables)
                
                # 调试：保存原始输出
                raw_output = response.content
                print(f"\n=== 内容页{i+1}LLM原始输出 ===")
                print(f"原始输出长度: {len(raw_output)}")
                print(f"原始输出前500字符: {raw_output[:500]}")
                print(f"原始输出后500字符: {raw_output[-500:]}")
                
                # 清理HTML
                cleaned_html = self._clean_html_response(raw_output)
                print(f"\n=== 内容页{i+1}HTML处理结果 ===")
                print(f"处理后长度: {len(cleaned_html)}")
                print(f"处理后前500字符: {cleaned_html[:500]}")
                print(f"处理后后500字符: {cleaned_html[-500:]}")
                print(f"=== 内容页{i+1}处理完成 ===\n")
                
                if cleaned_html:
                    generated_htmls.append(cleaned_html)
                    print(f"✅ 第 {i+1} 个内容页生成成功")
                else:
                    print(f"❌ 第 {i+1} 个内容页生成失败")
            
            if not generated_htmls:
                return {
                    "error_message": "所有内容页生成失败"
                }
            
            print(f"🎉 内容页生成完成，共生成 {len(generated_htmls)} 个页面")
            
            return {
                "content_htmls": generated_htmls,
                "content_metadata": {
                    "total_pages": len(generated_htmls),
                    "generated_pages": len(generated_htmls),
                    "failed_pages": len(content_segments) - len(generated_htmls)
                }
            }
            
        except Exception as e:
            return {
                "error_message": f"内容页生成失败: {str(e)}"
            }
    
    def _format_context(self, context: dict) -> str:
        """格式化上下文信息为结构化字符串"""
        if not context:
            return ""
        
        formatted = "上下文信息：\n"
        for key, value in context.items():
            if value:  # 只包含有值的参数
                formatted += f"- {key}: {value}\n"
        
        return formatted.strip()
    
    def _clean_html_response(self, raw_response: str) -> str:
        """清理LLM返回的HTML内容"""
        
        raw_response = raw_response.strip()
        
        # 首先尝试移除Markdown代码块标记
        content = raw_response
        if content.startswith("```html"):
            content = content[len("```html"):].strip()
        elif content.startswith("```"):
            content = content[len("```"):].strip()
        
        if content.endswith("```"):
            content = content[:-len("```")].strip()
        
        # 检查HTML完整性的辅助函数
        def is_html_complete(html_content: str) -> bool:
            """检查HTML是否完整"""
            html_lower = html_content.lower()
            
            # 检查基本HTML结构
            has_html_tag = "<html" in html_lower and "</html>" in html_lower
            has_head_tag = "<head" in html_lower and "</head>" in html_lower  
            has_body_tag = "<body" in html_lower and "</body>" in html_lower
            
            # 检查CSS样式是否完整（没有未闭合的大括号）
            open_braces = html_content.count('{')
            close_braces = html_content.count('}')
            balanced_braces = open_braces == close_braces
            
            return has_html_tag and has_head_tag and has_body_tag and balanced_braces
        
        # 查找完整的HTML文档 - 支持多种格式
        html_patterns = [
            ("<!doctype html", "</html>"),
            ("<!DOCTYPE html", "</html>"),
            ("<!DOCTYPE HTML", "</html>"),
            ("<html", "</html>"),
            ("<HTML", "</HTML>")
        ]
        
        for start_marker, end_marker in html_patterns:
            start_idx = content.lower().find(start_marker.lower())
            if start_idx != -1:
                content_after_start = content[start_idx:]
                # 使用rfind查找最后一个</html>标记，确保获取完整HTML
                end_idx = content_after_start.lower().rfind(end_marker.lower())
                
                if end_idx != -1:
                    # 确保包含完整的结束标记
                    full_html = content_after_start[:end_idx + len(end_marker)]
                    
                    # 验证HTML是否完整
                    if is_html_complete(full_html):
                        return full_html
                    else:
                        # 如果不完整，尝试返回全部内容
                        print(f"警告: 检测到不完整的HTML，返回全部内容")
                        return content_after_start
                else:
                    # 找到开始标记但没有结束标记，返回从开始标记到结尾的内容
                    print(f"警告: 找到HTML开始标记但没有结束标记")
                    return content_after_start
        
        # 如果没有找到完整的HTML结构，但包含基本HTML标签，直接返回
        if any(tag in content.lower() for tag in ["<div", "<body", "<head", "<style"]):
            print(f"警告: 未找到完整HTML结构，但包含HTML标签")
            return content
        
        # 最后回退：返回原始内容
        print(f"警告: 无法识别HTML格式，返回原始内容")
        return content

def test_content_page_generator():
    """测试内容页生成工具"""
    print("🧪 测试内容页生成工具")
    
    tool = ContentPageGeneratorTool()
    
    # 测试用例
    test_input = {
        "content_segments": [
            "程序员小王早上8点的闹钟响了三遍才起床，匆忙洗漱后发现又要迟到了。",
            "到公司后，打开电脑第一件事就是查看昨天的代码，结果发现测试服务器挂了。",
            "中午吃饭时，产品经理又提出了新的需求变更，要求今天下午就要完成。"
        ],
        "context": {
            "html_theme": "幽默故事内容页",
            "visual_style": "轻松幽默，手绘风格",
            "color_scheme": {
                "primary_bg": "#FFFFFF",
                "accent_1": "#FF6B6B"
            },
            "typography": {
                "main_font": "Comic Sans MS",
                "content_size": "18px"
            }
        }
    }
    
    result = tool.process(test_input)
    
    print(f"输入片段数: {len(test_input['content_segments'])}")
    print(f"输出: {result}")
    
    if result.get("content_htmls"):
        print("✅ 内容页生成测试通过!")
        print(f"生成页面数: {len(result['content_htmls'])}")
        print(f"元数据: {result['content_metadata']}")
    else:
        print("❌ 内容页生成测试失败!")
        print(f"错误信息: {result.get('error_message')}")

if __name__ == "__main__":
    test_content_page_generator()