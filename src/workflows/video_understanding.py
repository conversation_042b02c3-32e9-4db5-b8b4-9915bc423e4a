"""
视频理解工作流 V2 - 支持大文件预处理和分段分析
"""
import os
import sys
from typing import Dict, Any, TypedDict, Optional, List
from langgraph.graph import StateGraph, END
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入工具
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../..'))

from tools.video_processors.video_input import VideoInputTool
from tools.video_processors.video_script_analyzer import VideoScriptAnalyzer
from tools.video_processors.script_validator import ScriptValidatorTool
from tools.video_processors.segment_merger import SegmentMerger
from tools.output.video_script_output import VideoScriptOutputTool

class VideoUnderstandingStateV2(TypedDict):
    """视频理解工作流状态 V2 - 支持分段处理"""
    # 输入
    video_path: Optional[str]
    video_url: Optional[str]
    user_request: Optional[str]
    
    # 视频输入处理结果
    video_file_path: Optional[str]
    video_metadata: Optional[Dict[str, Any]]
    is_local_file: Optional[bool]
    processed_videos: Optional[List[Dict[str, Any]]]
    processing_info: Optional[Dict[str, Any]]
    total_segments: Optional[int]
    
    # 分段分析结果
    segment_results: Optional[List[Dict[str, Any]]]
    
    # 合并后的脚本
    video_script_json: Optional[Dict[str, Any]]
    merge_summary: Optional[Dict[str, Any]]
    
    # 验证结果
    validated_script: Optional[Dict[str, Any]]
    validation_report: Optional[Dict[str, Any]]
    
    # 输出结果
    output_file_path: Optional[str]
    script_summary: Optional[Dict[str, Any]]
    success_message: Optional[str]
    
    # 流程控制
    current_step: Optional[str]
    error_message: Optional[str]

# 节点函数定义
def video_input_node_v2(state: VideoUnderstandingStateV2) -> Dict[str, Any]:
    """视频输入处理节点 V2 - 支持预处理"""
    print("🎬 开始处理视频输入（支持大文件预处理）...")
    
    tool = VideoInputTool()
    result = tool.execute(state)
    
    if result.get("error_message"):
        print(f"❌ 视频输入处理失败: {result['error_message']}")
        return {
            "error_message": result["error_message"],
            "current_step": "video_input_failed"
        }
    
    processed_videos = result.get("processed_videos", [])
    processing_info = result.get("processing_info", {})
    total_segments = result.get("total_segments", 1)
    
    print(f"✅ 视频输入处理成功")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"📁 生成文件数: {len(processed_videos)}")
    
    if total_segments > 1:
        print(f"✂️ 视频已分段处理，共 {total_segments} 个片段")
    
    return {
        "video_file_path": result.get("video_file_path"),
        "video_metadata": result.get("video_metadata"),
        "is_local_file": result.get("is_local_file"),
        "processed_videos": processed_videos,
        "processing_info": processing_info,
        "total_segments": total_segments,
        "current_step": "video_input_completed"
    }

def segment_analysis_node(state: VideoUnderstandingStateV2) -> Dict[str, Any]:
    """分段分析节点 - 并发分析视频片段"""
    processed_videos = state.get("processed_videos", [])
    total_segments = state.get("total_segments", 1)

    print(f"🤖 开始视频分析，共 {total_segments} 个片段...")

    # 使用并发分析工具
    from tools.video_processors.concurrent_analyzer import ConcurrentVideoAnalyzer

    concurrent_analyzer = ConcurrentVideoAnalyzer()

    # 获取最优并发数
    max_workers = concurrent_analyzer.get_optimal_workers(total_segments)

    result = concurrent_analyzer.execute({
        "processed_videos": processed_videos,
        "user_request": state.get("user_request", ""),
        "max_workers": max_workers
    })

    if result.get("error_message"):
        print(f"❌ 并发分析失败: {result['error_message']}")
        return {
            "error_message": result["error_message"],
            "current_step": "segment_analysis_failed"
        }

    segment_results = result.get("segment_results", [])
    processing_summary = result.get("processing_summary", {})
    failed_segments = result.get("failed_segments", [])

    success_rate = processing_summary.get("success_rate", 0)

    if success_rate < 0.5:  # 成功率低于50%
        print(f"❌ 分析成功率过低: {success_rate:.1%}")
        return {
            "error_message": f"分析成功率过低: {success_rate:.1%}，请检查视频质量或网络连接",
            "current_step": "segment_analysis_failed"
        }

    print(f"✅ 并发分析完成，成功率: {success_rate:.1%}")

    if failed_segments:
        print(f"⚠️ {len(failed_segments)} 个分段分析失败，将使用成功的分段继续处理")

    return {
        "segment_results": segment_results,
        "processing_summary": processing_summary,
        "current_step": "segment_analysis_completed"
    }

def result_merge_node(state: VideoUnderstandingStateV2) -> Dict[str, Any]:
    """结果合并节点 - 合并分段分析结果"""
    segment_results = state.get("segment_results", [])
    processing_info = state.get("processing_info", {})
    total_segments = state.get("total_segments", 1)
    
    if total_segments == 1:
        # 单个片段，直接使用结果
        if segment_results:
            script = segment_results[0].get("video_script_json", {})
            return {
                "video_script_json": script,
                "merge_summary": {
                    "total_segments": 1,
                    "merge_method": "single_segment"
                },
                "current_step": "merge_completed"
            }
        else:
            return {
                "error_message": "没有分析结果可合并",
                "current_step": "merge_failed"
            }
    
    print("🔄 开始合并分段分析结果...")
    
    merger = SegmentMerger()
    result = merger.execute({
        "segment_results": segment_results,
        "processing_info": processing_info
    })
    
    if result.get("error_message"):
        print(f"❌ 结果合并失败: {result['error_message']}")
        return {
            "error_message": result["error_message"],
            "current_step": "merge_failed"
        }
    
    merge_summary = result.get("merge_summary", {})
    print(f"✅ 结果合并完成")
    print(f"📊 合并后镜头数: {merge_summary.get('total_shots', 0)}")
    print(f"⏱️ 总时长: {merge_summary.get('total_duration', 0)}秒")
    
    return {
        "video_script_json": result.get("merged_script"),
        "merge_summary": merge_summary,
        "current_step": "merge_completed"
    }

def script_validation_node_v2(state: VideoUnderstandingStateV2) -> Dict[str, Any]:
    """脚本验证节点 V2"""
    print("🔍 开始验证合并后的脚本格式...")
    
    tool = ScriptValidatorTool()
    result = tool.execute(state)
    
    if result.get("error_message"):
        print(f"❌ 脚本验证失败: {result['error_message']}")
        return {
            "error_message": result["error_message"],
            "current_step": "validation_failed"
        }
    
    fixes_count = len(result.get("fixes_applied", []))
    if fixes_count > 0:
        print(f"🔧 脚本验证完成，应用了{fixes_count}个修复")
    else:
        print("✅ 脚本格式验证通过")
    
    return {
        "validated_script": result.get("validated_script"),
        "validation_report": result.get("validation_report"),
        "current_step": "validation_completed"
    }

def output_generation_node_v2(state: VideoUnderstandingStateV2) -> Dict[str, Any]:
    """输出生成节点 V2"""
    print("💾 开始生成输出文件...")
    
    tool = VideoScriptOutputTool()
    
    # 准备输出数据，包含处理信息
    output_input = {
        "validated_script": state.get("validated_script"),
        "video_metadata": state.get("video_metadata"),
        "validation_report": state.get("validation_report")
    }
    
    # 添加处理信息到元数据
    if state.get("processing_info"):
        output_input["processing_metadata"] = {
            "processing_info": state.get("processing_info"),
            "total_segments": state.get("total_segments"),
            "merge_summary": state.get("merge_summary")
        }
    
    result = tool.execute(output_input)
    
    if result.get("error_message"):
        print(f"❌ 输出生成失败: {result['error_message']}")
        return {
            "error_message": result["error_message"],
            "current_step": "output_failed"
        }
    
    print("✅ 输出文件生成完成")
    return {
        "output_file_path": result.get("output_file_path"),
        "script_summary": result.get("script_summary"),
        "success_message": result.get("success_message"),
        "current_step": "workflow_completed"
    }

# 条件判断函数
def should_continue_after_input_v2(state: VideoUnderstandingStateV2) -> str:
    """判断视频输入后是否继续"""
    if state.get("error_message"):
        return "end"
    return "segment_analysis"

def should_continue_after_analysis(state: VideoUnderstandingStateV2) -> str:
    """判断分析后是否继续"""
    if state.get("error_message"):
        return "end"
    return "merge"

def should_continue_after_merge(state: VideoUnderstandingStateV2) -> str:
    """判断合并后是否继续"""
    if state.get("error_message"):
        return "end"
    return "validation"

def should_continue_after_validation_v2(state: VideoUnderstandingStateV2) -> str:
    """判断验证后是否继续"""
    if state.get("error_message"):
        return "end"
    return "output"

def build_video_understanding_workflow_v2():
    """构建视频理解工作流 V2"""
    
    # 创建状态图
    workflow = StateGraph(VideoUnderstandingStateV2)
    
    # 添加节点
    workflow.add_node("video_input", video_input_node_v2)
    workflow.add_node("segment_analysis", segment_analysis_node)
    workflow.add_node("result_merge", result_merge_node)
    workflow.add_node("script_validation", script_validation_node_v2)
    workflow.add_node("output_generation", output_generation_node_v2)
    
    # 设置入口点
    workflow.set_entry_point("video_input")
    
    # 添加条件边
    workflow.add_conditional_edges(
        "video_input",
        should_continue_after_input_v2,
        {
            "segment_analysis": "segment_analysis",
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "segment_analysis",
        should_continue_after_analysis,
        {
            "merge": "result_merge",
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "result_merge",
        should_continue_after_merge,
        {
            "validation": "script_validation",
            "end": END
        }
    )
    
    workflow.add_conditional_edges(
        "script_validation",
        should_continue_after_validation_v2,
        {
            "output": "output_generation",
            "end": END
        }
    )
    
    # 输出节点直接结束
    workflow.add_edge("output_generation", END)
    
    # 编译工作流
    app = workflow.compile()
    print("🚀 视频理解工作流 V2 已编译完成（支持大文件预处理）")
    
    return app

def run_video_understanding_v2(video_path: str = None, video_url: str = None, user_request: str = "请分析这个视频并生成详细的脚本"):
    """运行视频理解工作流 V2 - 支持大文件预处理"""

    if not video_path and not video_url:
        print("❌ 请提供视频文件路径或URL")
        return None

    # 构建工作流
    app = build_video_understanding_workflow_v2()

    # 初始化状态
    initial_state = VideoUnderstandingStateV2(
        video_path=video_path,
        video_url=video_url,
        user_request=user_request,
        video_file_path=None,
        video_metadata=None,
        is_local_file=None,
        processed_videos=None,
        processing_info=None,
        total_segments=None,
        segment_results=None,
        video_script_json=None,
        merge_summary=None,
        validated_script=None,
        validation_report=None,
        output_file_path=None,
        script_summary=None,
        success_message=None,
        current_step=None,
        error_message=None
    )

    print("🎬 开始视频理解工作流 V2...")
    print(f"📹 输入: {video_path or video_url}")
    print(f"📝 需求: {user_request}")
    print("=" * 50)

    try:
        # 运行工作流
        final_state = app.invoke(initial_state)

        print("=" * 50)

        if final_state.get("error_message"):
            print(f"❌ 工作流执行失败: {final_state['error_message']}")
            return final_state

        # 显示处理摘要
        processing_info = final_state.get("processing_info", {})
        merge_summary = final_state.get("merge_summary", {})

        print("📊 处理摘要:")
        print(f"  🔧 处理方法: {processing_info.get('method', 'unknown')}")

        if processing_info.get("method") == "segmentation":
            print(f"  ✂️ 分段数: {merge_summary.get('total_segments', 0)}")
            print(f"  🎬 总镜头数: {merge_summary.get('total_shots', 0)}")
            print(f"  ⏱️ 总时长: {merge_summary.get('total_duration', 0)}秒")
        elif processing_info.get("method") == "compression":
            print(f"  🗜️ 压缩比: {processing_info.get('compression_ratio', 0):.2f}")
            print(f"  📉 原始大小: {processing_info.get('original_size_mb', 0):.1f}MB")
            print(f"  📉 最终大小: {processing_info.get('final_size_mb', 0):.1f}MB")

        # 显示成功消息
        if final_state.get("success_message"):
            print("\n" + final_state["success_message"])

        return final_state

    except Exception as e:
        print(f"❌ 工作流执行出错: {str(e)}")
        return {"error_message": str(e)}

if __name__ == "__main__":
    # 测试工作流
    import argparse

    parser = argparse.ArgumentParser(description="视频理解工作流 V2 测试（支持大文件）")
    parser.add_argument("--video_path", type=str, help="本地视频文件路径")
    parser.add_argument("--video_url", type=str, help="视频URL")
    parser.add_argument("--request", type=str, default="请分析这个视频并生成详细的脚本", help="分析需求")

    args = parser.parse_args()

    if not args.video_path and not args.video_url:
        print("请提供 --video_path 或 --video_url 参数")
        print("示例:")
        print("  python video_understanding_workflow_v2.py --video_url 'https://example.com/video.mp4'")
        print("  python video_understanding_workflow_v2.py --video_path '/path/to/large_video.mp4'")
    else:
        result = run_video_understanding_v2(
            video_path=args.video_path,
            video_url=args.video_url,
            user_request=args.request
        )
