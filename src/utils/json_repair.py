"""
JSON修复工具包 - 处理API返回的损坏JSON
"""
import json
import re
from typing import Dict, Any, Optional, Tuple

class JSONRepairToolkit:
    """JSON修复工具包"""
    
    def __init__(self):
        self.repair_strategies = [
            self._fix_missing_quotes,
            self._fix_trailing_commas,
            self._fix_unescaped_quotes,
            self._fix_incomplete_json,
            self._extract_valid_json_blocks,
            self._fallback_extraction
        ]
    
    def repair_json(self, broken_json: str) -> Tuple[Optional[Dict], str]:
        """
        尝试修复损坏的JSON
        返回: (修复后的dict, 修复方法)
        """
        
        print("🔧 开始JSON修复...")
        
        # 首先尝试直接解析
        try:
            result = json.loads(broken_json)
            print("✅ JSON格式正确，无需修复")
            return result, "no_repair_needed"
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
        
        # 逐个尝试修复策略
        for i, strategy in enumerate(self.repair_strategies):
            try:
                print(f"🔄 尝试修复策略 {i+1}: {strategy.__name__}")
                repaired_json = strategy(broken_json)
                
                if repaired_json:
                    result = json.loads(repaired_json)
                    print(f"✅ 修复成功，使用策略: {strategy.__name__}")
                    return result, strategy.__name__
                    
            except Exception as e:
                print(f"⚠️ 策略 {i+1} 失败: {str(e)}")
                continue
        
        print("❌ 所有修复策略都失败了")
        return None, "all_strategies_failed"
    
    def _fix_missing_quotes(self, json_str: str) -> Optional[str]:
        """修复缺失的引号"""
        # 修复对象键缺少引号的问题
        # 例如: {name: "value"} -> {"name": "value"}
        pattern = r'(\{|\,)\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:'
        fixed = re.sub(pattern, r'\1"\2":', json_str)
        return fixed
    
    def _fix_trailing_commas(self, json_str: str) -> Optional[str]:
        """修复多余的逗号"""
        # 移除对象和数组末尾的逗号
        # 例如: {"a": 1,} -> {"a": 1}
        fixed = re.sub(r',\s*}', '}', json_str)
        fixed = re.sub(r',\s*]', ']', fixed)
        return fixed
    
    def _fix_unescaped_quotes(self, json_str: str) -> Optional[str]:
        """修复未转义的引号"""
        # 在字符串值中转义引号
        # 这个比较复杂，简单处理一些常见情况
        
        # 修复描述字段中的引号
        def escape_quotes_in_strings(match):
            content = match.group(1)
            # 转义内部的引号
            escaped = content.replace('"', '\\"')
            return f'"{escaped}"'
        
        # 匹配字符串值并转义内部引号
        pattern = r'"([^"]*"[^"]*)"'
        fixed = re.sub(pattern, escape_quotes_in_strings, json_str)
        return fixed
    
    def _fix_incomplete_json(self, json_str: str) -> Optional[str]:
        """修复不完整的JSON"""
        # 如果JSON被截断，尝试补全
        
        # 计算括号平衡
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')
        
        fixed = json_str
        
        # 补全缺失的括号
        if open_braces > close_braces:
            fixed += '}' * (open_braces - close_braces)
        
        if open_brackets > close_brackets:
            fixed += ']' * (open_brackets - close_brackets)
        
        # 如果最后一个字符是逗号，移除它
        fixed = fixed.rstrip(',')
        
        return fixed
    
    def _extract_valid_json_blocks(self, json_str: str) -> Optional[str]:
        """提取有效的JSON块"""
        # 尝试找到完整的JSON对象
        
        # 查找第一个完整的对象
        brace_count = 0
        start_pos = json_str.find('{')
        
        if start_pos == -1:
            return None
        
        for i, char in enumerate(json_str[start_pos:], start_pos):
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                
            if brace_count == 0:
                # 找到完整的对象
                return json_str[start_pos:i+1]
        
        return None
    
    def _fallback_extraction(self, json_str: str) -> Optional[str]:
        """最后的备用提取方法"""
        # 尝试提取关键信息构建最小可用JSON
        
        try:
            # 提取视频标题
            title_match = re.search(r'"videoTitle":\s*"([^"]*)"', json_str)
            title = title_match.group(1) if title_match else "解析失败"
            
            # 提取主题
            theme_match = re.search(r'"videoTheme":\s*"([^"]*)"', json_str)
            theme = theme_match.group(1) if theme_match else "未知主题"
            
            # 构建最小JSON
            minimal_json = {
                "videoMetaDataDTO": {
                    "videoTitle": title,
                    "videoTheme": theme,
                    "videoCover": "",
                    "narrativeStructure": "",
                    "best3sec": "",
                    "visualStyle": "",
                    "bgmStyle": "",
                    "characterSheet": []
                },
                "shots": []
            }
            
            return json.dumps(minimal_json, ensure_ascii=False)
            
        except Exception:
            return None

def analyze_json_failure_patterns():
    """分析JSON失败的常见模式"""
    
    print("📊 JSON解析失败常见原因分析:")
    print("=" * 50)
    
    patterns = [
        {
            "cause": "🔤 中文字符编码问题",
            "example": "包含特殊中文字符导致编码错误",
            "solution": "使用UTF-8编码，转义特殊字符"
        },
        {
            "cause": "📝 描述文本中的引号",
            "example": '"description": "他说"你好"然后离开"',
            "solution": "转义内部引号或使用单引号"
        },
        {
            "cause": "✂️ 响应被截断",
            "example": "API响应超长被截断，JSON不完整",
            "solution": "限制输出长度，分段处理"
        },
        {
            "cause": "🔧 格式不规范",
            "example": "缺少引号、多余逗号等格式问题",
            "solution": "使用JSON修复工具"
        },
        {
            "cause": "🤖 模型输出不稳定",
            "example": "AI模型偶尔输出格式错误",
            "solution": "重试机制，多次尝试"
        }
    ]
    
    for i, pattern in enumerate(patterns, 1):
        print(f"\n{i}. {pattern['cause']}")
        print(f"   📋 示例: {pattern['example']}")
        print(f"   💡 解决: {pattern['solution']}")

def suggest_prevention_strategies():
    """建议预防策略"""
    
    print("\n🛡️ JSON错误预防策略:")
    print("=" * 50)
    
    strategies = [
        "📏 限制输出长度 - 避免响应被截断",
        "🎯 简化输出格式 - 减少复杂嵌套结构", 
        "🔄 重试机制 - 失败时自动重试2-3次",
        "✅ 格式验证 - API调用前验证提示词",
        "🔧 后处理修复 - 自动修复常见JSON错误",
        "📊 分段处理 - 大内容分段生成再合并"
    ]
    
    for strategy in strategies:
        print(f"  {strategy}")

def test_json_repair():
    """测试JSON修复功能"""
    
    print("\n🧪 测试JSON修复功能...")
    
    # 模拟常见的损坏JSON
    broken_jsons = [
        # 缺少引号
        '{videoTitle: "测试视频", theme: "测试主题"}',
        
        # 多余逗号
        '{"videoTitle": "测试视频", "theme": "测试主题",}',
        
        # 不完整JSON
        '{"videoTitle": "测试视频", "theme": "测试主题"',
        
        # 内部引号未转义
        '{"description": "他说"你好"然后离开"}',
    ]
    
    toolkit = JSONRepairToolkit()
    
    for i, broken_json in enumerate(broken_jsons, 1):
        print(f"\n📋 测试案例 {i}:")
        print(f"原始: {broken_json}")
        
        repaired, method = toolkit.repair_json(broken_json)
        
        if repaired:
            print(f"✅ 修复成功: {method}")
            print(f"结果: {json.dumps(repaired, ensure_ascii=False)}")
        else:
            print(f"❌ 修复失败: {method}")

if __name__ == "__main__":
    print("🔧 JSON修复工具包")
    print("=" * 60)
    
    # 分析失败模式
    analyze_json_failure_patterns()
    
    # 建议预防策略
    suggest_prevention_strategies()
    
    # 测试修复功能
    test_json_repair()
    
    print("\n💡 总结:")
    print("JSON解析失败确实很难完全避免，但可以通过:")
    print("1. 🛡️ 预防策略 - 减少出错概率")
    print("2. 🔧 修复工具 - 自动修复常见错误") 
    print("3. 🔄 重试机制 - 多次尝试提高成功率")
    print("4. 📊 降级处理 - 部分成功也能继续")
    print("来大幅提高成功率和用户体验！")
