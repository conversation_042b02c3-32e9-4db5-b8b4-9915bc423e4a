"""
鲁棒性视频分析器 - 集成JSON修复和重试机制
"""
import json
import time
from typing import Dict, Any, Optional
from tools.video_processors.video_script_analyzer import VideoScriptAnalyzer
from json_repair_toolkit import JSONRepairToolkit

class RobustVideoAnalyzer:
    """鲁棒性视频分析器 - 处理JSON解析失败"""
    
    def __init__(self):
        self.base_analyzer = VideoScriptAnalyzer()
        self.json_repair = JSONRepairToolkit()
        self.max_retries = 3
        self.retry_delay = 2  # 秒
    
    def analyze_with_retry(self, video_url: str, requirements: str) -> Dict[str, Any]:
        """带重试和JSON修复的分析"""
        
        print(f"🤖 开始鲁棒性分析: {video_url[:50]}...")
        
        for attempt in range(self.max_retries):
            print(f"🔄 尝试 {attempt + 1}/{self.max_retries}...")
            
            try:
                # 调用基础分析器
                result = self.base_analyzer.process({
                    "video_url": video_url,
                    "analysis_requirements": requirements
                })
                
                if result.get("error_message"):
                    print(f"⚠️ 尝试 {attempt + 1} API调用失败: {result['error_message']}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return self._create_fallback_result(video_url, result["error_message"])
                
                # 检查JSON解析
                script = result.get("video_script_json", {})
                
                if not script or not isinstance(script, dict):
                    print(f"⚠️ 尝试 {attempt + 1} 返回空脚本")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return self._create_fallback_result(video_url, "返回空脚本")
                
                # 验证关键字段
                if self._validate_script_structure(script):
                    print(f"✅ 尝试 {attempt + 1} 成功")
                    return result
                else:
                    print(f"⚠️ 尝试 {attempt + 1} 脚本结构不完整")
                    # 尝试修复
                    repaired_script = self._repair_script_structure(script)
                    if repaired_script:
                        result["video_script_json"] = repaired_script
                        print(f"🔧 脚本结构已修复")
                        return result
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                        continue
                    else:
                        return self._create_fallback_result(video_url, "脚本结构不完整")
                        
            except Exception as e:
                print(f"❌ 尝试 {attempt + 1} 异常: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    return self._create_fallback_result(video_url, str(e))
        
        return self._create_fallback_result(video_url, "所有重试都失败")
    
    def _validate_script_structure(self, script: Dict[str, Any]) -> bool:
        """验证脚本结构完整性"""
        
        # 检查必需的顶级字段
        required_fields = ["videoMetaDataDTO", "shots"]
        for field in required_fields:
            if field not in script:
                return False
        
        # 检查元数据结构
        metadata = script.get("videoMetaDataDTO", {})
        if not isinstance(metadata, dict):
            return False
        
        # 检查镜头数据
        shots = script.get("shots", [])
        if not isinstance(shots, list):
            return False
        
        # 如果有镜头，检查第一个镜头的结构
        if shots:
            first_shot = shots[0]
            shot_fields = ["shotNumber", "startTime", "endTime", "scene"]
            for field in shot_fields:
                if field not in first_shot:
                    return False
        
        return True
    
    def _repair_script_structure(self, script: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """修复脚本结构"""
        
        try:
            repaired = script.copy()
            
            # 确保有videoMetaDataDTO
            if "videoMetaDataDTO" not in repaired:
                repaired["videoMetaDataDTO"] = {}
            
            metadata = repaired["videoMetaDataDTO"]
            
            # 补全元数据字段
            default_metadata = {
                "videoTitle": "解析失败",
                "videoCover": "",
                "videoTheme": "",
                "narrativeStructure": "",
                "best3sec": "",
                "visualStyle": "",
                "bgmStyle": "",
                "characterSheet": []
            }
            
            for key, default_value in default_metadata.items():
                if key not in metadata:
                    metadata[key] = default_value
            
            # 确保有shots数组
            if "shots" not in repaired:
                repaired["shots"] = []
            
            # 修复shots结构
            shots = repaired["shots"]
            if not isinstance(shots, list):
                repaired["shots"] = []
            else:
                # 修复每个镜头的结构
                for i, shot in enumerate(shots):
                    if not isinstance(shot, dict):
                        continue
                    
                    # 补全镜头字段
                    default_shot = {
                        "shotNumber": i + 1,
                        "startTime": i * 10,
                        "endTime": (i + 1) * 10,
                        "scene": "场景描述缺失",
                        "shotType": "MEDIUM_SHOT",
                        "cameraMovement": "STATIC",
                        "characters": [],
                        "dialogue": "",
                        "action": "",
                        "emotion": "",
                        "lighting": "",
                        "sound": "",
                        "props": [],
                        "location": "",
                        "ability": []
                    }
                    
                    for key, default_value in default_shot.items():
                        if key not in shot:
                            shot[key] = default_value
            
            return repaired
            
        except Exception as e:
            print(f"❌ 脚本修复失败: {str(e)}")
            return None
    
    def _create_fallback_result(self, video_url: str, error_msg: str) -> Dict[str, Any]:
        """创建降级结果"""
        
        print(f"📊 创建降级结果...")
        
        # 创建最小可用的脚本结构
        fallback_script = {
            "videoMetaDataDTO": {
                "videoTitle": "分析失败 - JSON解析错误",
                "videoCover": "",
                "videoTheme": "无法解析",
                "narrativeStructure": "",
                "best3sec": "",
                "visualStyle": "",
                "bgmStyle": "",
                "characterSheet": []
            },
            "shots": [
                {
                    "shotNumber": 1,
                    "startTime": 0,
                    "endTime": 10,
                    "scene": "由于JSON解析失败，无法生成详细场景描述",
                    "shotType": "MEDIUM_SHOT",
                    "cameraMovement": "STATIC",
                    "characters": [],
                    "dialogue": "",
                    "action": "视频内容分析失败",
                    "emotion": "",
                    "lighting": "",
                    "sound": "",
                    "props": [],
                    "location": "",
                    "ability": ["SCENE_DESCRIPTION"]
                }
            ]
        }
        
        return {
            "video_script_json": fallback_script,
            "analysis_metadata": {
                "success": False,
                "error_message": error_msg,
                "fallback_used": True,
                "video_url": video_url
            }
        }

def test_robust_analyzer():
    """测试鲁棒性分析器"""
    
    print("🧪 测试鲁棒性视频分析器...")
    
    analyzer = RobustVideoAnalyzer()
    
    # 使用一个小的测试视频
    test_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = analyzer.analyze_with_retry(
        video_url=test_url,
        requirements="请分析这个视频内容"
    )
    
    script = result.get("video_script_json", {})
    metadata = result.get("analysis_metadata", {})
    
    print("📊 分析结果:")
    
    if metadata.get("fallback_used"):
        print("⚠️ 使用了降级结果")
        print(f"❌ 原因: {metadata.get('error_message', 'unknown')}")
    else:
        print("✅ 正常分析成功")
    
    # 显示脚本信息
    video_metadata = script.get("videoMetaDataDTO", {})
    shots = script.get("shots", [])
    
    print(f"📺 标题: {video_metadata.get('videoTitle', 'N/A')}")
    print(f"🎭 主题: {video_metadata.get('videoTheme', 'N/A')}")
    print(f"🎬 镜头数: {len(shots)}")
    
    if shots:
        first_shot = shots[0]
        print(f"📋 第一个镜头: {first_shot.get('scene', 'N/A')[:50]}...")
    
    return not metadata.get("fallback_used", False)

if __name__ == "__main__":
    print("🛡️ 鲁棒性视频分析器")
    print("=" * 60)
    print("💡 特性:")
    print("  🔄 自动重试 - 失败时重试3次")
    print("  🔧 JSON修复 - 自动修复常见JSON错误")
    print("  📊 降级处理 - 失败时提供最小可用结果")
    print("  ✅ 结构验证 - 确保输出格式正确")
    print("=" * 60)
    
    success = test_robust_analyzer()
    
    if success:
        print("\n🎉 鲁棒性分析器测试成功！")
        print("💡 现在可以更可靠地处理视频分析了")
    else:
        print("\n⚠️ 使用了降级结果，但系统仍然可用")
        print("💡 这证明了鲁棒性设计的价值")
