"""
智能规划器 - 分析用户意图并生成执行计划
"""
from typing import Dict, List, Any, Optional
from llm_services import get_llm

class IntelligentPlanner:
    """智能规划器，负责理解用户意图和生成执行计划"""
    
    def __init__(self):
        self.llm = get_llm(temperature=0.3)  # 规划需要较低的随机性
    
    def analyze_intent(self, user_request: str) -> Dict[str, Any]:
        """分析用户意图"""
        
        analyze_prompt = f"""
        请分析以下用户请求，提取关键信息：

        用户请求："{user_request}"

        请以JSON格式返回分析结果，包含以下字段：
        {{
            "content_type": "内容类型（如：新闻、小说、产品介绍等）",
            "style_requirement": "风格需求（如：小红书风格、meme风格、商务风格等）",
            "output_format": "输出格式（如：图片、PDF、HTML等）",
            "special_requirements": ["特殊要求列表"],
            "keywords": ["关键词列表"]
        }}
        """
        
        try:
            response = self.llm.invoke(analyze_prompt)
            # 这里需要解析LLM返回的JSON，暂时先返回基础结构
            return {
                "content_type": "unknown",
                "style_requirement": "default",
                "output_format": "image",
                "special_requirements": [],
                "keywords": [],
                "raw_analysis": response.content
            }
        except Exception as e:
            print(f"意图分析失败: {e}")
            return {
                "content_type": "unknown",
                "style_requirement": "default", 
                "output_format": "image",
                "special_requirements": [],
                "keywords": [],
                "error": str(e)
            }
    
    def generate_execution_plan(self, intent: Dict[str, Any]) -> Dict[str, Any]:
        """根据意图生成执行计划"""
        
        # 基础规则映射
        plan_templates = {
            "小红书风格": [
                "input.text_input",
                "style_converter.xiaohongshu",
                "content_processor.segmenter",
                "generator.html",
                "output.html2image"
            ],
            "csv数据源": [
                "input.csv_reader",
                "style_converter.xiaohongshu",
                "content_processor.segmenter",
                "generator.html",
                "output.html2image"
            ],
            "meme风格": [
                "input.text_input", 
                "analyzer.content",
                "style_converter.meme",
                "generator.line_art",
                "output.image"
            ],
            "default": [
                "input.text_input",
                "output.image"
            ]
        }
        
        style = intent.get("style_requirement", "default")
        tools = plan_templates.get(style, plan_templates["default"])
        
        return {
            "selected_tools": tools,
            "execution_mode": "sequential",  # sequential | parallel
            "estimated_steps": len(tools),
            "fallback_plan": plan_templates["default"]
        }