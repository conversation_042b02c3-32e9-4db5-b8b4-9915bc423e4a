"""
风格配置管理 - 详细的风格描述和视觉指导
"""
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class StyleConfig:
    """风格配置类"""
    name: str
    description: str
    visual_style: str
    layout_guide: str
    color_scheme: Dict[str, str]
    typography: Dict[str, str]
    elements: Dict[str, str]

class StyleConfigManager:
    """风格配置管理器"""
    
    def __init__(self):
        self.styles = {
            # Meme相关风格
            "humor": StyleConfig(
                name="极简手绘幽默风格",
                description="极简手绘涂鸦风格，白底为主搭配趣味小元素，专为轻松内容设计，简约而不失趣味",
                visual_style="""
• 极简手绘：干净的白底搭配少量手绘线条，保持整体清爽
• 幽默点缀：少量"！？"符号、简笔小表情等极简趣味元素
• 装饰元素：极简的圆圈、星星等小图案点缀，不喧宾夺主
• 色彩搭配：主色为白底，搭配1-2种明亮但不刺眼的点缀色
• 字体处理：部分文字轻微手绘效果，重点词用极简下划线
• 边框设计：极细的手绘边框，像是铅笔轻轻勾勒""",
                layout_guide="保持大量留白，图片和文字周围添加极简手绘标注，整体像是素描本上的简约涂鸦",
                color_scheme={
                    "primary_bg": "#FFFFFF",
                    "secondary_bg": "#FAFAFA", 
                    "text_primary": "#333333",
                    "text_secondary": "#666666",
                    "accent_1": "#FF6B6B",
                    "accent_2": "#4ECDC4",
                    "border": "#E0E0E0"
                },
                typography={
                    "main_font": "Comic Sans MS, Kalam, cursive",
                    "title_size": "24px",
                    "content_size": "16px",
                    "annotation_size": "12px",
                    "line_height": "1.5"
                },
                elements={
                    "border_style": "2px dashed #FF6B6B",
                    "border_radius": "15px",
                    "padding": "20px",
                    "margin": "10px",
                    "shadow": "0 2px 8px rgba(0,0,0,0.1)"
                }
            ),
            
            "meme_classic": StyleConfig(
                name="经典表情包风格",
                description="传统meme表情包风格，黑色粗体字配白底，简单直接有冲击力",
                visual_style="""
• 经典配色：纯白背景配黑色粗体文字，高对比度
• 字体特征：粗黑体字体，字号较大，有冲击力
• 布局特点：文字通常在顶部和底部，图片居中
• 装饰元素：几乎无装饰，保持简洁直接
• 边框设计：简单的黑色边框或无边框
• 表情元素：可加入简单的emoji或符号强调""",
                layout_guide="上下文字布局，图片居中，整体布局对称，文字简短有力",
                color_scheme={
                    "primary_bg": "#FFFFFF",
                    "text_primary": "#000000",
                    "text_secondary": "#333333",
                    "border": "#000000"
                },
                typography={
                    "main_font": "Impact, Arial Black, sans-serif",
                    "title_size": "28px",
                    "content_size": "20px",
                    "weight": "bold",
                    "line_height": "1.2"
                },
                elements={
                    "border_style": "3px solid #000000",
                    "border_radius": "0px",
                    "padding": "15px",
                    "text_align": "center"
                }
            ),
            
            "kawaii": StyleConfig(
                name="可爱卡哇伊风格",
                description="日式可爱风格，粉嫩色彩搭配萌系元素，治愈系设计",
                visual_style="""
• 色彩特征：粉色、淡紫色、淡蓝色等柔和色调
• 装饰元素：小星星、爱心、花朵等可爱图案
• 字体特点：圆润可爱的字体，部分文字带有小装饰
• 边框设计：圆角边框，可能带有花边或波浪线
• 图案元素：小动物、彩虹、云朵等萌系元素
• 整体感觉：温柔治愈，让人心情愉悦""",
                layout_guide="圆润的布局设计，多用曲线和圆角，整体给人温暖可爱的感觉",
                color_scheme={
                    "primary_bg": "#FFF0F5",
                    "secondary_bg": "#FFE4E6",
                    "text_primary": "#8B4B8C",
                    "accent_1": "#FF69B4",
                    "accent_2": "#DDA0DD",
                    "border": "#FFB6C1"
                },
                typography={
                    "main_font": "Nunito, Comic Sans MS, sans-serif",
                    "title_size": "22px",
                    "content_size": "16px",
                    "line_height": "1.6"
                },
                elements={
                    "border_style": "3px solid #FFB6C1",
                    "border_radius": "20px",
                    "padding": "25px",
                    "shadow": "0 4px 12px rgba(255,182,193,0.3)"
                }
            ),
            
            # 小红书风格
            "xiaohongshu_modern": StyleConfig(
                name="小红书现代简约",
                description="现代小红书风格，干净简约的设计配合温暖色调",
                visual_style="""
• 现代简约：干净的版面设计，合理的留白空间
• 温暖色调：米色、暖白色为主，搭配温暖的点缀色
• 时尚元素：现代化的图标和装饰，简约而不简单
• 层次设计：清晰的信息层次，重点突出
• 圆角设计：大量使用圆角，显得亲和友好
• 阴影效果：适度的阴影增加层次感""",
                layout_guide="卡片式布局，信息分层清晰，视觉重点突出，整体现代时尚",
                color_scheme={
                    "primary_bg": "#FFFEF7",
                    "secondary_bg": "#F8F7F0",
                    "text_primary": "#2C2C2C",
                    "text_secondary": "#666666",
                    "accent_1": "#FF2442",
                    "accent_2": "#FFA500",
                    "border": "#E8E6E0"
                },
                typography={
                    "main_font": "PingFang SC, Helvetica Neue, sans-serif",
                    "title_size": "20px",
                    "content_size": "14px",
                    "line_height": "1.5"
                },
                elements={
                    "border_radius": "12px",
                    "padding": "20px",
                    "shadow": "0 2px 10px rgba(0,0,0,0.1)"
                }
            ),
            
            # 商务风格
            "business_professional": StyleConfig(
                name="商务专业风格",
                description="专业商务风格，正式而现代的设计语言",
                visual_style="""
• 专业配色：深蓝、灰色、白色的经典商务配色
• 现代设计：简洁的线条，清晰的层次结构
• 专业字体：易读性强的无衬线字体
• 几何元素：简单的几何图形作为装饰
• 品质感：高质量的视觉呈现，体现专业性
• 功能性：信息传达清晰，重点突出""",
                layout_guide="网格化布局，信息组织有序，突出重点内容，整体严谨专业",
                color_scheme={
                    "primary_bg": "#FFFFFF",
                    "secondary_bg": "#F8F9FA",
                    "text_primary": "#212529",
                    "text_secondary": "#6C757D",
                    "accent_1": "#0056B3",
                    "accent_2": "#28A745",
                    "border": "#DEE2E6"
                },
                typography={
                    "main_font": "Roboto, Arial, sans-serif",
                    "title_size": "18px",
                    "content_size": "14px",
                    "line_height": "1.4"
                },
                elements={
                    "border_radius": "4px",
                    "padding": "24px",
                    "border_style": "1px solid #DEE2E6"
                }
            )
        }
    
    def get_style(self, style_name: str) -> StyleConfig:
        """获取风格配置"""
        return self.styles.get(style_name, self.styles["humor"])
    
    def list_styles(self) -> Dict[str, str]:
        """列出所有可用风格"""
        return {name: config.description for name, config in self.styles.items()}
    
    def get_html_template_params(self, style_name: str) -> Dict[str, Any]:
        """获取HTML模板参数"""
        style = self.get_style(style_name)
        return {
            "style_name": style.name,
            "description": style.description,
            "visual_guide": style.visual_style,
            "layout_guide": style.layout_guide,
            "colors": style.color_scheme,
            "typography": style.typography,
            "elements": style.elements
        }
    
    def get_image_generation_prompt(self, style_name: str, base_content: str) -> str:
        """获取图像生成提示词"""
        style = self.get_style(style_name)
        
        if style_name == "humor":
            return f"""
{base_content}

Style: {style.visual_style}

Visual requirements: minimalist hand-drawn illustration, clean white background with simple line art, 
funny cartoon style, exaggerated expressions, simple doodle elements like "!" "?" symbols, 
small decorative circles and stars, bright but not overwhelming accent colors, 
pencil sketch border, lots of white space, sketch book aesthetic
"""
        elif style_name == "meme_classic":
            return f"""
{base_content}

Style: classic internet meme format, bold black text on white background, 
high contrast, simple and direct, minimal decorations, impact font style, 
traditional meme layout, clean and recognizable format
"""
        elif style_name == "kawaii":
            return f"""
{base_content}

Style: kawaii cute style, soft pastel colors (pink, purple, blue), 
cute decorative elements (stars, hearts, flowers), round and soft shapes, 
adorable characters, healing and warm atmosphere, Japanese cute aesthetic
"""
        else:
            return f"{base_content}\n\nStyle: {style.description}"

# 全局风格配置管理器
style_config_manager = StyleConfigManager()

def get_style_config_manager() -> StyleConfigManager:
    """获取全局风格配置管理器"""
    return style_config_manager