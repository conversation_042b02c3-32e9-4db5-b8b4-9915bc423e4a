"""
视频编辑Agent - 基于现有架构的智能对话编辑系统
"""
import asyncio
import uuid
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime
import json

# 导入现有架构组件
from .state import UniversalState
from .assistant import IntelligentImageAssistant
from .planner import IntelligentPlanner
from .executor import DynamicExecutor
from ..tools.base.tool_registry import ToolRegistry

class AgentState(Enum):
    """Agent状态枚举"""
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    WAITING_CONFIRMATION = "waiting_confirmation"
    EXECUTING = "executing"
    ERROR = "error"

@dataclass
class AgentResponse:
    """Agent响应数据结构"""
    response_id: str
    response_type: str  # "text", "suggestion", "confirmation", "progress", "error"
    content: str
    metadata: Dict[str, Any]
    suggestions: Optional[List[str]] = None
    requires_confirmation: bool = False
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)

@dataclass
class DialogMessage:
    """对话消息"""
    message_id: str
    sender: str  # "user" or "agent"
    content: str
    message_type: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

class VideoEditAgent:
    """视频编辑Agent主控制器"""
    
    def __init__(self, project_id: str, user_id: str):
        self.agent_id = str(uuid.uuid4())
        self.project_id = project_id
        self.user_id = user_id
        self.state = AgentState.IDLE
        
        # 集成现有架构组件
        self.assistant = IntelligentImageAssistant()
        self.planner = IntelligentPlanner()
        self.executor = DynamicExecutor()
        
        # Agent专用组件
        self.dialog_manager = DialogManager(self.agent_id)
        self.context_manager = ContextManager(project_id)
        self.intent_processor = IntentProcessor()
        
        # 状态管理
        self.current_session = None
        self.pending_operations = []
        self.execution_history = []
        self.websocket_connections = []
    
    async def process_user_input(self, user_input: str, input_type: str = "text") -> AgentResponse:
        """处理用户输入的主入口"""
        try:
            self.state = AgentState.LISTENING
            
            # 1. 记录用户消息
            await self.dialog_manager.add_user_message(user_input, input_type)
            
            # 2. 获取当前上下文
            context = await self.context_manager.get_current_context()
            
            # 3. 处理意图
            intent_result = await self.intent_processor.process_intent(user_input, context)
            
            # 4. 根据意图类型分发处理
            if intent_result.get("requires_clarification"):
                return await self._handle_clarification(intent_result)
            elif intent_result.get("is_edit_request"):
                return await self._handle_edit_request(intent_result, context)
            elif intent_result.get("is_query"):
                return await self._handle_query(intent_result, context)
            else:
                return await self._handle_general_conversation(intent_result)
                
        except Exception as e:
            self.state = AgentState.ERROR
            return await self._handle_error(e)
    
    async def _handle_edit_request(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> AgentResponse:
        """处理编辑请求"""
        self.state = AgentState.PROCESSING
        
        try:
            # 1. 将意图转换为现有架构的状态格式
            universal_state = self._convert_to_universal_state(intent_result, context)
            
            # 2. 使用现有的智能助手处理
            result = self.assistant.process_request(intent_result.get("user_request", ""))
            
            # 3. 分析结果，决定是否需要确认
            if self._requires_user_confirmation(intent_result, result):
                self.state = AgentState.WAITING_CONFIRMATION
                self.pending_operations = intent_result.get("edit_intents", [])
                
                return await self._generate_confirmation_response(intent_result, result)
            else:
                # 直接执行
                return await self._execute_edit_operations(intent_result, result)
                
        except Exception as e:
            return await self._handle_error(e)
    
    async def _handle_query(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> AgentResponse:
        """处理查询请求"""
        query_type = intent_result.get("query_type", "general")
        
        if query_type == "project_status":
            return await self._handle_project_status_query(context)
        elif query_type == "capabilities":
            return await self._handle_capabilities_query()
        elif query_type == "history":
            return await self._handle_history_query()
        else:
            return await self._handle_general_query(intent_result)
    
    async def _handle_project_status_query(self, context: Dict[str, Any]) -> AgentResponse:
        """处理项目状态查询"""
        project_info = context.get("project_state", {})
        
        response_content = f"""
        📊 当前项目状态：
        
        🎬 视频信息：
        - 时长：{project_info.get('duration', 0)}秒
        - 分辨率：{project_info.get('resolution', 'unknown')}
        - 格式：{project_info.get('format', 'unknown')}
        
        📝 编辑进度：
        - 已完成操作：{len(self.execution_history)}个
        - 当前状态：{self.state.value}
        
        🛠️ 可用功能：
        - 语音替换 ✅
        - 背景音乐替换 ✅  
        - 字幕编辑 ✅
        - 视觉效果 🔄 开发中
        
        有什么需要我帮您编辑的吗？
        """
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="text",
            content=response_content.strip(),
            metadata={"query_type": "project_status", "project_info": project_info}
        )
    
    async def confirm_operation(self, confirmation: bool, user_feedback: str = "") -> AgentResponse:
        """确认操作"""
        if self.state != AgentState.WAITING_CONFIRMATION:
            return AgentResponse(
                response_id=str(uuid.uuid4()),
                response_type="error",
                content="当前没有待确认的操作",
                metadata={"error_type": "invalid_state"}
            )
        
        if confirmation:
            # 执行待确认的操作
            return await self._execute_pending_operations(user_feedback)
        else:
            # 取消操作
            self.state = AgentState.IDLE
            self.pending_operations = []
            
            return AgentResponse(
                response_id=str(uuid.uuid4()),
                response_type="text",
                content="好的，已取消操作。还有其他需要帮助的吗？",
                metadata={"action": "cancelled"}
            )
    
    async def _execute_pending_operations(self, user_feedback: str = "") -> AgentResponse:
        """执行待确认的操作"""
        self.state = AgentState.EXECUTING
        
        try:
            # 创建执行任务
            execution_task = asyncio.create_task(
                self._run_edit_operations(self.pending_operations, user_feedback)
            )
            
            # 清空待确认操作
            self.pending_operations = []
            
            # 返回执行开始响应
            return AgentResponse(
                response_id=str(uuid.uuid4()),
                response_type="progress",
                content="好的，开始为您处理视频编辑，请稍等...",
                metadata={
                    "status": "started",
                    "task_id": str(execution_task),
                    "estimated_time": "30-60秒"
                }
            )
            
        except Exception as e:
            return await self._handle_error(e)
    
    async def _run_edit_operations(self, operations: List[Dict[str, Any]], user_feedback: str = ""):
        """运行编辑操作"""
        try:
            for i, operation in enumerate(operations):
                # 发送进度更新
                progress = (i + 1) / len(operations) * 100
                await self._send_progress_update(f"正在处理第{i+1}个操作...", progress)
                
                # 执行具体操作
                await self._execute_single_operation(operation)
                
                # 短暂延迟，模拟处理时间
                await asyncio.sleep(1)
            
            # 完成通知
            self.state = AgentState.IDLE
            await self._send_completion_notification()
            
        except Exception as e:
            self.state = AgentState.ERROR
            await self._send_error_notification(str(e))
    
    async def _send_progress_update(self, message: str, progress: float):
        """发送进度更新"""
        update = {
            "type": "progress_update",
            "message": message,
            "progress": progress,
            "timestamp": datetime.now().isoformat()
        }
        
        # 通过WebSocket发送给所有连接的客户端
        for ws in self.websocket_connections:
            try:
                await ws.send_json(update)
            except:
                # 移除失效连接
                self.websocket_connections.remove(ws)
    
    async def _generate_confirmation_response(self, intent_result: Dict[str, Any], processing_result: Dict[str, Any]) -> AgentResponse:
        """生成确认响应"""
        operations = intent_result.get("edit_intents", [])
        
        # 构建操作摘要
        operation_summary = []
        for op in operations:
            action = op.get("action", "unknown")
            target = op.get("target", "unknown")
            operation_summary.append(f"- {action} {target}")
        
        content = f"""
        我理解您想要进行以下编辑操作：
        
        {chr(10).join(operation_summary)}
        
        预计处理时间：30-60秒
        
        是否确认执行这些操作？
        """
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="confirmation",
            content=content.strip(),
            metadata={
                "operations": operations,
                "estimated_time": "30-60秒"
            },
            requires_confirmation=True
        )
    
    def _convert_to_universal_state(self, intent_result: Dict[str, Any], context: Dict[str, Any]) -> UniversalState:
        """将意图结果转换为UniversalState格式"""
        return UniversalState(
            user_request=intent_result.get("user_request", ""),
            user_intent=intent_result,
            execution_plan=None,
            selected_tools=None,
            raw_content=context.get("raw_content"),
            processed_content=None,
            content_segments=None,
            content_metadata=context.get("metadata"),
            html_contents=None,
            image_paths=None,
            final_outputs=None,
            current_step=None,
            execution_history=None,
            error_message=None,
            tool_outputs=None,
            temp_data=context
        )
    
    def _requires_user_confirmation(self, intent_result: Dict[str, Any], processing_result: Dict[str, Any]) -> bool:
        """判断是否需要用户确认"""
        # 如果是重要操作（如删除、替换）需要确认
        operations = intent_result.get("edit_intents", [])
        
        for op in operations:
            action = op.get("action", "")
            if action in ["replace", "delete", "remove"]:
                return True
        
        # 如果置信度较低需要确认
        if intent_result.get("confidence", 1.0) < 0.8:
            return True
        
        return False
    
    async def _handle_error(self, error: Exception) -> AgentResponse:
        """处理错误"""
        self.state = AgentState.ERROR
        
        error_message = f"抱歉，处理过程中出现了问题：{str(error)}"
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="error",
            content=error_message,
            metadata={"error_type": type(error).__name__, "error_details": str(error)}
        )

class DialogManager:
    """对话管理器"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.conversation_history: List[DialogMessage] = []
        self.context_window_size = 10
    
    async def add_user_message(self, content: str, message_type: str = "text"):
        """添加用户消息"""
        message = DialogMessage(
            message_id=str(uuid.uuid4()),
            sender="user",
            content=content,
            message_type=message_type,
            timestamp=datetime.now()
        )
        self.conversation_history.append(message)
        self._maintain_context_window()
    
    def _maintain_context_window(self):
        """维护上下文窗口"""
        if len(self.conversation_history) > self.context_window_size * 2:
            self.conversation_history = self.conversation_history[-self.context_window_size:]

class ContextManager:
    """上下文管理器"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.project_state = {}
    
    async def get_current_context(self) -> Dict[str, Any]:
        """获取当前上下文"""
        return {
            "project_id": self.project_id,
            "project_state": self.project_state,
            "timestamp": datetime.now().isoformat()
        }

class IntentProcessor:
    """意图处理器"""
    
    async def process_intent(self, user_input: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户意图"""
        # 这里可以集成更复杂的意图理解逻辑
        # 目前返回简化的结果
        
        # 简单的关键词匹配
        if any(keyword in user_input.lower() for keyword in ["替换", "换成", "改成"]):
            return {
                "intent_type": "edit_request",
                "is_edit_request": True,
                "confidence": 0.9,
                "edit_intents": [
                    {
                        "action": "replace",
                        "target": "audio",
                        "parameters": {"user_request": user_input}
                    }
                ],
                "user_request": user_input
            }
        elif any(keyword in user_input.lower() for keyword in ["状态", "进度", "怎么样"]):
            return {
                "intent_type": "query",
                "is_query": True,
                "query_type": "project_status",
                "confidence": 0.9
            }
        else:
            return {
                "intent_type": "general",
                "confidence": 0.5,
                "requires_clarification": True
            }
