"""
智能图片生成助手 - 主控制器
"""
from typing import Dict, Any
from core.state import UniversalState
from core.planner import IntelligentPlanner
from core.selector import HybridSelector
from core.executor import DynamicExecutor

class IntelligentImageAssistant:
    """智能图片生成助手"""
    
    def __init__(self):
        self.planner = IntelligentPlanner()
        self.selector = HybridSelector()
        self.executor = DynamicExecutor()
    
    def process_request(self, user_request: str) -> Dict[str, Any]:
        """处理用户请求"""
        
        print(f"🤖 智能助手开始处理请求: {user_request}")
        
        # 1. 初始化状态
        state = UniversalState(
            user_request=user_request,
            user_intent=None,
            execution_plan=None,
            selected_tools=None,
            raw_content=None,
            processed_content=None,
            content_segments=None,
            content_metadata=None,
            html_contents=None,
            image_paths=None,
            final_outputs=None,
            current_step=None,
            execution_history=None,
            error_message=None,
            tool_outputs=None,
            temp_data=None
        )
        
        try:
            # 2. 分析用户意图
            print("📋 分析用户意图...")
            intent = self.planner.analyze_intent(user_request)
            state["user_intent"] = intent
            print(f"   意图分析结果: {intent}")
            
            # 3. 生成执行计划
            print("⚡ 生成执行计划...")
            plan = self.planner.generate_execution_plan(intent)
            state["execution_plan"] = plan
            print(f"   执行计划: {plan}")
            
            # 4. 选择工具
            print("🔧 选择执行工具...")
            tools = self.selector.select_tools(plan)
            state["selected_tools"] = tools
            print(f"   选中工具: {tools}")
            
            # 5. 执行工具链
            print("🚀 开始执行工具链...")
            final_state = self.executor.execute_tool_chain(tools, state)
            
            # 6. 返回结果
            if final_state.get("error_message"):
                print(f"❌ 执行失败: {final_state['error_message']}")
            else:
                print("✅ 执行完成!")
                if final_state.get("image_paths"):
                    print(f"   生成图片: {final_state['image_paths']}")
            
            return final_state
            
        except Exception as e:
            error_msg = f"智能助手处理异常: {str(e)}"
            print(f"💥 {error_msg}")
            state["error_message"] = error_msg
            return state
    
    def get_available_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        from tools.base.tool_registry import ToolRegistry
        
        tools_info = {}
        for tool_name in ToolRegistry.list_all_tools():
            tools_info[tool_name] = ToolRegistry.get_tool_info(tool_name)
        
        return tools_info