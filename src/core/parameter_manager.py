"""
参数管理器 - 统一管理工具参数传递
"""
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from core.style_config import get_style_config_manager

@dataclass
class ImageGenerationParams:
    """图像生成参数"""
    prompt: str = ""
    style: str = "modern"
    width: int = 810
    height: int = 1080
    seed: int = -1
    scale: float = 2.5
    enhance_prompt: bool = True

@dataclass
class ImageEditParams:
    """图像编辑参数"""
    input_image_url: str = ""
    edit_prompt: str = ""
    model: str = "black-forest-labs/flux-kontext-pro"
    aspect_ratio: str = "match_input_image"
    seed: Optional[int] = None
    output_format: str = "png"
    enhance_prompt: bool = True

@dataclass
class StyleConversionParams:
    """风格转换参数"""
    target_style: str = "xiaohongshu"
    enhance_with_llm: bool = True
    style_keywords: list = field(default_factory=list)
    tone: str = "casual"  # casual, formal, humor, professional

@dataclass
class HtmlGenerationParams:
    """HTML生成参数"""
    template_type: str = "modern"
    background_color: str = "#FFFFFF"
    text_color: str = "#333333"
    font_family: str = "Arial, sans-serif"
    custom_css: Dict[str, Any] = field(default_factory=dict)

class ParameterManager:
    """参数管理器 - 负责为不同工具准备正确的参数"""
    
    def __init__(self):
        self.style_manager = get_style_config_manager()
        self.presets = {
            # 小红书预设参数
            "xiaohongshu": {
                "style": StyleConversionParams(
                    target_style="xiaohongshu",
                    enhance_with_llm=True,
                    tone="casual"
                ),
                "html": HtmlGenerationParams(
                    template_type="social_media",
                    background_color="#FFF8DC",
                    font_family="PingFang SC, sans-serif"
                )
            },
            
            # Meme预设参数 - 使用详细风格配置
            "meme": {
                "style": StyleConversionParams(
                    target_style="humor_meme",
                    enhance_with_llm=True,
                    style_keywords=["搞笑", "幽默", "夸张", "网络梗"],
                    tone="humor"
                ),
                "image": ImageGenerationParams(
                    style="humor",
                    width=810,
                    height=810,  # 正方形适合表情包
                    scale=3.0,
                    enhance_prompt=True
                ),
                "image_edit": ImageEditParams(
                    edit_prompt="enhance this meme image to make it more funny and expressive",
                    enhance_prompt=True
                ),
                "html_style": "humor"  # 引用详细风格配置
            },
            
            # 商务预设参数
            "business": {
                "style": StyleConversionParams(
                    target_style="business_formal",
                    enhance_with_llm=True,
                    tone="professional"
                ),
                "image": ImageGenerationParams(
                    style="business",
                    width=1200,
                    height=800,
                    scale=2.0,
                    enhance_prompt=True
                ),
                "html": HtmlGenerationParams(
                    template_type="business",
                    background_color="#F8F9FA",
                    text_color="#212529",
                    font_family="Arial, sans-serif"
                )
            }
        }
    
    def get_preset_params(self, preset_name: str) -> Dict[str, Any]:
        """获取预设参数"""
        return self.presets.get(preset_name, {})
    
    def prepare_tool_params(self, tool_name: str, preset_name: str, 
                           custom_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """为特定工具准备参数"""
        preset_params = self.get_preset_params(preset_name)
        custom_params = custom_params or {}
        
        # 根据工具类型选择对应参数
        if "ai_image" in tool_name:
            base_params = preset_params.get("image", ImageGenerationParams())
            if isinstance(base_params, ImageGenerationParams):
                params = {
                    "prompt": custom_params.get("prompt", base_params.prompt),
                    "style": custom_params.get("style", base_params.style),
                    "width": custom_params.get("width", base_params.width),
                    "height": custom_params.get("height", base_params.height),
                    "seed": custom_params.get("seed", base_params.seed),
                    "scale": custom_params.get("scale", base_params.scale),
                    "enhance_prompt": custom_params.get("enhance_prompt", base_params.enhance_prompt)
                }
                return params
        
        elif "image_editor" in tool_name:
            base_params = preset_params.get("image_edit", ImageEditParams())
            if isinstance(base_params, ImageEditParams):
                params = {
                    "edit_prompt": custom_params.get("edit_prompt", base_params.edit_prompt),
                    "model": custom_params.get("model", base_params.model),
                    "aspect_ratio": custom_params.get("aspect_ratio", base_params.aspect_ratio),
                    "seed": custom_params.get("seed", base_params.seed),
                    "output_format": custom_params.get("output_format", base_params.output_format),
                    "enhance_prompt": custom_params.get("enhance_prompt", base_params.enhance_prompt)
                }
                return params
        
        elif "style_converter" in tool_name:
            base_params = preset_params.get("style", StyleConversionParams())
            if isinstance(base_params, StyleConversionParams):
                params = {
                    "target_style": custom_params.get("target_style", base_params.target_style),
                    "enhance_with_llm": custom_params.get("enhance_with_llm", base_params.enhance_with_llm),
                    "style_keywords": custom_params.get("style_keywords", base_params.style_keywords),
                    "tone": custom_params.get("tone", base_params.tone)
                }
                return params
        
        elif "html" in tool_name:
            # 使用详细风格配置
            style_name = preset_params.get("html_style", "humor")
            style_params = self.style_manager.get_html_template_params(style_name)
            
            # 应用自定义参数
            html_theme = custom_params.get("html_theme")
            if html_theme == "dark":
                style_params["colors"]["primary_bg"] = "#2F2F2F"
                style_params["colors"]["text_primary"] = "#FFFFFF"
            elif html_theme == "bright":
                style_params["colors"]["primary_bg"] = "#FFFF00"
                style_params["colors"]["text_primary"] = "#FF0000"
            
            # 合并自定义CSS
            if "custom_css" in custom_params:
                style_params["elements"].update(custom_params["custom_css"])
            
            return {
                "style_config": style_params,
                "template_type": style_name
            }
        
        # 如果没有匹配的参数配置，返回自定义参数
        return custom_params
    
    def create_meme_image_prompt(self, base_content: str, style_name: str = "humor") -> str:
        """创建Meme专用图像提示词 - 使用详细风格配置"""
        return self.style_manager.get_image_generation_prompt(style_name, base_content)
    
    def create_business_image_prompt(self, base_content: str) -> str:
        """创建商务专用图像提示词"""
        business_style_template = """
{content}

Style: professional business illustration, clean modern design, corporate aesthetic, 
minimalist style, professional photography, high quality, sophisticated, formal presentation, 
business conference setting, professional lighting
"""
        return business_style_template.format(content=base_content)

# 全局参数管理器实例
param_manager = ParameterManager()

def get_param_manager() -> ParameterManager:
    """获取全局参数管理器"""
    return param_manager