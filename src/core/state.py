"""
统一状态管理
"""
from typing import TypedDict, List, Optional, Dict, Any

class UniversalState(TypedDict):
    """通用状态，用于在整个处理流程中传递数据"""
    
    # === 用户输入 ===
    user_request: str  # 用户的原始请求
    user_intent: Optional[Dict[str, Any]]  # 解析后的用户意图
    
    # === 执行计划 ===
    execution_plan: Optional[Dict[str, Any]]  # 生成的执行计划
    selected_tools: Optional[List[str]]  # 选中的工具列表
    
    # === 内容数据 ===
    raw_content: Optional[str]  # 原始内容
    processed_content: Optional[str]  # 处理后的内容
    content_segments: Optional[List[str]]  # 分段内容
    content_metadata: Optional[Dict[str, Any]]  # 内容元数据
    
    # === 生成结果 ===
    html_contents: Optional[List[str]]  # HTML内容
    image_paths: Optional[List[str]]  # 生成的图片路径
    final_outputs: Optional[List[str]]  # 最终输出文件路径
    
    # === 执行状态 ===
    current_step: Optional[str]  # 当前执行步骤
    execution_history: Optional[List[Dict[str, Any]]]  # 执行历史
    error_message: Optional[str]  # 错误信息
    
    # === 工具间传递数据 ===
    tool_outputs: Optional[Dict[str, Any]]  # 工具输出缓存
    temp_data: Optional[Dict[str, Any]]  # 临时数据

# 向后兼容，保留原有状态
class CardGenerationState(TypedDict):
    """原有的卡片生成状态，用于兼容现有代码"""
    original_text: str
    source_identifier: Optional[str]
    xiaohongshu_text: Optional[str]
    segmented_texts: Optional[List[str]]
    html_contents: Optional[List[str]]
    image_paths: Optional[List[str]]
    error_message: Optional[str]
    current_step: Optional[str]