# 🎬 XHS Image Text Agent Framework

这是一个基于LangGraph的智能图文生成和视频理解框架，专为小红书等社交媒体平台设计。

## ✨ 功能特性

- 🎬 **视频理解分析** - 支持任意大小视频，智能生成脚本
- 🎨 **智能图像生成** - 多种风格的图像创作
- 📝 **文本内容创作** - 智能文案和故事生成
- 🔄 **工作流自动化** - 端到端的内容创作流程
- 📊 **多模态处理** - 图文视频一体化处理

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 创建 .env 文件
VIDEO_UNDERSTAND_KEY=your_api_key
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key
COS_REGION=ap-guangzhou
COS_BUCKET=your_bucket_name
```

### 3. 使用主入口
```bash
# 视频理解
python main.py video --url "https://example.com/video.mp4"

# 图像生成
python main.py image --text "生成一张图片"

# 内容创作
python main.py content --type "story" --theme "科技"
```

## 📁 项目结构

```
📁 xhs_image_textagenf/
├── 📋 README.md                           # 主项目文档
├── 🚀 main.py                             # 主入口文件
├── 📁 src/                                # 源代码目录
│   ├── 📁 core/                           # 核心模块
│   ├── 📁 workflows/                      # 工作流模块
│   ├── 📁 tools/                          # 工具模块
│   ├── 📁 utils/                          # 工具函数
│   └── 📁 configs/                        # 配置文件
├── 📁 tests/                              # 测试目录
├── 📁 examples/                           # 示例代码
├── 📁 docs/                               # 文档目录
└── 📁 output/                             # 输出目录
```

## 🎯 核心功能

### 🎬 视频理解系统
- **无限制处理** - 支持任意大小视频文件
- **智能预处理** - 自动压缩和分段
- **并发分析** - 3-4倍速度提升
- **JSON修复** - 自动修复格式错误
- **云存储集成** - 自动上传到COS

**测试验证**: 成功处理111MB视频，生成56个镜头的完整脚本

### 🎨 图像生成系统
- 多种风格支持
- 智能提示词优化
- 批量生成功能

### 📝 内容创作系统
- 故事生成
- 文案创作
- 脚本编写

## 📖 文档

- [视频理解系统文档](README_VIDEO_UNDERSTANDING.md)
- [API参考文档](docs/API_REFERENCE.md)
- [开发指南](docs/DEVELOPMENT.md)
- [部署指南](docs/)

## 🧪 测试

```bash
# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/

# 运行端到端测试
python -m pytest tests/e2e/
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**版本**: v2.0  
**状态**: ✅ 生产就绪  
**主要功能**: 视频理解、图像生成、内容创作
