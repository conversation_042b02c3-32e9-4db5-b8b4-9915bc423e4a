#!/usr/bin/env python3
"""
XHS Image Text Agent Framework - 主入口文件
智能图文生成和视频理解框架
"""

import sys
import os
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="XHS Image Text Agent Framework",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 视频理解
  python main.py video --url "https://example.com/video.mp4"
  python main.py video --path "/path/to/video.mp4"
  
  # 图像生成
  python main.py image --text "生成一张图片"
  
  # 内容创作
  python main.py content --type "story" --theme "科技"
        """
    )
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 视频理解命令
    video_parser = subparsers.add_parser('video', help='视频理解和分析')
    video_parser.add_argument('--url', type=str, help='视频URL')
    video_parser.add_argument('--path', type=str, help='本地视频路径')
    video_parser.add_argument('--request', type=str, default='请详细分析这个视频', help='分析需求')
    video_parser.add_argument('--output', type=str, help='输出文件路径')
    
    # 图像生成命令
    image_parser = subparsers.add_parser('image', help='图像生成')
    image_parser.add_argument('--text', type=str, required=True, help='生成描述')
    image_parser.add_argument('--style', type=str, default='realistic', help='图像风格')
    image_parser.add_argument('--output', type=str, help='输出目录')
    
    # 内容创作命令
    content_parser = subparsers.add_parser('content', help='内容创作')
    content_parser.add_argument('--type', type=str, choices=['story', 'article', 'script'], 
                               default='story', help='内容类型')
    content_parser.add_argument('--theme', type=str, required=True, help='内容主题')
    content_parser.add_argument('--output', type=str, help='输出文件路径')
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行对应命令
    if args.command == 'video':
        run_video_command(args)
    elif args.command == 'image':
        run_image_command(args)
    elif args.command == 'content':
        run_content_command(args)

def run_video_command(args):
    """执行视频理解命令"""
    try:
        from src.workflows.video_understanding import run_video_understanding_v2
        
        if not args.url and not args.path:
            print("❌ 请提供视频URL (--url) 或本地路径 (--path)")
            return
        
        print("🎬 开始视频理解...")
        
        result = run_video_understanding_v2(
            video_path=args.path,
            video_url=args.url,
            user_request=args.request
        )
        
        if result and not result.get("error_message"):
            print("✅ 视频理解完成！")
            if result.get("output_file_path"):
                print(f"📄 输出文件: {result['output_file_path']}")
            if result.get("cos_url"):
                print(f"🔗 云端链接: {result['cos_url']}")
        else:
            print(f"❌ 视频理解失败: {result.get('error_message') if result else '未知错误'}")
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已正确安装依赖: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ 执行错误: {e}")

def run_image_command(args):
    """执行图像生成命令"""
    print("🎨 图像生成功能开发中...")
    print(f"📝 生成描述: {args.text}")
    print(f"🎭 图像风格: {args.style}")

def run_content_command(args):
    """执行内容创作命令"""
    print("📝 内容创作功能开发中...")
    print(f"📋 内容类型: {args.type}")
    print(f"🎯 内容主题: {args.theme}")

if __name__ == "__main__":
    main()
