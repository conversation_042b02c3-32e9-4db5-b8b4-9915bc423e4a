# API 参考手册

## 核心类

### IntelligentImageAssistant

智能图片生成助手主类。

#### 构造函数
```python
assistant = IntelligentImageAssistant()
```

#### 方法

##### process_request(user_request: str) -> Dict[str, Any]
处理用户请求，返回执行结果。

**参数**:
- `user_request` (str): 用户请求文本

**返回**: Dict 包含以下字段:
- `user_intent` (Dict): 解析的用户意图
- `selected_tools` (List[str]): 选中的工具列表  
- `image_paths` (List[str]): 生成的图片路径
- `error_message` (str, optional): 错误信息

**示例**:
```python
result = assistant.process_request("生成小红书风格图文")
if result.get("image_paths"):
    print(f"生成了 {len(result['image_paths'])} 张图片")
```

##### get_available_tools() -> Dict[str, Any]
获取所有可用工具信息。

**返回**: Dict[工具名称, 工具信息]

## 工具基类

### BaseTool

所有工具的基础类。

#### 属性
- `name` (str): 工具名称
- `category` (ToolCategory): 工具类别
- `description` (str): 工具描述
- `input_keys` (List[str]): 输入字段列表
- `output_keys` (List[str]): 输出字段列表
- `dependencies` (List[str]): 依赖工具列表

#### 方法

##### process(input_data: Dict[str, Any]) -> Dict[str, Any]
核心处理方法，子类必须实现。

**参数**:
- `input_data` (Dict): 输入数据

**返回**: Dict 处理结果

##### execute(state: UniversalState) -> Dict[str, Any]
标准执行接口，包含错误处理。

##### validate_input(input_data: Dict[str, Any]) -> bool
验证输入数据有效性。

##### get_info() -> Dict[str, Any]  
获取工具基本信息。

## 工具注册

### ToolRegistry

工具注册管理类。

#### 静态方法

##### register(category: ToolCategory, name: str = None)
工具注册装饰器。

**参数**:
- `category` (ToolCategory): 工具类别
- `name` (str, optional): 工具名称，默认使用类名

**示例**:
```python
@ToolRegistry.register(ToolCategory.STYLE_CONVERTER, "meme")
class MemeStyleTool(BaseTool):
    pass
```

##### get_tool(name: str) -> Type[BaseTool]
获取工具类。

##### create_tool(name: str) -> BaseTool
创建工具实例。

##### list_all_tools() -> List[str]
列出所有已注册工具。

## 状态管理

### UniversalState

通用状态字典，用于工具间数据传递。

#### 字段

##### 用户输入
- `user_request` (str): 用户原始请求
- `user_intent` (Dict, optional): 解析的用户意图

##### 执行计划  
- `execution_plan` (Dict, optional): 生成的执行计划
- `selected_tools` (List[str], optional): 选中的工具列表

##### 内容数据
- `raw_content` (str, optional): 原始内容
- `processed_content` (str, optional): 处理后的内容
- `content_segments` (List[str], optional): 分段内容
- `content_metadata` (Dict, optional): 内容元数据

##### 生成结果
- `html_contents` (List[str], optional): HTML内容
- `image_paths` (List[str], optional): 图片路径
- `final_outputs` (List[str], optional): 最终输出文件

##### 执行状态
- `current_step` (str, optional): 当前执行步骤
- `error_message` (str, optional): 错误信息
- `tool_outputs` (Dict, optional): 工具输出缓存

## 工具类别

### ToolCategory

工具分类枚举。

```python
class ToolCategory(Enum):
    INPUT = "input"                    # 输入处理
    ANALYZER = "analyzer"              # 内容分析  
    STYLE_CONVERTER = "style_converter" # 风格转换
    CONTENT_PROCESSOR = "content_processor" # 内容处理
    GENERATOR = "generator"            # 生成工具
    OUTPUT = "output"                  # 输出工具
```

## 已实现工具

### 输入工具

#### TextInputTool
**注册名**: `input.text_input`
**描述**: 处理用户输入的文本内容

**输入**:
- `user_request` (str): 用户请求文本

**输出**:
- `raw_content` (str): 处理后的文本
- `content_metadata` (Dict): 内容元数据

#### CsvReaderTool  
**注册名**: `input.csv_reader`
**描述**: 从CSV文件读取指定ID的内容

**输入**:
- `source_identifier` (str): CSV中的记录ID

**输出**:
- `raw_content` (str): 读取的内容
- `content_metadata` (Dict): 内容元数据

### 风格转换工具

#### XiaohongshuStyleTool
**注册名**: `style_converter.xiaohongshu`  
**描述**: 将原始文本转换为小红书风格

**输入**:
- `raw_content` (str): 原始文本

**输出**:
- `processed_content` (str): 小红书风格文本
- `style_metadata` (Dict): 风格元数据

### 内容处理工具

#### TextSegmenterTool
**注册名**: `content_processor.segmenter`
**描述**: 将文本智能分段，用于制作多页图文

**输入**:
- `processed_content` (str): 待分段文本

**输出**:
- `content_segments` (List[str]): 分段结果
- `segment_metadata` (Dict): 分段元数据

### 生成工具

#### HtmlGeneratorTool  
**注册名**: `generator.html`
**描述**: 为每个文本片段生成HTML卡片

**输入**:
- `content_segments` (List[str]): 文本片段
- `processed_content` (str, optional): 处理后的内容
- `raw_content` (str, optional): 原始内容

**输出**:
- `html_contents` (List[str]): HTML内容列表
- `html_metadata` (Dict): HTML元数据

### 输出工具

#### ImageConverterTool
**注册名**: `output.html2image`
**描述**: 将HTML内容转换为图片文件

**输入**:
- `html_contents` (List[str]): HTML内容列表

**输出**:
- `image_paths` (List[str]): 生成的图片路径
- `final_outputs` (List[str]): 最终输出文件路径
- `conversion_metadata` (Dict): 转换元数据

## 预设流程

### XiaohongshuPresetGraph

小红书图文生成预设流程。

#### 构造函数
```python
preset = XiaohongshuPresetGraph()
```

#### 方法

##### execute(initial_state: UniversalState) -> UniversalState
执行小红书预设流程。

**参数**:
- `initial_state` (UniversalState): 初始状态

**返回**: UniversalState 执行结果

##### get_tools_for_input_type(input_type: str) -> List[str]
根据输入类型获取工具链。

**参数**:
- `input_type` (str): "text" 或 "csv"

**返回**: List[str] 工具名称列表

##### get_info() -> Dict[str, Any]
获取预设流程信息。

## 错误处理

### 错误类型

#### 工具执行错误
当工具执行失败时，返回包含 `error_message` 字段的字典：

```python
{
    "error_message": "具体错误信息",
    "current_step": "当前执行的工具名称"
}
```

#### 常见错误

1. **输入验证错误**: 缺少必需的输入字段
2. **LLM调用错误**: API调用失败或超时
3. **文件操作错误**: 文件读写权限或路径问题
4. **工具不存在错误**: 请求的工具未注册

### 错误处理最佳实践

```python
def safe_tool_execution(tool_name: str, input_data: Dict):
    """安全的工具执行"""
    try:
        tool = ToolRegistry.create_tool(tool_name)
        if not tool:
            return {"error_message": f"工具不存在: {tool_name}"}
        
        result = tool.execute(input_data)
        return result
        
    except Exception as e:
        return {"error_message": f"工具执行异常: {str(e)}"}
```

## 配置选项

### LLM配置

通过环境变量配置：

```bash
export CUSTOM_OPENAI_API_KEY="your-api-key"
export CUSTOM_OPENAI_BASE_URL="your-base-url"
```

### 输出配置

图片输出默认配置：
- 宽度: 600px
- 高度: 800px  
- 格式: PNG
- 输出目录: `output/images/`

## 扩展接口

### 自定义工具接口

```python
class CustomTool(BaseTool):
    def __init__(self):
        super().__init__()
        # 设置工具属性
    
    def process(self, input_data):
        # 实现处理逻辑
        return {"result": "处理结果"}
    
    def validate_input(self, input_data):
        # 自定义输入验证
        return super().validate_input(input_data)
```

### 自定义预设流程接口

```python
class CustomPresetGraph:
    def __init__(self):
        self.name = "custom_preset"
        self.tools = ["tool1", "tool2", "tool3"]
    
    def execute(self, initial_state):
        # 实现执行逻辑
        return final_state
    
    def get_info(self):
        # 返回流程信息
        return {"name": self.name, "tools": self.tools}
```

---

💡 **API使用提示**:
- 总是检查返回结果中的 `error_message` 字段
- 使用类型提示提高代码可读性
- 遵循输入输出字段约定
- 及时处理异常情况