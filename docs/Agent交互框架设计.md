# 视频编辑Agent交互框架设计

## 🎯 核心设计理念

### 1. 对话式交互
- 支持自然语言输入，理解用户编辑意图
- 维护多轮对话上下文，支持澄清和确认
- 智能推荐编辑方案，提供专业建议

### 2. 状态感知
- 实时感知视频项目状态
- 理解编辑历史和用户偏好
- 基于上下文提供个性化服务

### 3. 渐进式交互
- 支持分步骤编辑，逐步完善
- 实时预览和调整
- 支持撤销和重做操作

## 🤖 Agent核心架构

### 1. VideoEditAgent 主控制器

```python
from typing import Dict, List, Any, Optional, AsyncGenerator
from dataclasses import dataclass
from enum import Enum
import asyncio
import uuid

class AgentState(Enum):
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    WAITING_CONFIRMATION = "waiting_confirmation"
    EXECUTING = "executing"
    ERROR = "error"

@dataclass
class AgentResponse:
    """Agent响应"""
    response_id: str
    response_type: str  # "text", "suggestion", "confirmation", "progress", "error"
    content: str
    metadata: Dict[str, Any]
    suggestions: List[str] = None
    requires_confirmation: bool = False

class VideoEditAgent:
    """视频编辑Agent主控制器"""
    
    def __init__(self, project_id: str, user_id: str):
        self.agent_id = str(uuid.uuid4())
        self.project_id = project_id
        self.user_id = user_id
        self.state = AgentState.IDLE
        
        # 核心组件
        self.dialog_manager = DialogManager(self.agent_id)
        self.context_manager = ContextManager(project_id)
        self.intent_processor = IntentProcessor()
        self.edit_planner = EditPlanner()
        self.execution_controller = ExecutionController()
        self.response_generator = ResponseGenerator()
        
        # 状态管理
        self.current_session = None
        self.pending_operations = []
        self.execution_history = []
    
    async def process_user_input(self, user_input: str, input_type: str = "text") -> AgentResponse:
        """处理用户输入的主入口"""
        try:
            self.state = AgentState.LISTENING
            
            # 1. 更新对话上下文
            await self.dialog_manager.add_user_message(user_input, input_type)
            
            # 2. 理解用户意图
            intent_result = await self.intent_processor.process_intent(
                user_input, 
                self.context_manager.get_current_context()
            )
            
            # 3. 处理不同类型的意图
            if intent_result.requires_clarification:
                return await self._handle_clarification(intent_result)
            elif intent_result.is_edit_request:
                return await self._handle_edit_request(intent_result)
            elif intent_result.is_query:
                return await self._handle_query(intent_result)
            else:
                return await self._handle_general_conversation(intent_result)
                
        except Exception as e:
            self.state = AgentState.ERROR
            return await self._handle_error(e)
    
    async def _handle_edit_request(self, intent_result) -> AgentResponse:
        """处理编辑请求"""
        self.state = AgentState.PROCESSING
        
        # 1. 生成编辑计划
        edit_plan = await self.edit_planner.create_edit_plan(
            intent_result.edit_intents,
            self.context_manager.get_project_state()
        )
        
        # 2. 验证和优化计划
        validated_plan = await self.edit_planner.validate_and_optimize(edit_plan)
        
        # 3. 生成确认响应
        if validated_plan.requires_confirmation:
            self.state = AgentState.WAITING_CONFIRMATION
            self.pending_operations = validated_plan.operations
            
            return await self.response_generator.generate_confirmation_response(
                validated_plan, intent_result
            )
        else:
            # 直接执行
            return await self._execute_edit_plan(validated_plan)
    
    async def _execute_edit_plan(self, edit_plan) -> AgentResponse:
        """执行编辑计划"""
        self.state = AgentState.EXECUTING
        
        # 启动异步执行
        execution_task = asyncio.create_task(
            self.execution_controller.execute_plan(edit_plan)
        )
        
        # 返回执行开始响应
        response = await self.response_generator.generate_execution_start_response(edit_plan)
        
        # 设置进度回调
        self.execution_controller.set_progress_callback(self._on_execution_progress)
        
        return response
    
    async def _on_execution_progress(self, progress_info: Dict[str, Any]):
        """执行进度回调"""
        # 通过WebSocket发送进度更新
        await self.dialog_manager.send_progress_update(progress_info)
    
    async def confirm_operation(self, confirmation: bool) -> AgentResponse:
        """确认操作"""
        if self.state != AgentState.WAITING_CONFIRMATION:
            return await self.response_generator.generate_error_response(
                "当前没有待确认的操作"
            )
        
        if confirmation:
            # 执行待确认的操作
            edit_plan = EditPlan(operations=self.pending_operations)
            return await self._execute_edit_plan(edit_plan)
        else:
            # 取消操作
            self.state = AgentState.IDLE
            self.pending_operations = []
            return await self.response_generator.generate_cancellation_response()
```

### 2. 对话管理器 (DialogManager)

```python
from datetime import datetime
from typing import List, Dict, Any

@dataclass
class DialogMessage:
    """对话消息"""
    message_id: str
    sender: str  # "user" or "agent"
    content: str
    message_type: str  # "text", "voice", "image", "confirmation"
    timestamp: datetime
    metadata: Dict[str, Any] = None

class DialogManager:
    """对话管理器"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.conversation_history: List[DialogMessage] = []
        self.current_topic = None
        self.context_window_size = 10  # 保持最近10轮对话
        
    async def add_user_message(self, content: str, message_type: str = "text") -> None:
        """添加用户消息"""
        message = DialogMessage(
            message_id=str(uuid.uuid4()),
            sender="user",
            content=content,
            message_type=message_type,
            timestamp=datetime.now()
        )
        
        self.conversation_history.append(message)
        await self._update_topic(content)
        self._maintain_context_window()
    
    async def add_agent_response(self, response: AgentResponse) -> None:
        """添加Agent响应"""
        message = DialogMessage(
            message_id=response.response_id,
            sender="agent",
            content=response.content,
            message_type=response.response_type,
            timestamp=datetime.now(),
            metadata=response.metadata
        )
        
        self.conversation_history.append(message)
        self._maintain_context_window()
    
    def get_conversation_context(self, window_size: int = None) -> List[DialogMessage]:
        """获取对话上下文"""
        size = window_size or self.context_window_size
        return self.conversation_history[-size:]
    
    async def _update_topic(self, user_input: str):
        """更新当前话题"""
        # 使用LLM分析当前话题
        # 这里可以集成话题检测算法
        pass
    
    def _maintain_context_window(self):
        """维护上下文窗口大小"""
        if len(self.conversation_history) > self.context_window_size * 2:
            # 保留重要消息，删除冗余消息
            self.conversation_history = self.conversation_history[-self.context_window_size:]
```

### 3. 意图处理器 (IntentProcessor)

```python
from typing import List, Dict, Any
import json

@dataclass
class IntentResult:
    """意图处理结果"""
    intent_type: str
    confidence: float
    edit_intents: List[Dict[str, Any]] = None
    requires_clarification: bool = False
    clarification_questions: List[str] = None
    is_edit_request: bool = False
    is_query: bool = False
    entities: Dict[str, Any] = None

class IntentProcessor:
    """意图处理器"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.intent_templates = self._load_intent_templates()
        self.entity_patterns = self._load_entity_patterns()
    
    async def process_intent(self, user_input: str, context: Dict[str, Any]) -> IntentResult:
        """处理用户意图"""
        
        # 1. 构建意图分析提示词
        prompt = self._build_intent_analysis_prompt(user_input, context)
        
        # 2. 调用LLM分析意图
        llm_response = await self.llm_service.analyze_intent(prompt)
        
        # 3. 解析LLM响应
        intent_result = self._parse_intent_response(llm_response)
        
        # 4. 实体提取
        entities = await self._extract_entities(user_input, intent_result.intent_type)
        intent_result.entities = entities
        
        # 5. 歧义检测和澄清
        if self._needs_clarification(intent_result, context):
            intent_result.requires_clarification = True
            intent_result.clarification_questions = await self._generate_clarification_questions(
                intent_result, context
            )
        
        return intent_result
    
    def _build_intent_analysis_prompt(self, user_input: str, context: Dict[str, Any]) -> str:
        """构建意图分析提示词"""
        return f"""
        你是一个专业的视频编辑助手。请分析用户的编辑需求并识别意图。

        当前项目信息：
        - 视频时长：{context.get('video_duration', 0)}秒
        - 当前片段数：{len(context.get('segments', []))}
        - 可用轨道：{context.get('tracks', [])}
        - 最近操作：{context.get('recent_operations', [])}

        对话历史：
        {self._format_conversation_history(context.get('conversation_history', []))}

        用户输入：{user_input}

        请分析并返回JSON格式的意图：
        {{
            "intent_type": "edit_request|query|general_conversation",
            "confidence": 0.95,
            "is_edit_request": true,
            "edit_intents": [
                {{
                    "action": "replace_audio",
                    "target": "segment_1",
                    "parameters": {{"new_content": "新的音频内容", "style": "专业"}}
                }}
            ],
            "requires_clarification": false,
            "clarification_reason": "需要明确具体的替换内容"
        }}
        """
    
    async def _extract_entities(self, user_input: str, intent_type: str) -> Dict[str, Any]:
        """提取实体信息"""
        entities = {}
        
        # 时间实体提取
        time_entities = self._extract_time_entities(user_input)
        if time_entities:
            entities['time'] = time_entities
        
        # 内容实体提取
        content_entities = self._extract_content_entities(user_input)
        if content_entities:
            entities['content'] = content_entities
        
        # 风格实体提取
        style_entities = self._extract_style_entities(user_input)
        if style_entities:
            entities['style'] = style_entities
        
        return entities
    
    def _needs_clarification(self, intent_result: IntentResult, context: Dict[str, Any]) -> bool:
        """判断是否需要澄清"""
        # 置信度过低
        if intent_result.confidence < 0.7:
            return True
        
        # 缺少关键参数
        if intent_result.is_edit_request:
            for intent in intent_result.edit_intents:
                if not intent.get('target') or not intent.get('parameters'):
                    return True
        
        # 存在歧义
        if self._has_ambiguity(intent_result, context):
            return True
        
        return False
```

### 4. 响应生成器 (ResponseGenerator)

```python
class ResponseGenerator:
    """响应生成器"""
    
    def __init__(self):
        self.llm_service = LLMService()
        self.response_templates = self._load_response_templates()
    
    async def generate_confirmation_response(self, edit_plan, intent_result) -> AgentResponse:
        """生成确认响应"""
        
        # 构建确认提示词
        prompt = f"""
        用户想要进行以下视频编辑操作：
        {self._format_edit_plan(edit_plan)}
        
        请生成一个友好的确认消息，包括：
        1. 总结用户的编辑需求
        2. 说明将要执行的操作
        3. 预估的处理时间
        4. 询问用户是否确认执行
        
        要求：语言自然、专业、友好
        """
        
        response_text = await self.llm_service.generate_response(prompt)
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="confirmation",
            content=response_text,
            metadata={
                "edit_plan": edit_plan.to_dict(),
                "estimated_time": edit_plan.estimated_duration
            },
            requires_confirmation=True
        )
    
    async def generate_execution_start_response(self, edit_plan) -> AgentResponse:
        """生成执行开始响应"""
        response_text = f"好的，我开始为您处理视频编辑。预计需要 {edit_plan.estimated_duration} 秒，请稍等..."
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="progress",
            content=response_text,
            metadata={
                "status": "started",
                "estimated_duration": edit_plan.estimated_duration
            }
        )
    
    async def generate_suggestion_response(self, suggestions: List[str]) -> AgentResponse:
        """生成建议响应"""
        prompt = f"""
        基于当前的视频编辑项目，为用户提供以下建议：
        {suggestions}
        
        请生成一个自然、有帮助的响应，解释这些建议的价值。
        """
        
        response_text = await self.llm_service.generate_response(prompt)
        
        return AgentResponse(
            response_id=str(uuid.uuid4()),
            response_type="suggestion",
            content=response_text,
            metadata={"suggestions": suggestions},
            suggestions=suggestions
        )
```

## 🔄 Agent交互流程

### 典型对话流程
```
用户: "把第一段的背景音乐换成更轻松的"
Agent: "我理解您想要替换第一段的背景音乐。我为您推荐几种轻松风格的音乐：
       1. 轻快的钢琴曲
       2. 温和的吉他音乐  
       3. 自然环境音
       您希望选择哪种风格？"

用户: "钢琴曲"
Agent: "好的，我将为第一段(0-15秒)替换为轻快的钢琴背景音乐。
       预计处理时间：30秒
       是否确认执行？"

用户: "确认"
Agent: "开始处理中...已完成20%...已完成50%...处理完成！
       您可以预览效果，如果满意可以继续其他编辑。"
```

---

**这个Agent框架设计充分考虑了用户体验和技术实现的平衡，能够提供智能、自然的视频编辑交互体验。**
