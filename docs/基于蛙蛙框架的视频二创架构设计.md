# 基于蛙蛙多模态框架的视频二创平台架构设计

## 🎯 核心设计理念

借鉴蛙蛙多模态Graph V2框架的优秀设计，采用**极简双节点架构 + Agent-as-Tool模式**，将视频二创的复杂流程封装为专家工具。

### 架构对比

**蛙蛙框架模式**:
```
用户输入 → Master Agent → 智能路由 → 执行引擎（可选）→ 结果输出
```

**视频二创平台模式**:
```
用户视频+编辑需求 → Master Agent → 智能路由 → 执行引擎（可选）→ 二创视频
```

## 🏗️ 核心架构设计

### 1. 双节点架构

```mermaid
graph TB
    subgraph "视频二创平台核心架构"
        A[用户输入<br/>视频+编辑需求] --> B[master_agent_node]
        B --> C{should_continue路由判断}
        C -->|should_use_execution_engine=true<br/>且plan未完成| D[execution_engine_node]
        C -->|AIMessage且无tool_calls| E[END]
        C -->|其他情况| B
        D --> F[循环调用Master Agent]
        F --> G[更新步骤状态]
        G --> H{计划完成?}
        H -->|否| F
        H -->|是| E
    end

    subgraph "should_use_execution_engine判断规则"
        I[简单编辑 → False<br/>直接处理]
        J[复杂编辑 → True<br/>多步骤流程]
        K[模板编辑 → True<br/>预定义流程]
    end

    style B fill:#e1f5fe
    style D fill:#e8f5e8
    style C fill:#fff3e0
    style E fill:#c8e6c9
```

### 2. Master Agent工具体系

```mermaid
graph TB
    subgraph "Master Agent核心"
        MA[Master Agent<br/>ReAct模式]
    end

    subgraph "专家工具层 (Agent-as-Tool)"
        VUE[Video Understanding Expert<br/>视频理解专家]
        VEE[Video Editing Expert<br/>视频编辑专家]
        VRE[Video Rendering Expert<br/>视频渲染专家]
        AE[Audio Expert<br/>音频处理专家]
        TE[Text Expert<br/>文本处理专家]
    end

    subgraph "状态管理工具"
        GCP[get_current_plan<br/>获取计划状态]
        GPS[get_next_pending_step<br/>获取下一步骤]
        USS[update_step_status<br/>更新步骤状态]
        RSI[resolve_step_inputs<br/>解析步骤输入]
    end

    subgraph "模板系统工具"
        RT[recommend_template<br/>推荐编辑模板]
        CPT[create_plan_from_template<br/>从模板创建计划]
        GAT[get_available_templates<br/>获取可用模板]
    end

    subgraph "规划工具"
        PT[planner_tool<br/>创建编辑计划]
        RVT[reviser_tool<br/>修订计划]
    end

    MA --> VUE
    MA --> VEE
    MA --> VRE
    MA --> AE
    MA --> TE
    MA --> GCP
    MA --> GPS
    MA --> USS
    MA --> RSI
    MA --> RT
    MA --> CPT
    MA --> GAT
    MA --> PT
    MA --> RVT

    style MA fill:#f3e5f5
    style VUE fill:#e8f5e8
    style VEE fill:#fff3e0
    style VRE fill:#fce4ec
```

## 🛠️ 专家工具设计

### 1. Video Understanding Expert (视频理解专家)

**功能**: 将你现有的视频理解工作流封装为专家工具

```python
@ToolRegistry.register(ToolCategory.ANALYZER, "video_understanding_expert")
class VideoUnderstandingExpert(BaseTool):
    """视频理解专家 - 封装现有视频理解工作流"""
    
    def __init__(self):
        super().__init__()
        self.description = "专业的视频理解和分析工具，生成结构化JSON"
        self.input_keys = ["video_path", "video_url", "analysis_request"]
        self.output_keys = ["video_json", "video_metadata", "analysis_summary"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑 - 调用现有的视频理解工作流"""
        try:
            # 调用你现有的视频理解工作流
            from src.workflows.video_understanding import run_video_understanding_v2
            
            result = run_video_understanding_v2(
                video_path=input_data.get("video_path"),
                video_url=input_data.get("video_url"),
                user_request=input_data.get("analysis_request", "请详细分析这个视频")
            )
            
            if result and not result.get("error_message"):
                return {
                    "success": True,
                    "video_json": result.get("video_script_json"),
                    "video_metadata": result.get("video_metadata"),
                    "analysis_summary": result.get("script_summary"),
                    "assets": {
                        "json_file": result.get("output_file_path"),
                        "cos_url": result.get("cos_url")
                    }
                }
            else:
                return {
                    "error_message": result.get("error_message", "视频理解失败")
                }
                
        except Exception as e:
            return {"error_message": f"视频理解专家执行失败: {str(e)}"}
```

### 2. Video Editing Expert (视频编辑专家)

**功能**: 基于结构化JSON和用户意图进行智能编辑

```python
@ToolRegistry.register(ToolCategory.PROCESSOR, "video_editing_expert")
class VideoEditingExpert(BaseTool):
    """视频编辑专家 - 智能编辑视频内容"""
    
    def __init__(self):
        super().__init__()
        self.description = "基于视频JSON结构和用户意图进行智能编辑"
        self.input_keys = ["video_json", "edit_instructions", "edit_type"]
        self.output_keys = ["edited_json", "edit_summary", "changed_segments"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心编辑逻辑"""
        try:
            video_json = input_data["video_json"]
            edit_instructions = input_data["edit_instructions"]
            edit_type = input_data.get("edit_type", "content_replacement")
            
            # 解析编辑意图
            edit_plan = self._parse_edit_instructions(edit_instructions, video_json)
            
            # 执行编辑操作
            edited_json = self._apply_edits(video_json, edit_plan)
            
            return {
                "success": True,
                "edited_json": edited_json,
                "edit_summary": f"完成{len(edit_plan)}项编辑操作",
                "changed_segments": [op["target_segment"] for op in edit_plan],
                "edit_operations": edit_plan
            }
            
        except Exception as e:
            return {"error_message": f"视频编辑专家执行失败: {str(e)}"}
    
    def _parse_edit_instructions(self, instructions: str, video_json: Dict) -> List[Dict]:
        """解析编辑指令为具体操作"""
        # 使用LLM解析自然语言编辑指令
        # 返回结构化的编辑操作列表
        pass
    
    def _apply_edits(self, video_json: Dict, edit_plan: List[Dict]) -> Dict:
        """应用编辑操作到视频JSON"""
        # 实现具体的JSON编辑逻辑
        pass
```

### 3. Video Rendering Expert (视频渲染专家)

**功能**: 将编辑后的JSON渲染为实际视频

```python
@ToolRegistry.register(ToolCategory.OUTPUT, "video_rendering_expert")
class VideoRenderingExpert(BaseTool):
    """视频渲染专家 - 将JSON转换为实际视频"""
    
    def __init__(self):
        super().__init__()
        self.description = "将编辑后的视频JSON渲染为实际视频文件"
        self.input_keys = ["edited_json", "render_config", "output_format"]
        self.output_keys = ["rendered_video_path", "render_summary"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心渲染逻辑"""
        try:
            edited_json = input_data["edited_json"]
            render_config = input_data.get("render_config", {})
            output_format = input_data.get("output_format", "mp4")
            
            # 调用视频渲染工作流
            rendered_path = self._render_video_from_json(
                edited_json, render_config, output_format
            )
            
            return {
                "success": True,
                "rendered_video_path": rendered_path,
                "render_summary": "视频渲染完成",
                "assets": {
                    "video": rendered_path
                }
            }
            
        except Exception as e:
            return {"error_message": f"视频渲染专家执行失败: {str(e)}"}
    
    def _render_video_from_json(self, json_data: Dict, config: Dict, format: str) -> str:
        """从JSON渲染视频的核心逻辑"""
        # 实现JSON到视频的转换逻辑
        # 这里会调用各种AI服务：TTS、图像生成、视频合成等
        pass
```

## 🎯 执行流程设计

### 1. 简单编辑流程 (Master Agent直接处理)

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant VUE as Video Understanding Expert
    participant VEE as Video Editing Expert
    participant VRE as Video Rendering Expert

    U->>MA: "把这个视频的背景音乐换成轻松的"
    MA->>VUE: video_understanding_expert(video_url)
    VUE-->>MA: 返回视频JSON结构
    
    MA->>VEE: video_editing_expert(json, "换背景音乐")
    VEE-->>MA: 返回编辑后的JSON
    
    MA->>VRE: video_rendering_expert(edited_json)
    VRE-->>MA: 返回渲染后的视频
    
    MA-->>U: 🎬 编辑完成的视频
```

### 2. 复杂编辑流程 (执行引擎驱动)

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant PT as Planner Tool
    participant EE as 执行引擎
    participant VUE as Video Understanding Expert
    participant VEE as Video Editing Expert
    participant AE as Audio Expert
    participant VRE as Video Rendering Expert

    U->>MA: "制作这个视频的多语言版本，包括英文和日文"
    MA->>PT: planner_tool("多语言视频制作")
    PT-->>MA: 返回4步计划
    
    Note over MA,EE: should_continue → execution_engine
    
    EE->>MA: 步骤1: 理解原视频
    MA->>VUE: video_understanding_expert(video)
    VUE-->>MA: 返回视频JSON
    
    EE->>MA: 步骤2: 生成英文配音
    MA->>AE: audio_expert("生成英文配音")
    AE-->>MA: 返回英文音频
    
    EE->>MA: 步骤3: 生成日文配音
    MA->>AE: audio_expert("生成日文配音")
    AE-->>MA: 返回日文音频
    
    EE->>MA: 步骤4: 渲染多语言版本
    MA->>VRE: video_rendering_expert(多语言JSON)
    VRE-->>MA: 返回多个视频文件
    
    EE-->>U: 🌍 多语言视频制作完成
```

### 3. 模板驱动流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant MA as Master Agent
    participant TT as 模板工具
    participant EE as 执行引擎

    U->>MA: "制作一个教学视频的本地化版本"
    MA->>TT: recommend_template("教学视频本地化")
    TT-->>MA: 推荐education_localization模板
    
    MA->>TT: create_plan_from_template(education_localization)
    TT-->>MA: 返回标准化5步计划
    
    Note over MA,EE: should_continue → execution_engine (模板计划)
    
    EE->>EE: 按模板步骤执行
    EE-->>U: 🎓 本地化教学视频完成
```

## 📋 状态管理设计

### 视频二创状态结构

```python
class VideoCreationState(TypedDict):
    """视频二创状态"""
    messages: Annotated[list, add_messages]  # 消息历史
    plan: Optional[EnhancedPlan]             # 执行计划
    
    # 视频相关状态
    original_video: Optional[Dict[str, Any]]  # 原始视频信息
    video_json: Optional[Dict[str, Any]]      # 视频结构JSON
    edited_json: Optional[Dict[str, Any]]     # 编辑后的JSON
    rendered_videos: Optional[List[str]]      # 渲染后的视频路径
    
    # 编辑相关状态
    edit_instructions: Optional[str]          # 用户编辑指令
    edit_history: Optional[List[Dict]]        # 编辑历史
    current_edit_step: Optional[str]          # 当前编辑步骤
    
    # 模板系统
    template_id: Optional[str]
    template_params: Optional[Dict[str, Any]]
    template_mode: bool
    
    # 执行引擎
    current_step: Optional[str]               # 当前执行步骤ID
```

## 🎨 预设模板设计

### 教学视频本地化模板

```python
education_localization_template = PlanTemplate(
    template_id="education_localization",
    name="教学视频本地化",
    description="将教学视频快速本地化为多语言版本",
    category="video_localization",
    tags=["education", "localization", "multilingual"],
    
    parameters={
        "target_languages": ParameterSchema(
            type=ParameterType.LIST,
            required=True,
            description="目标语言列表，如['en', 'ja', 'ko']"
        ),
        "voice_style": ParameterSchema(
            type=ParameterType.STRING,
            default="professional",
            allowed_values=["professional", "friendly", "energetic"],
            description="配音风格"
        )
    },
    
    step_templates=[
        StepTemplate(
            template_step_id="understand_original",
            name="理解原视频",
            description_template="分析原始教学视频的内容结构",
            tool_to_use="video_understanding_expert",
            step_type=StepType.DATA_COLLECTION
        ),
        StepTemplate(
            template_step_id="extract_speech_content",
            name="提取语音内容",
            description_template="提取视频中的语音和文字内容",
            tool_to_use="text_expert",
            step_type=StepType.CONTENT_EXTRACTION,
            dependencies=["understand_original"]
        ),
        StepTemplate(
            template_step_id="generate_multilingual_audio",
            name="生成多语言配音",
            description_template="为{target_languages}生成{voice_style}风格的配音",
            tool_to_use="audio_expert",
            step_type=StepType.CONTENT_GENERATION,
            dependencies=["extract_speech_content"]
        ),
        StepTemplate(
            template_step_id="create_localized_versions",
            name="创建本地化版本",
            description_template="为每种语言创建完整的视频版本",
            tool_to_use="video_rendering_expert",
            step_type=StepType.CONTENT_SYNTHESIS,
            dependencies=["generate_multilingual_audio"]
        )
    ]
)
```

## 🚀 实现优势

### 1. 复用现有能力
- **视频理解工作流** → Video Understanding Expert
- **现有工具系统** → 各种专家工具的底层实现
- **LangGraph架构** → 保持原有的工作流编排优势

### 2. 简化架构复杂度
- 只有2个核心节点，易于理解和维护
- 智能路由自动判断执行路径
- Agent-as-Tool模式高度模块化

### 3. 提升扩展性
- 新功能只需添加新的专家工具
- 模板系统标准化复杂流程
- 统一的状态管理和错误处理

### 4. 优化用户体验
- 自然语言交互，降低使用门槛
- 智能推荐编辑模板
- 实时状态反馈和进度显示

---

**这个设计充分借鉴了蛙蛙框架的优秀理念，将你现有的视频理解能力和工具系统完美整合，形成了一个简洁而强大的视频二创平台架构！**
