# 开发指南

## 🏗️ 架构概览

智能图片生成助手采用模块化架构，主要包含以下组件：

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户请求      │ -> │   智能规划器     │ -> │   工具选择器    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                ↓
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   结果输出      │ <- │   动态执行引擎   │ <- │   工具注册中心  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🔧 工具开发

### 工具基础类

所有工具都继承自 `BaseTool`：

```python
from tools.base import BaseTool, ToolCategory, ToolRegistry

class MyTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.name = "my_tool"                    # 工具名称
        self.category = ToolCategory.GENERATOR   # 工具类别
        self.description = "我的工具描述"         # 工具描述
        self.input_keys = ["input_data"]         # 输入字段
        self.output_keys = ["output_data"]       # 输出字段
        self.dependencies = []                   # 依赖的其他工具
    
    def process(self, input_data):
        """核心处理逻辑"""
        # 实现您的处理逻辑
        result = self.do_something(input_data["input_data"])
        return {"output_data": result}
```

### 工具类别

```python
class ToolCategory(Enum):
    INPUT = "input"                    # 输入处理
    ANALYZER = "analyzer"              # 内容分析
    STYLE_CONVERTER = "style_converter" # 风格转换
    CONTENT_PROCESSOR = "content_processor" # 内容处理
    GENERATOR = "generator"            # 生成工具
    OUTPUT = "output"                  # 输出工具
```

### 工具注册

使用装饰器自动注册工具：

```python
@ToolRegistry.register(ToolCategory.STYLE_CONVERTER, "meme")
class MemeStyleTool(BaseTool):
    # 工具实现
    pass
```

### 完整示例：Meme风格转换工具

```python
"""
Meme风格转换工具
"""
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm

@ToolRegistry.register(ToolCategory.STYLE_CONVERTER, "meme")
class MemeStyleTool(BaseTool):
    """Meme风格转换工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将文本转换为Meme风格"
        self.input_keys = ["raw_content"]
        self.output_keys = ["processed_content", "style_metadata"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换为Meme风格"""
        
        content = input_data.get("raw_content")
        if not content:
            return {"error_message": "缺少原始内容"}
        
        try:
            llm = get_llm()
            
            # 构建Meme风格提示词
            prompt = f"""
            将以下文本转换为网络Meme风格：
            1. 使用夸张幽默的表达
            2. 添加网络流行语
            3. 保持简洁有趣
            
            原文：{content}
            
            Meme风格：
            """
            
            response = llm.invoke(prompt)
            meme_text = response.content
            
            return {
                "processed_content": meme_text,
                "style_metadata": {
                    "style": "meme",
                    "original_length": len(content),
                    "processed_length": len(meme_text)
                }
            }
            
        except Exception as e:
            return {"error_message": f"Meme风格转换失败: {str(e)}"}

def test_meme_style_tool():
    """测试Meme风格工具"""
    tool = MemeStyleTool()
    
    test_input = {"raw_content": "今天天气很好"}
    result = tool.process(test_input)
    
    assert "processed_content" in result
    assert "style_metadata" in result
    print("✅ MemeStyleTool 测试通过")

if __name__ == "__main__":
    test_meme_style_tool()
```

## 📋 预设流程开发

### 创建预设流程

```python
"""
商务风格预设流程
"""
from typing import Dict, Any, List
from core.state import UniversalState
from tools.base.tool_registry import ToolRegistry

class BusinessPresetGraph:
    """商务风格预设流程"""
    
    def __init__(self):
        self.name = "business_preset"
        self.description = "商务风格文档生成"
        self.tools = [
            "input.text_input",
            "style_converter.business",
            "content_processor.formal",
            "generator.pdf",
            "output.document"
        ]
    
    def execute(self, initial_state: UniversalState) -> UniversalState:
        """执行商务预设流程"""
        print(f"💼 执行商务预设流程: {self.name}")
        
        current_state = initial_state.copy()
        
        for tool_name in self.tools:
            print(f"🔧 执行工具: {tool_name}")
            
            tool = ToolRegistry.create_tool(tool_name)
            if not tool:
                current_state["error_message"] = f"工具不存在: {tool_name}"
                break
            
            try:
                result = tool.execute(current_state)
                current_state.update(result)
                
                if result.get("error_message"):
                    break
                    
            except Exception as e:
                current_state["error_message"] = str(e)
                break
        
        return current_state
```

## 🧪 测试开发

### 单元测试模板

```python
def test_my_tool():
    """测试我的工具"""
    print("🧪 测试 MyTool")
    
    tool = MyTool()
    
    # 测试用例1：正常输入
    test_input = {"input_data": "测试数据"}
    result = tool.process(test_input)
    
    assert "output_data" in result
    assert result["output_data"] is not None
    print("✅ 测试1通过")
    
    # 测试用例2：空输入
    test_input2 = {}
    result2 = tool.process(test_input2)
    assert "error_message" in result2
    print("✅ 测试2通过")
    
    print("✅ MyTool 所有测试通过!\n")
```

### Mock LLM测试

```python
class MockLLM:
    """模拟LLM用于测试"""
    def invoke(self, prompt):
        class MockResponse:
            def __init__(self):
                self.content = "模拟LLM返回结果"
        return MockResponse()

def test_with_mock_llm():
    """使用Mock LLM测试"""
    tool = MyStyleTool()
    
    # 替换LLM
    original_get_llm = None
    try:
        import llm_services
        original_get_llm = llm_services.get_llm
        llm_services.get_llm = lambda: MockLLM()
        
        # 执行测试
        result = tool.process({"raw_content": "测试"})
        assert "processed_content" in result
        
    finally:
        # 恢复原始LLM
        if original_get_llm:
            llm_services.get_llm = original_get_llm
```

## 🔍 调试指南

### 启用详细日志

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 在工具中添加日志
def process(self, input_data):
    logging.debug(f"处理输入: {input_data}")
    result = self.do_something(input_data)
    logging.debug(f"处理结果: {result}")
    return result
```

### 状态检查

```python
def debug_state(state: UniversalState, step: str):
    """调试状态信息"""
    print(f"🔍 调试 {step}:")
    print(f"  当前步骤: {state.get('current_step')}")
    print(f"  原始内容: {state.get('raw_content', 'N/A')[:50]}...")
    print(f"  处理内容: {state.get('processed_content', 'N/A')[:50]}...")
    print(f"  错误信息: {state.get('error_message', 'N/A')}")
    print()
```

## 📈 性能优化

### 缓存机制

```python
from functools import lru_cache

class CachedTool(BaseTool):
    @lru_cache(maxsize=100)
    def expensive_operation(self, content):
        """缓存昂贵的操作"""
        return self.do_expensive_work(content)
    
    def process(self, input_data):
        content = input_data["content"]
        result = self.expensive_operation(content)
        return {"output": result}
```

### 异步处理

```python
import asyncio
from typing import List

class AsyncTool(BaseTool):
    async def async_process(self, input_data):
        """异步处理"""
        # 异步LLM调用
        result = await self.async_llm_call(input_data)
        return result
    
    def process(self, input_data):
        """同步接口包装"""
        return asyncio.run(self.async_process(input_data))
```

## 🛡️ 错误处理

### 标准错误处理模式

```python
def process(self, input_data):
    try:
        # 验证输入
        if not self.validate_input(input_data):
            return {"error_message": "输入验证失败"}
        
        # 执行处理
        result = self.do_processing(input_data)
        
        # 验证输出
        if not self.validate_output(result):
            return {"error_message": "输出验证失败"}
        
        return result
        
    except ValueError as e:
        return {"error_message": f"参数错误: {e}"}
    except Exception as e:
        return {"error_message": f"处理失败: {e}"}

def validate_input(self, input_data):
    """验证输入数据"""
    for key in self.input_keys:
        if key not in input_data:
            return False
    return True

def validate_output(self, output_data):
    """验证输出数据"""
    for key in self.output_keys:
        if key not in output_data:
            return False
    return True
```

## 📚 最佳实践

### 1. 工具设计原则
- **单一职责**: 每个工具只做一件事
- **输入验证**: 总是验证输入数据
- **错误处理**: 优雅处理所有异常
- **可测试性**: 提供完整的测试用例

### 2. 代码规范
- 使用类型提示
- 添加详细的文档字符串
- 遵循PEP 8代码风格
- 提供使用示例

### 3. 性能考虑
- 缓存重复计算
- 避免不必要的LLM调用
- 使用适当的数据结构
- 监控内存使用

### 4. 文档维护
- 更新工具列表
- 记录API变更
- 提供迁移指南
- 维护示例代码

## 🔄 发布流程

### 1. 开发阶段
```bash
# 创建功能分支
git checkout -b feature/new-tool

# 开发工具
# 编写测试
# 更新文档
```

### 2. 测试阶段
```bash
# 运行所有测试
python test_all_tools.py

# 端到端测试
python test_end_to_end.py

# 性能测试
python benchmark_tools.py
```

### 3. 集成阶段
```bash
# 合并到主分支
git checkout main
git merge feature/new-tool

# 更新版本号
# 创建发布标签
git tag v1.2.0
```

---

💡 **开发提示**: 
- 始终从现有工具开始学习
- 保持工具接口的一致性
- 优先考虑可测试性和可维护性
- 及时更新文档和示例