# 参数传递机制使用指南

## 🎯 概述

本系统使用**参数管理器**来统一管理各种工具的参数传递，使得预设流程可以方便地自定义生成效果。

## 📋 核心组件

### 1. ParameterManager (参数管理器)
- **位置**: `core/parameter_manager.py`
- **功能**: 管理预设参数配置，为不同工具准备正确的参数
- **预设**: xiaohongshu, meme, business

### 2. 工具参数类
- `ImageGenerationParams`: 图像生成参数
- `ImageEditParams`: 图像编辑参数  
- `StyleConversionParams`: 风格转换参数
- `HtmlGenerationParams`: HTML生成参数

## 🚀 使用方法

### 基础用法：使用预设参数

```python
from graphs.preset_graphs.meme_preset_v2 import MemePresetGraphV2
from core.state import UniversalState

preset = MemePresetGraphV2()
state = UniversalState(user_request="程序员看到bug时的表情")

# 使用默认meme预设参数
result = preset.execute(state)
```

### 高级用法：自定义参数

```python
# 自定义参数覆盖预设
custom_params = {
    "image_style": "cute",              # 图像风格：cute, humor, business, tech
    "image_size": "512x512",            # 图像尺寸：WIDTHxHEIGHT
    "meme_keywords": ["程序员", "bug"],   # Meme关键词
    "html_theme": "dark"                # HTML主题：dark, bright, default
}

result = preset.execute(state, custom_params)
```

## 🔧 参数详解

### 图像生成参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `image_style` | str | "humor" | 图像风格：humor, cute, business, tech, artistic |
| `image_size` | str | "810x810" | 图像尺寸，格式：宽x高 |
| `image_seed` | int | -1 | 随机种子，-1为随机 |
| `image_scale` | float | 3.0 | 提示词相关性（1.0-10.0）|

### 风格转换参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `meme_keywords` | list | ["搞笑","幽默"] | Meme关键词列表 |
| `tone` | str | "humor" | 语调：humor, casual, formal, professional |

### HTML生成参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `html_theme` | str | "default" | 主题：default, dark, bright, minimal |
| `custom_css` | dict | {} | 自定义CSS样式 |

## 📝 完整示例

### 示例1：可爱风格Meme

```python
custom_params = {
    "image_style": "cute",
    "image_size": "600x600", 
    "meme_keywords": ["可爱", "萌", "治愈"],
    "html_theme": "bright"
}

result = preset.execute(state, custom_params)
```

### 示例2：专业商务Meme

```python
custom_params = {
    "image_style": "business",
    "image_size": "1200x800",
    "tone": "professional", 
    "html_theme": "minimal"
}

result = preset.execute(state, custom_params)
```

### 示例3：深度自定义

```python
custom_params = {
    "image_style": "tech",
    "image_size": "1024x1024",
    "image_scale": 4.0,  # 更严格遵循提示词
    "meme_keywords": ["程序员", "代码", "debug", "加班"],
    "html_theme": "dark",
    "custom_css": {
        "border-radius": "20px",
        "box-shadow": "0 4px 8px rgba(0,0,0,0.3)",
        "background": "linear-gradient(45deg, #FF6B6B, #4ECDC4)"
    }
}

result = preset.execute(state, custom_params)
```

## 🔍 参数传递流程

```
用户请求 + 自定义参数
    ↓
预设流程 (MemePresetGraphV2)
    ↓
参数管理器 (ParameterManager)
    ↓
为每个工具准备特定参数
    ↓
工具执行 (传入准备好的参数)
    ↓
返回结果
```

## ⚙️ 添加新预设

### 1. 在ParameterManager中添加预设

```python
# 在 core/parameter_manager.py 中添加
"my_preset": {
    "style": StyleConversionParams(
        target_style="my_style",
        tone="custom"
    ),
    "image": ImageGenerationParams(
        style="my_image_style",
        width=800,
        height=600
    ),
    "html": HtmlGenerationParams(
        template_type="my_template",
        background_color="#CUSTOM"
    )
}
```

### 2. 创建新的预设流程

```python
class MyPresetGraph:
    def __init__(self):
        self.param_manager = get_param_manager()
    
    def execute(self, state, custom_params=None):
        # 使用 self.param_manager.prepare_tool_params("tool_name", "my_preset")
        pass
```

## 🎯 最佳实践

1. **使用预设作为基础**：先选择合适的预设（meme, xiaohongshu, business），再用自定义参数微调
2. **参数命名规范**：使用清晰的参数名，如 `image_style` 而不是 `style`
3. **参数验证**：在PresetGraph中验证自定义参数的有效性
4. **文档更新**：添加新参数时及时更新此文档

## 🤔 FAQ

### Q: 如何查看当前可用的预设？
A: 使用 `param_manager.presets.keys()` 查看所有预设名称

### Q: 如何查看某个预设的默认参数？
A: 使用 `param_manager.get_preset_params("preset_name")` 查看

### Q: 自定义参数会覆盖所有默认参数吗？
A: 不会，自定义参数只覆盖指定的字段，其他字段使用默认值

### Q: 可以同时使用多个预设吗？
A: 目前一个PresetGraph只能使用一个预设，但可以通过自定义参数混合不同预设的特性

---

💡 **提示**: 这个参数系统让你可以用简单的参数控制复杂的生成流程，同时保持代码的清晰和可维护性。