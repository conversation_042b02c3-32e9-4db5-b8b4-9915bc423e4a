# XHS 智能视频二创平台 - 开发指南

## 🚀 快速开始

### 环境要求
- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- FFmpeg
- Git

### 本地开发环境搭建

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/xhs-video-creator.git
cd xhs-video-creator
```

#### 2. 后端环境设置
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 安装开发依赖
pip install -r requirements-dev.txt
```

#### 3. 前端环境设置
```bash
cd web
npm install
```

#### 4. 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

#### 5. 数据库初始化
```bash
# 启动数据库服务
docker-compose up -d postgres redis

# 运行数据库迁移
alembic upgrade head
```

#### 6. 启动开发服务
```bash
# 启动后端服务
uvicorn src.api.main:app --reload --port 8000

# 启动前端服务 (新终端)
cd web && npm run dev
```

访问 http://localhost:3000 查看应用

## 🏗️ 开发规范

### 代码规范

#### Python代码规范
```python
# 使用类型注解
def process_video(video_path: str, options: Dict[str, Any]) -> VideoResult:
    """处理视频文件
    
    Args:
        video_path: 视频文件路径
        options: 处理选项
        
    Returns:
        VideoResult: 处理结果
        
    Raises:
        VideoProcessingError: 处理失败时抛出
    """
    pass

# 使用dataclass定义数据结构
@dataclass
class VideoMetadata:
    duration: float
    resolution: Tuple[int, int]
    fps: int
    format: str
```

#### 代码格式化工具
```bash
# 安装格式化工具
pip install black isort flake8 mypy

# 格式化代码
black src/
isort src/
flake8 src/
mypy src/
```

#### Git提交规范
```bash
# 提交信息格式
<type>(<scope>): <description>

# 示例
feat(video): add video upload functionality
fix(api): resolve authentication issue
docs(readme): update installation guide
```

### 工具开发规范

#### 1. 工具基础结构
```python
from src.tools.base.tool_interface import BaseTool, ToolCategory
from src.tools.base.tool_registry import ToolRegistry

@ToolRegistry.register(ToolCategory.PROCESSOR, "your_tool_name")
class YourTool(BaseTool):
    """工具描述"""
    
    def __init__(self):
        super().__init__()
        self.description = "详细的工具功能描述"
        self.input_keys = ["input_field1", "input_field2"]
        self.output_keys = ["output_field1", "output_field2"]
        self.dependencies = []  # 依赖的其他工具
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑"""
        try:
            # 1. 输入验证
            self._validate_input(input_data)
            
            # 2. 核心处理
            result = self._core_process(input_data)
            
            # 3. 输出验证
            self._validate_output(result)
            
            return result
            
        except Exception as e:
            return {"error_message": f"{self.name} 处理失败: {str(e)}"}
    
    def _core_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """实现具体的处理逻辑"""
        # TODO: 实现处理逻辑
        pass
```

#### 2. 工具测试规范
```python
import pytest
from unittest.mock import Mock, patch
from src.tools.your_module.your_tool import YourTool

class TestYourTool:
    def setup_method(self):
        self.tool = YourTool()
    
    def test_process_success(self):
        """测试正常处理流程"""
        input_data = {"input_field1": "test_value"}
        result = self.tool.process(input_data)
        
        assert "error_message" not in result
        assert "output_field1" in result
    
    def test_process_invalid_input(self):
        """测试无效输入处理"""
        input_data = {}  # 缺少必需字段
        result = self.tool.process(input_data)
        
        assert "error_message" in result
    
    @pytest.mark.asyncio
    async def test_async_process(self):
        """测试异步处理"""
        if hasattr(self.tool, 'process_async'):
            input_data = {"input_field1": "test_value"}
            result = await self.tool.process_async(input_data)
            assert "output_field1" in result
```

### API开发规范

#### 1. 路由定义
```python
from fastapi import APIRouter, Depends, HTTPException
from src.api.models.project import ProjectCreate, ProjectResponse
from src.core.project_manager import ProjectManager

router = APIRouter(prefix="/api/v1/projects", tags=["projects"])

@router.post("/", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    project_manager: ProjectManager = Depends(get_project_manager)
) -> ProjectResponse:
    """创建新项目"""
    try:
        project = await project_manager.create_project(project_data)
        return ProjectResponse.from_orm(project)
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
```

#### 2. 错误处理
```python
from src.api.exceptions import VideoProcessingError, ProjectNotFoundError

@router.post("/{project_id}/process")
async def process_video(project_id: str):
    try:
        result = await process_video_logic(project_id)
        return result
    except ProjectNotFoundError:
        raise HTTPException(status_code=404, detail="项目不存在")
    except VideoProcessingError as e:
        raise HTTPException(status_code=400, detail=f"视频处理失败: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")
```

## 🧪 测试指南

### 测试结构
```
tests/
├── unit/                    # 单元测试
│   ├── test_tools/         # 工具测试
│   ├── test_core/          # 核心模块测试
│   └── test_api/           # API测试
├── integration/            # 集成测试
│   ├── test_workflows/     # 工作流测试
│   └── test_services/      # 服务集成测试
└── e2e/                    # 端到端测试
    └── test_user_flows/    # 用户流程测试
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 生成测试覆盖率报告
pytest --cov=src --cov-report=html
```

### 测试数据管理
```python
# conftest.py
import pytest
from src.core.database import get_db_session

@pytest.fixture
def db_session():
    """测试数据库会话"""
    session = get_test_db_session()
    yield session
    session.rollback()
    session.close()

@pytest.fixture
def sample_video_data():
    """示例视频数据"""
    return {
        "video_url": "https://example.com/test.mp4",
        "metadata": {
            "duration": 120.5,
            "resolution": "1920x1080"
        }
    }
```

## 📦 部署指南

### Docker部署

#### 1. 构建镜像
```bash
# 构建后端镜像
docker build -t video-creator-api .

# 构建前端镜像
cd web && docker build -t video-creator-web .
```

#### 2. 使用Docker Compose
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f api
```

### 生产环境部署

#### 1. 环境变量配置
```bash
# 生产环境变量
export DATABASE_URL="********************************/dbname"
export REDIS_URL="redis://host:6379/0"
export SECRET_KEY="your-secret-key"
export AI_API_KEY="your-ai-api-key"
```

#### 2. 数据库迁移
```bash
# 生产环境数据库迁移
alembic upgrade head
```

#### 3. 服务启动
```bash
# 使用Gunicorn启动API服务
gunicorn src.api.main:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用Nginx代理前端
nginx -c /path/to/nginx.conf
```

## 🔧 调试指南

### 日志配置
```python
import logging
from src.utils.logger import setup_logger

# 设置日志级别
logger = setup_logger(__name__, level=logging.DEBUG)

# 记录调试信息
logger.debug("调试信息")
logger.info("一般信息")
logger.warning("警告信息")
logger.error("错误信息")
```

### 性能分析
```python
import cProfile
import pstats

# 性能分析装饰器
def profile_function(func):
    def wrapper(*args, **kwargs):
        pr = cProfile.Profile()
        pr.enable()
        result = func(*args, **kwargs)
        pr.disable()
        
        stats = pstats.Stats(pr)
        stats.sort_stats('cumulative')
        stats.print_stats(10)
        
        return result
    return wrapper
```

### 常见问题排查

#### 1. 视频处理失败
```bash
# 检查FFmpeg安装
ffmpeg -version

# 检查视频文件格式
ffprobe input.mp4

# 查看处理日志
tail -f logs/video_processing.log
```

#### 2. API响应慢
```bash
# 检查数据库连接
psql -h host -U user -d dbname

# 检查Redis连接
redis-cli ping

# 监控API性能
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/v1/health"
```

## 📚 开发资源

### 文档链接
- [项目架构文档](./项目架构文档.md)
- [API参考文档](./api/README.md)
- [工具开发指南](./工具开发最佳实践.md)
- [部署运维指南](./deployment/README.md)

### 开发工具推荐
- **IDE**: PyCharm Professional / VS Code
- **API测试**: Postman / Insomnia
- **数据库管理**: DBeaver / pgAdmin
- **版本控制**: Git / GitHub Desktop
- **容器管理**: Docker Desktop

### 学习资源
- [LangGraph官方文档](https://langchain-ai.github.io/langgraph/)
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [React官方文档](https://react.dev/)
- [FFmpeg文档](https://ffmpeg.org/documentation.html)

---

**这个开发指南提供了完整的开发环境搭建、编码规范、测试方法和部署流程，帮助团队快速上手项目开发。**
