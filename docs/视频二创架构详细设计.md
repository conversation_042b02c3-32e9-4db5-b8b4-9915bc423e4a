# 视频二创平台架构详细设计

## 🎯 架构设计原则

### 1. 分层解耦原则
- **用户交互层**: 处理用户界面和API交互
- **业务编排层**: 处理业务逻辑和流程编排
- **核心引擎层**: 提供核心处理能力
- **数据管理层**: 管理状态、缓存和持久化
- **工具生态层**: 提供可插拔的处理工具

### 2. 状态驱动原则
- 所有操作基于项目状态进行
- 支持状态版本控制和回滚
- 实现增量更新和差异计算

### 3. 异步并发原则
- 支持多轨道并行处理
- 异步任务队列管理
- 实时进度反馈

## 🏗️ 核心组件设计

### 1. 项目状态管理 (ProjectState)

```python
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class VideoTrack:
    """视频轨道"""
    track_id: str
    track_type: str  # "video", "audio", "subtitle"
    segments: List[Dict[str, Any]]
    effects: List[Dict[str, Any]]
    metadata: Dict[str, Any]

@dataclass
class EditOperation:
    """编辑操作"""
    operation_id: str
    operation_type: str  # "replace", "add", "remove", "modify"
    target_track: str
    target_segment: Optional[str]
    parameters: Dict[str, Any]
    timestamp: datetime
    status: str  # "pending", "processing", "completed", "failed"

class ProjectState:
    """项目状态管理器"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.version = 1
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
        
        # 核心数据结构
        self.video_metadata: Dict[str, Any] = {}
        self.tracks: Dict[str, VideoTrack] = {}
        self.timeline: Dict[str, Any] = {}
        self.global_settings: Dict[str, Any] = {}
        
        # 编辑历史
        self.edit_history: List[EditOperation] = []
        self.pending_operations: List[EditOperation] = []
        
        # 资源管理
        self.assets: Dict[str, str] = {}  # asset_id -> file_path
        self.cache_keys: Dict[str, str] = {}  # operation_id -> cache_key
    
    def add_track(self, track: VideoTrack) -> None:
        """添加轨道"""
        self.tracks[track.track_id] = track
        self._update_version()
    
    def apply_operation(self, operation: EditOperation) -> bool:
        """应用编辑操作"""
        try:
            # 执行操作逻辑
            self._execute_operation(operation)
            
            # 更新历史
            operation.status = "completed"
            self.edit_history.append(operation)
            
            # 更新版本
            self._update_version()
            return True
            
        except Exception as e:
            operation.status = "failed"
            operation.error_message = str(e)
            return False
    
    def get_state_snapshot(self) -> Dict[str, Any]:
        """获取状态快照"""
        return {
            "project_id": self.project_id,
            "version": self.version,
            "video_metadata": self.video_metadata,
            "tracks": {k: v.__dict__ for k, v in self.tracks.items()},
            "timeline": self.timeline,
            "global_settings": self.global_settings,
            "edit_history": [op.__dict__ for op in self.edit_history],
            "assets": self.assets
        }
    
    def _update_version(self):
        """更新版本号"""
        self.version += 1
        self.updated_at = datetime.now()
    
    def _execute_operation(self, operation: EditOperation):
        """执行具体的编辑操作"""
        # 根据操作类型执行相应逻辑
        if operation.operation_type == "replace":
            self._handle_replace_operation(operation)
        elif operation.operation_type == "add":
            self._handle_add_operation(operation)
        # ... 其他操作类型
```

### 2. 编辑会话管理器 (SessionManager)

```python
import asyncio
from typing import Dict, Optional
import uuid

class EditSession:
    """编辑会话"""
    
    def __init__(self, session_id: str, project_id: str):
        self.session_id = session_id
        self.project_id = project_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.is_active = True
        
        # 会话状态
        self.current_operations: List[EditOperation] = []
        self.websocket_connections: List[Any] = []
        
    async def add_operation(self, operation: EditOperation):
        """添加编辑操作"""
        self.current_operations.append(operation)
        self.last_activity = datetime.now()
        
        # 通知所有连接的客户端
        await self._broadcast_update({
            "type": "operation_added",
            "operation": operation.__dict__
        })
    
    async def _broadcast_update(self, message: Dict[str, Any]):
        """广播更新到所有客户端"""
        for ws in self.websocket_connections:
            try:
                await ws.send_json(message)
            except:
                # 移除失效连接
                self.websocket_connections.remove(ws)

class SessionManager:
    """会话管理器"""
    
    def __init__(self):
        self.sessions: Dict[str, EditSession] = {}
        self.project_sessions: Dict[str, List[str]] = {}  # project_id -> session_ids
    
    def create_session(self, project_id: str) -> str:
        """创建编辑会话"""
        session_id = str(uuid.uuid4())
        session = EditSession(session_id, project_id)
        
        self.sessions[session_id] = session
        
        if project_id not in self.project_sessions:
            self.project_sessions[project_id] = []
        self.project_sessions[project_id].append(session_id)
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[EditSession]:
        """获取会话"""
        return self.sessions.get(session_id)
    
    async def cleanup_inactive_sessions(self):
        """清理非活跃会话"""
        current_time = datetime.now()
        inactive_sessions = []
        
        for session_id, session in self.sessions.items():
            if (current_time - session.last_activity).seconds > 3600:  # 1小时超时
                inactive_sessions.append(session_id)
        
        for session_id in inactive_sessions:
            await self._remove_session(session_id)
```

### 3. 意图解析引擎 (IntentParser)

```python
from enum import Enum
from typing import List, Dict, Any
import json

class EditIntentType(Enum):
    REPLACE_CONTENT = "replace_content"
    ADD_EFFECT = "add_effect"
    MODIFY_STYLE = "modify_style"
    ADJUST_TIMING = "adjust_timing"
    TRANSLATE_CONTENT = "translate_content"

class EditIntent:
    """编辑意图"""
    
    def __init__(self, intent_type: EditIntentType, target: str, parameters: Dict[str, Any]):
        self.intent_id = str(uuid.uuid4())
        self.intent_type = intent_type
        self.target = target  # 目标轨道或片段
        self.parameters = parameters
        self.confidence = 0.0
        self.created_at = datetime.now()

class IntentParser:
    """意图解析引擎"""
    
    def __init__(self, llm_service):
        self.llm_service = llm_service
        self.intent_templates = self._load_intent_templates()
    
    async def parse_user_input(self, user_input: str, project_state: ProjectState) -> List[EditIntent]:
        """解析用户输入为编辑意图"""
        
        # 构建上下文
        context = self._build_context(project_state)
        
        # 构建提示词
        prompt = self._build_intent_parsing_prompt(user_input, context)
        
        # 调用LLM解析
        response = await self.llm_service.generate(prompt)
        
        # 解析响应为意图对象
        intents = self._parse_llm_response(response)
        
        return intents
    
    def _build_context(self, project_state: ProjectState) -> Dict[str, Any]:
        """构建项目上下文"""
        return {
            "video_duration": project_state.video_metadata.get("duration", 0),
            "tracks": list(project_state.tracks.keys()),
            "current_segments": self._get_segment_summary(project_state),
            "available_effects": self._get_available_effects(),
            "supported_languages": ["zh-CN", "en-US", "ja-JP"]
        }
    
    def _build_intent_parsing_prompt(self, user_input: str, context: Dict[str, Any]) -> str:
        """构建意图解析提示词"""
        return f"""
        你是一个视频编辑意图解析专家。请分析用户的编辑需求，并转换为结构化的编辑意图。

        当前项目信息：
        - 视频时长：{context['video_duration']}秒
        - 可用轨道：{context['tracks']}
        - 当前片段：{context['current_segments']}

        用户需求：{user_input}

        请返回JSON格式的编辑意图列表：
        {{
            "intents": [
                {{
                    "intent_type": "replace_content",
                    "target": "audio_track_1",
                    "parameters": {{
                        "segment_id": "seg_001",
                        "new_content": "替换的内容",
                        "style": "专业"
                    }},
                    "confidence": 0.95
                }}
            ]
        }}
        """
    
    def _parse_llm_response(self, response: str) -> List[EditIntent]:
        """解析LLM响应为意图对象"""
        try:
            data = json.loads(response)
            intents = []
            
            for intent_data in data.get("intents", []):
                intent = EditIntent(
                    intent_type=EditIntentType(intent_data["intent_type"]),
                    target=intent_data["target"],
                    parameters=intent_data["parameters"]
                )
                intent.confidence = intent_data.get("confidence", 0.0)
                intents.append(intent)
            
            return intents
            
        except Exception as e:
            print(f"解析意图失败: {e}")
            return []
```

### 4. 多轨道编排器 (MultiTrackOrchestrator)

```python
import asyncio
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor

class TrackProcessor:
    """轨道处理器"""
    
    def __init__(self, track_type: str):
        self.track_type = track_type
        self.processing_queue = asyncio.Queue()
        self.is_processing = False
    
    async def process_operation(self, operation: EditOperation, project_state: ProjectState) -> Dict[str, Any]:
        """处理轨道操作"""
        # 根据轨道类型选择合适的工具
        tools = self._select_tools_for_operation(operation)
        
        # 执行工具链
        result = await self._execute_tool_chain(tools, operation, project_state)
        
        return result

class MultiTrackOrchestrator:
    """多轨道编排器"""
    
    def __init__(self):
        self.track_processors: Dict[str, TrackProcessor] = {
            "video": TrackProcessor("video"),
            "audio": TrackProcessor("audio"),
            "subtitle": TrackProcessor("subtitle")
        }
        self.dependency_graph = {}
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def execute_edit_plan(self, intents: List[EditIntent], project_state: ProjectState) -> Dict[str, Any]:
        """执行编辑计划"""
        
        # 1. 将意图转换为操作
        operations = self._convert_intents_to_operations(intents)
        
        # 2. 分析依赖关系
        dependency_graph = self._analyze_dependencies(operations)
        
        # 3. 创建执行计划
        execution_plan = self._create_execution_plan(operations, dependency_graph)
        
        # 4. 并行执行
        results = await self._execute_parallel(execution_plan, project_state)
        
        return results
    
    def _analyze_dependencies(self, operations: List[EditOperation]) -> Dict[str, List[str]]:
        """分析操作间的依赖关系"""
        dependencies = {}
        
        for op in operations:
            dependencies[op.operation_id] = []
            
            # 分析依赖规则
            for other_op in operations:
                if self._has_dependency(op, other_op):
                    dependencies[op.operation_id].append(other_op.operation_id)
        
        return dependencies
    
    async def _execute_parallel(self, execution_plan: List[List[EditOperation]], project_state: ProjectState) -> Dict[str, Any]:
        """并行执行操作"""
        results = {}
        
        for batch in execution_plan:
            # 并行执行同一批次的操作
            tasks = []
            for operation in batch:
                processor = self.track_processors[operation.target_track]
                task = processor.process_operation(operation, project_state)
                tasks.append(task)
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    results[batch[i].operation_id] = {"error": str(result)}
                else:
                    results[batch[i].operation_id] = result
        
        return results
```

## 🔄 数据流设计

### 1. 编辑流程数据流
```
用户输入 → 意图解析 → 编辑计划 → 多轨道编排 → 工具执行 → 状态更新 → 结果反馈
```

### 2. 状态同步机制
- **实时同步**: WebSocket推送状态变更
- **增量更新**: 只传输变更的部分
- **冲突解决**: 基于时间戳的冲突解决策略

### 3. 缓存策略
- **操作结果缓存**: 缓存耗时操作的结果
- **中间文件缓存**: 缓存渲染的中间文件
- **智能失效**: 基于依赖关系的缓存失效

## 🚀 性能优化策略

### 1. 异步处理
- 所有I/O操作异步化
- 使用协程池管理并发
- 实现背压控制

### 2. 资源管理
- 智能资源预加载
- 内存使用监控和清理
- GPU资源调度优化

### 3. 分布式处理
- 支持多机器分布式渲染
- 任务队列负载均衡
- 故障恢复机制

---

**这个架构设计充分考虑了视频二创的复杂性，在你现有架构基础上进行了针对性扩展，保持了良好的可扩展性和性能。**
