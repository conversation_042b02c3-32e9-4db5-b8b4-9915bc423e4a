# 多Agent视频二创平台架构设计

## 核心目的
1.让用户用自然语言（或者加上其他参考“语言”）就能完成专业级视频编辑
2.完整二创流程 - 从理解到生成的闭环
3.实现多层次的编辑能力 - 既简单又专业
层次1: 一键完整二创
层次2: 精细化调整
用户: "把第3个镜头的主角表情改得更开心，眼睛要更大"
系统: 精确定位 → 局部修改 → 无缝更新
目的: 满足不同用户的不同需求层次
4.能够做到90分以上的内容吧产出

##  核心设计理念

基于多Agent协作模式，构建分层的视频二创平台：
- **主Agent**: 负责整体流程编排、JSON操作和高级决策
- **专家子Agent**: 各自负责专业领域的细节工具和具体执行
- **工具分层**: 支持完整二创流程，也支持细节精调

## 🏗️ 整体架构图

```mermaid
graph TB
    subgraph "用户层"
        USER[用户自然语言输入]
    end
    
    subgraph "主Agent层"
        MASTER[主Agent<br/>流程编排 + JSON操作]
    end
    
    subgraph "专家Agent层"
        VA[视觉Agent<br/>图像处理专家]
        AA[音频Agent<br/>音频处理专家]
        VDA[视频Agent<br/>视频处理专家]
        TA[文本Agent<br/>文本处理专家]
    end
    
    USER --> MASTER
    MASTER --> VA
    MASTER --> AA
    MASTER --> VDA
    MASTER --> TA
```

```mermaid
graph TB
    subgraph "用户交互层"
        USER[用户输入<br/>视频+编辑需求]
    end
    
    subgraph "主Agent层"
        MASTER[主Agent<br/>Master Video Creator]
        MASTER_TOOLS[主Agent工具集]
        
        subgraph "主Agent核心工具"
            VU[video_understanding<br/>视频理解]
            JE[json_editor<br/>JSON编辑器]
            VG[video_generator<br/>JSON转视频]
            PLAN[planner<br/>任务规划]
        end
    end
    
    subgraph "专家子Agent层"
        subgraph "视觉专家Agent"
            VA[Visual Agent<br/>视觉创作专家]
            VA_TOOLS[视觉工具集]
            T2I[text_to_image<br/>文生图]
            I2I[image_to_image<br/>图生图]
            IE[image_edit<br/>图像编辑]
            ST[style_transfer<br/>风格转换]
        end
        
        subgraph "音频专家Agent"
            AA[Audio Agent<br/>音频创作专家]
            AA_TOOLS[音频工具集]
            TTS[text_to_speech<br/>语音合成]
            VC[voice_clone<br/>声音克隆]
            MG[music_generation<br/>音乐生成]
            AE[audio_edit<br/>音频编辑]
        end
        
        subgraph "视频专家Agent"
            VDA[Video Agent<br/>视频创作专家]
            VDA_TOOLS[视频工具集]
            I2V[image_to_video<br/>图生视频]
            T2V[text_to_video<br/>文生视频]
            VE[video_edit<br/>视频编辑]
            VS[video_synthesis<br/>视频合成]
        end
        
        subgraph "文本专家Agent"
            TA[Text Agent<br/>文本处理专家]
            TA_TOOLS[文本工具集]
            TC[text_creation<br/>文本创作]
            TT[text_translation<br/>文本翻译]
            TS[text_style<br/>文本风格化]
            SE[subtitle_edit<br/>字幕编辑]
        end
    end
    
    subgraph "基础服务层"
        API[AI服务API]
        STORAGE[文件存储]
        CACHE[缓存系统]
    end
    
    %% 连接关系
    USER --> MASTER
    MASTER --> MASTER_TOOLS
    MASTER_TOOLS --> VU
    MASTER_TOOLS --> JE
    MASTER_TOOLS --> VG
    MASTER_TOOLS --> PLAN
    
    MASTER --> VA
    MASTER --> AA
    MASTER --> VDA
    MASTER --> TA
    
    VA --> VA_TOOLS
    VA_TOOLS --> T2I
    VA_TOOLS --> I2I
    VA_TOOLS --> IE
    VA_TOOLS --> ST
    
    AA --> AA_TOOLS
    AA_TOOLS --> TTS
    AA_TOOLS --> VC
    AA_TOOLS --> MG
    AA_TOOLS --> AE
    
    VDA --> VDA_TOOLS
    VDA_TOOLS --> I2V
    VDA_TOOLS --> T2V
    VDA_TOOLS --> VE
    VDA_TOOLS --> VS
    
    TA --> TA_TOOLS
    TA_TOOLS --> TC
    TA_TOOLS --> TT
    TA_TOOLS --> TS
    TA_TOOLS --> SE
    
    T2I --> API
    TTS --> API
    I2V --> API
    
    VG --> STORAGE
    VU --> STORAGE
    
    style MASTER fill:#e1f5fe
    style VA fill:#e8f5e8
    style AA fill:#fff3e0
    style VDA fill:#fce4ec
    style TA fill:#f3e5f5
```

## 🤖 主Agent设计

### 主Agent职责
1. **整体流程编排**: 理解用户需求，制定执行计划
2. **JSON操作**: 视频理解生成JSON，编辑JSON，JSON转视频
3. **子Agent调度**: 根据需要调用专家子Agent
4. **结果整合**: 将各子Agent的结果整合到最终输出

### 主Agent核心工具

```python
class MasterVideoCreatorAgent:
    """主视频创作Agent"""
    
    def __init__(self):
        self.tools = [
            self.video_understanding_tool,      # 视频理解 → JSON
            self.json_editor_tool,             # JSON编辑器
            self.video_generator_tool,         # JSON → 视频
            self.planner_tool,                 # 任务规划
            self.visual_agent_caller,          # 调用视觉Agent
            self.audio_agent_caller,           # 调用音频Agent
            self.video_agent_caller,           # 调用视频Agent
            self.text_agent_caller             # 调用文本Agent
        ]
    
    @tool
    def video_understanding_tool(self, video_input: str, analysis_request: str) -> Dict:
        """视频理解工具 - 你现有的视频理解工作流"""
        from src.workflows.video_understanding import run_video_understanding_v2
        
        result = run_video_understanding_v2(
            video_path=video_input if not video_input.startswith('http') else None,
            video_url=video_input if video_input.startswith('http') else None,
            user_request=analysis_request
        )
        
        return {
            "video_json": result.get("video_script_json"),
            "metadata": result.get("video_metadata"),
            "summary": "视频理解完成，生成结构化JSON"
        }
    
    @tool
    def json_editor_tool(self, video_json: Dict, edit_instructions: str) -> Dict:
        """JSON编辑器 - 根据用户指令编辑视频JSON"""
        # 使用LLM理解编辑指令，修改JSON结构
        edited_json = self._edit_json_with_llm(video_json, edit_instructions)
        
        return {
            "edited_json": edited_json,
            "changes": self._get_json_changes(video_json, edited_json),
            "summary": f"JSON编辑完成: {edit_instructions}"
        }
    
    @tool
    def video_generator_tool(self, video_json: Dict, render_config: Dict = None) -> Dict:
        """视频生成器 - JSON转视频的完整流程"""
        # 这里是你说的"那一套流程"
        # 1. 解析JSON中需要的素材
        # 2. 调用各种AI服务生成素材
        # 3. 合成最终视频
        
        video_path = self._generate_video_from_json(video_json, render_config)
        
        return {
            "video_path": video_path,
            "render_info": {"format": "mp4", "duration": "auto"},
            "summary": "视频生成完成"
        }
    
    @tool
    def visual_agent_caller(self, task_description: str, task_params: Dict = None) -> Dict:
        """调用视觉专家Agent"""
        visual_agent = VisualAgent()
        return visual_agent.execute(task_description, task_params)
    
    @tool
    def audio_agent_caller(self, task_description: str, task_params: Dict = None) -> Dict:
        """调用音频专家Agent"""
        audio_agent = AudioAgent()
        return audio_agent.execute(task_description, task_params)
```

## 🎨 专家子Agent设计

### 视觉专家Agent

```python
class VisualAgent:
    """视觉创作专家Agent"""
    
    def __init__(self):
        self.tools = [
            self.text_to_image,
            self.image_to_image, 
            self.image_edit,
            self.style_transfer,
            self.image_upscale,
            self.background_removal
        ]
    
    @tool
    def text_to_image(self, prompt: str, style: str = "realistic", size: str = "1024x1024") -> str:
        """文生图工具"""
        # 调用FLUX、DALL-E等API
        image_url = self._call_image_generation_api(prompt, style, size)
        return image_url
    
    @tool
    def image_to_image(self, source_image: str, prompt: str, strength: float = 0.7) -> str:
        """图生图工具"""
        # 调用图像编辑API
        edited_image_url = self._call_image_edit_api(source_image, prompt, strength)
        return edited_image_url
    
    @tool
    def style_transfer(self, source_image: str, style_reference: str) -> str:
        """风格转换工具"""
        styled_image_url = self._call_style_transfer_api(source_image, style_reference)
        return styled_image_url
    
    def execute(self, task_description: str, task_params: Dict = None) -> Dict:
        """执行视觉任务"""
        # 使用ReAct模式，根据任务描述选择合适的工具
        # 这里是一个完整的Agent，有自己的推理和工具选择逻辑
        pass
```

### 音频专家Agent

```python
class AudioAgent:
    """音频创作专家Agent"""
    
    def __init__(self):
        self.tools = [
            self.text_to_speech,
            self.voice_clone,
            self.music_generation,
            self.audio_edit,
            self.voice_conversion,
            self.audio_enhancement
        ]
    
    @tool
    def text_to_speech(self, text: str, voice_style: str = "natural", language: str = "zh-CN") -> str:
        """语音合成工具"""
        audio_url = self._call_tts_api(text, voice_style, language)
        return audio_url
    
    @tool
    def voice_clone(self, reference_audio: str, target_text: str) -> str:
        """声音克隆工具"""
        cloned_audio_url = self._call_voice_clone_api(reference_audio, target_text)
        return cloned_audio_url
    
    @tool
    def music_generation(self, description: str, duration: int = 30, style: str = "pop") -> str:
        """音乐生成工具"""
        music_url = self._call_music_generation_api(description, duration, style)
        return music_url
    
    def execute(self, task_description: str, task_params: Dict = None) -> Dict:
        """执行音频任务"""
        # ReAct模式的音频专家Agent
        pass
```

### 视频专家Agent

```python
class VideoAgent:
    """视频创作专家Agent"""
    
    def __init__(self):
        self.tools = [
            self.image_to_video,
            self.text_to_video,
            self.video_edit,
            self.video_synthesis,
            self.video_upscale,
            self.video_stabilization
        ]
    
    @tool
    def image_to_video(self, image_path: str, motion_prompt: str = "", duration: int = 5) -> str:
        """图生视频工具"""
        video_url = self._call_image_to_video_api(image_path, motion_prompt, duration)
        return video_url
    
    @tool
    def text_to_video(self, prompt: str, duration: int = 5, style: str = "realistic") -> str:
        """文生视频工具"""
        video_url = self._call_text_to_video_api(prompt, duration, style)
        return video_url
    
    @tool
    def video_synthesis(self, video_clips: List[str], audio_track: str = None) -> str:
        """视频合成工具"""
        synthesized_video = self._call_video_synthesis_api(video_clips, audio_track)
        return synthesized_video
    
    def execute(self, task_description: str, task_params: Dict = None) -> Dict:
        """执行视频任务"""
        # ReAct模式的视频专家Agent
        pass
```

## 🔄 工作流程设计

### 1. 完整二创流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as 主Agent
    participant VA as 视觉Agent
    participant AA as 音频Agent
    participant VDA as 视频Agent

    U->>M: "把这个视频改成卡通风格，换个轻松的背景音乐"
    
    M->>M: video_understanding_tool(video)
    Note over M: 生成视频JSON结构
    
    M->>M: json_editor_tool(json, "改成卡通风格+换背景音乐")
    Note over M: 编辑JSON，标记需要的素材
    
    M->>VA: visual_agent_caller("生成卡通风格的角色图像")
    VA->>VA: 选择style_transfer工具
    VA-->>M: 返回卡通风格图像
    
    M->>AA: audio_agent_caller("生成轻松的背景音乐")
    AA->>AA: 选择music_generation工具
    AA-->>M: 返回背景音乐
    
    M->>M: video_generator_tool(edited_json)
    Note over M: 整合所有素材，生成最终视频
    
    M-->>U: 🎬 完成的二创视频
```

### 2. 细节调整流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant M as 主Agent
    participant VA as 视觉Agent

    U->>M: "把第3个镜头的角色表情改得更开心一些"
    
    M->>M: 分析当前JSON，定位第3个镜头
    
    M->>VA: visual_agent_caller("修改角色表情为更开心", {segment: 3})
    
    VA->>VA: 分析任务，选择image_edit工具
    VA->>VA: image_edit(原图, "开心表情")
    VA-->>M: 返回修改后的图像
    
    M->>M: json_editor_tool(更新第3镜头的图像)
    
    M->>M: video_generator_tool(只重新渲染第3镜头)
    
    M-->>U: ✨ 局部更新完成
```

## 🎯 架构优势

### 1. 分层清晰
- **主Agent**: 负责整体流程和JSON操作
- **子Agent**: 各自专精，工具丰富
- **用户**: 可以做完整二创，也可以精细调整

### 2. 易于扩展
- **新增专家Agent**: 只需实现标准接口
- **新增工具**: 在对应的专家Agent中添加
- **新增功能**: 在主Agent中添加新的编排逻辑

### 3. 灵活性强
- **完整流程**: 一键完成复杂的视频二创
- **细节控制**: 精确调整任何细节元素
- **模块化**: 每个Agent可以独立使用和测试

### 4. 复用现有能力
- **视频理解**: 直接复用你现有的工作流
- **工具系统**: 现有工具可以分配到各专家Agent
- **JSON操作**: 成为主Agent的核心能力
