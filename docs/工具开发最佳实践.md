# 工具开发最佳实践指南

## 🎯 工具开发核心原则

### 1. 单一职责原则
每个工具只负责一个明确的功能，避免功能耦合。

**✅ 好的例子**:
```python
@ToolRegistry.register(ToolCategory.PROCESSOR, "text_segmenter")
class TextSegmenter(BaseTool):
    """专门负责文本分段的工具"""
    def process(self, input_data):
        text = input_data["text"]
        segments = self._segment_text(text)
        return {"segments": segments}
```

**❌ 避免的例子**:
```python
class TextProcessorAndGenerator(BaseTool):
    """一个工具做太多事情"""
    def process(self, input_data):
        # 既分段又生成图片，职责不清
        segments = self._segment_text(input_data["text"])
        images = self._generate_images(segments)
        return {"segments": segments, "images": images}
```

### 2. 状态驱动设计
工具通过状态字段通信，不直接调用其他工具。

**✅ 正确的数据流**:
```python
class VideoAnalyzer(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["video_file_path"]  # 从状态读取
        self.output_keys = ["analysis_result"]  # 写入状态
    
    def process(self, input_data):
        video_path = input_data["video_file_path"]
        result = self._analyze_video(video_path)
        return {"analysis_result": result}
```

### 3. 错误处理标准化
统一的错误处理格式，便于上层统一处理。

```python
class RobustTool(BaseTool):
    def process(self, input_data):
        try:
            # 验证输入
            if not self._validate_input(input_data):
                return {"error_message": "输入数据验证失败"}
            
            # 执行处理
            result = self._do_process(input_data)
            
            # 验证输出
            if not self._validate_output(result):
                return {"error_message": "输出数据验证失败"}
            
            return result
            
        except Exception as e:
            return {"error_message": f"处理异常: {str(e)}"}
```

## 🛠️ 工具开发模板

### 基础工具模板
```python
from tools.base.tool_interface import BaseTool, ToolCategory
from tools.base.tool_registry import ToolRegistry

@ToolRegistry.register(ToolCategory.PROCESSOR, "your_tool_name")
class YourTool(BaseTool):
    """工具描述"""
    
    def __init__(self):
        super().__init__()
        self.description = "详细的工具功能描述"
        self.input_keys = ["input_field1", "input_field2"]
        self.output_keys = ["output_field1", "output_field2"]
        self.dependencies = []  # 依赖的其他工具
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑"""
        try:
            # 1. 输入验证
            if not self._validate_input(input_data):
                return {"error_message": "输入验证失败"}
            
            # 2. 核心处理
            result = self._core_process(input_data)
            
            # 3. 输出验证
            if not self._validate_output(result):
                return {"error_message": "输出验证失败"}
            
            return result
            
        except Exception as e:
            return {"error_message": f"{self.name} 处理失败: {str(e)}"}
    
    def _validate_input(self, input_data: Dict[str, Any]) -> bool:
        """验证输入数据"""
        for key in self.input_keys:
            if key not in input_data:
                return False
        return True
    
    def _core_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑，子类重写此方法"""
        # 实现具体的处理逻辑
        pass
    
    def _validate_output(self, result: Dict[str, Any]) -> bool:
        """验证输出数据"""
        for key in self.output_keys:
            if key not in result:
                return False
        return True
```

### 异步工具模板
```python
import asyncio
from typing import Dict, Any

@ToolRegistry.register(ToolCategory.PROCESSOR, "async_tool")
class AsyncTool(BaseTool):
    """异步处理工具模板"""
    
    async def process_async(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步处理方法"""
        try:
            # 异步处理逻辑
            result = await self._async_core_process(input_data)
            return result
        except Exception as e:
            return {"error_message": f"异步处理失败: {str(e)}"}
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """同步接口，内部调用异步方法"""
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(self.process_async(input_data))
    
    async def _async_core_process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步核心处理逻辑"""
        # 实现异步处理
        await asyncio.sleep(0.1)  # 示例异步操作
        return {"result": "async_result"}
```

## 📋 工具分类指南

### INPUT 类工具
负责数据输入和预处理
```python
@ToolRegistry.register(ToolCategory.INPUT, "file_reader")
class FileReader(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["file_path"]
        self.output_keys = ["raw_content", "file_metadata"]
```

### ANALYZER 类工具
负责内容分析和理解
```python
@ToolRegistry.register(ToolCategory.ANALYZER, "sentiment_analyzer")
class SentimentAnalyzer(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["text_content"]
        self.output_keys = ["sentiment_score", "emotion_tags"]
```

### PROCESSOR 类工具
负责数据处理和转换
```python
@ToolRegistry.register(ToolCategory.PROCESSOR, "data_transformer")
class DataTransformer(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["raw_data"]
        self.output_keys = ["processed_data"]
```

### GENERATOR 类工具
负责内容生成
```python
@ToolRegistry.register(ToolCategory.GENERATOR, "content_generator")
class ContentGenerator(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["prompt", "style_config"]
        self.output_keys = ["generated_content"]
```

### OUTPUT 类工具
负责结果输出和存储
```python
@ToolRegistry.register(ToolCategory.OUTPUT, "file_writer")
class FileWriter(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["content", "output_path"]
        self.output_keys = ["file_path", "file_size"]
```

## 🧪 工具测试最佳实践

### 单元测试模板
```python
import pytest
from unittest.mock import Mock, patch
from your_tool import YourTool

class TestYourTool:
    def setup_method(self):
        self.tool = YourTool()
    
    def test_process_success(self):
        """测试正常处理流程"""
        input_data = {"input_field1": "test_value"}
        result = self.tool.process(input_data)
        
        assert "error_message" not in result
        assert "output_field1" in result
    
    def test_process_invalid_input(self):
        """测试无效输入处理"""
        input_data = {}  # 缺少必需字段
        result = self.tool.process(input_data)
        
        assert "error_message" in result
    
    def test_process_exception_handling(self):
        """测试异常处理"""
        with patch.object(self.tool, '_core_process', side_effect=Exception("测试异常")):
            input_data = {"input_field1": "test_value"}
            result = self.tool.process(input_data)
            
            assert "error_message" in result
            assert "测试异常" in result["error_message"]
    
    @pytest.mark.asyncio
    async def test_async_process(self):
        """测试异步处理"""
        if hasattr(self.tool, 'process_async'):
            input_data = {"input_field1": "test_value"}
            result = await self.tool.process_async(input_data)
            assert "output_field1" in result
```

### 集成测试示例
```python
def test_tool_integration():
    """测试工具在完整流程中的集成"""
    from core.executor import DynamicExecutor
    from core.state import UniversalState
    
    # 初始化状态
    state = UniversalState(
        user_request="测试请求",
        # ... 其他字段
    )
    
    # 执行工具链
    executor = DynamicExecutor()
    tools = ["input.your_input_tool", "processor.your_tool", "output.your_output_tool"]
    
    final_state = executor.execute_tool_chain(tools, state)
    
    # 验证结果
    assert final_state.get("error_message") is None
    assert final_state.get("final_outputs") is not None
```

## 🚀 性能优化建议

### 1. 缓存机制
```python
from functools import lru_cache

class CachedTool(BaseTool):
    @lru_cache(maxsize=128)
    def _expensive_operation(self, input_key):
        """缓存耗时操作的结果"""
        # 耗时的处理逻辑
        return result
```

### 2. 批处理支持
```python
class BatchProcessor(BaseTool):
    def process_batch(self, input_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理，提高效率"""
        results = []
        for input_data in input_list:
            result = self.process(input_data)
            results.append(result)
        return results
```

### 3. 资源管理
```python
class ResourceManagedTool(BaseTool):
    def __init__(self):
        super().__init__()
        self._resource = None
    
    def _get_resource(self):
        """懒加载资源"""
        if self._resource is None:
            self._resource = self._initialize_resource()
        return self._resource
    
    def _initialize_resource(self):
        """初始化资源（如模型、连接等）"""
        pass
    
    def __del__(self):
        """清理资源"""
        if self._resource:
            self._cleanup_resource()
```

## 📚 常见问题和解决方案

### Q1: 工具间如何传递复杂数据？
**A**: 通过状态字段传递，避免直接调用。复杂数据可以序列化存储。

### Q2: 如何处理工具的依赖关系？
**A**: 在工具的 `dependencies` 字段中声明，执行器会自动处理依赖顺序。

### Q3: 如何调试工具执行过程？
**A**: 使用结构化日志，记录每个工具的输入输出和执行时间。

### Q4: 如何处理长时间运行的工具？
**A**: 使用异步处理，并提供进度回调机制。

---

**记住**: 好的工具设计是整个框架成功的关键。遵循这些最佳实践，可以确保工具的可维护性、可测试性和可扩展性。
