# XHS 智能视频二创平台 - 项目架构文档

## 📋 项目概述

### 项目定位
基于LangGraph构建的智能视频二创平台，用户上传视频后系统进行智能理解，生成结构化JSON，用户通过自然语言描述编辑需求，系统自动匹配工具并输出二创视频。

### 核心价值
- **降低视频编辑门槛**: 自然语言交互，无需专业技能
- **提升内容创作效率**: 自动化工具匹配和批量处理
- **支持内容本地化**: 快速生成多语言、多风格版本
- **AI驱动的智能编辑**: 基于内容理解的智能推荐

### 技术特色
- 🎬 **无限制视频理解**: 支持任意大小视频文件 (已验证111MB+)
- 🤖 **AI驱动编辑**: LLM理解用户意图，智能匹配编辑工具
- 🔧 **模块化架构**: 基于工具注册的插件化设计
- ⚡ **高性能处理**: 并发分析，3-4倍性能提升
- 🌐 **云原生设计**: 支持分布式处理和云端部署

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────────────────────────────────────────────────┤
│  Web界面  │  REST API  │  WebSocket  │  移动端(未来)        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    业务编排层                                │
├─────────────────────────────────────────────────────────────┤
│  会话管理  │  意图解析  │  计划生成  │  多轨道编排          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    核心引擎层                                │
├─────────────────────────────────────────────────────────────┤
│  理解引擎  │  编辑引擎  │  渲染引擎  │  AI能力引擎          │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    数据管理层                                │
├─────────────────────────────────────────────────────────────┤
│  状态管理  │  版本控制  │  缓存系统  │  资源管理            │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    工具生态层                                │
├─────────────────────────────────────────────────────────────┤
│  视频工具  │  音频工具  │  文本工具  │  AI工具              │
└─────────────────────────────────────────────────────────────┘
```

### 核心设计原则

#### 1. 分层解耦
- **用户交互层**: 处理用户界面和API交互
- **业务编排层**: 处理业务逻辑和流程编排  
- **核心引擎层**: 提供核心处理能力
- **数据管理层**: 管理状态、缓存和持久化
- **工具生态层**: 提供可插拔的处理工具

#### 2. 状态驱动
- 所有操作基于项目状态进行
- 支持状态版本控制和回滚
- 实现增量更新和差异计算

#### 3. 工具组合
- 单一职责的工具组件
- 基于装饰器的工具注册
- 动态工具链组合和执行

#### 4. 异步并发
- 支持多轨道并行处理
- 异步任务队列管理
- 实时进度反馈

## 🔧 核心组件设计

### 1. 状态管理系统

#### 项目状态结构
```python
@dataclass
class ProjectState:
    """项目状态管理"""
    project_id: str
    version: int
    created_at: datetime
    updated_at: datetime
    
    # 视频元数据
    video_metadata: Dict[str, Any]
    
    # 轨道数据
    tracks: Dict[str, VideoTrack]  # track_id -> VideoTrack
    
    # 时间轴
    timeline: Dict[str, Any]
    
    # 编辑历史
    edit_history: List[EditOperation]
    pending_operations: List[EditOperation]
    
    # 资源管理
    assets: Dict[str, str]  # asset_id -> file_path
    cache_keys: Dict[str, str]  # operation_id -> cache_key
```

#### 视频轨道结构
```python
@dataclass
class VideoTrack:
    """视频轨道"""
    track_id: str
    track_type: str  # "video", "audio", "subtitle"
    segments: List[Dict[str, Any]]
    effects: List[Dict[str, Any]]
    metadata: Dict[str, Any]
```

### 2. 编辑操作系统

#### 编辑操作定义
```python
@dataclass
class EditOperation:
    """编辑操作"""
    operation_id: str
    operation_type: str  # "replace", "add", "remove", "modify"
    target_track: str
    target_segment: Optional[str]
    parameters: Dict[str, Any]
    timestamp: datetime
    status: str  # "pending", "processing", "completed", "failed"
```

#### 意图解析引擎
```python
class IntentParser:
    """意图解析引擎"""
    
    async def parse_user_input(self, user_input: str, project_state: ProjectState) -> List[EditIntent]:
        """
        解析用户输入为编辑意图
        
        示例输入: "把第一段的背景音乐换成更激昂的，然后把说话内容改成介绍Python"
        
        输出: [
            EditIntent(type="replace", target="background_music", params={"style": "激昂"}),
            EditIntent(type="replace", target="speech", params={"content": "Python介绍"})
        ]
        """
```

### 3. 工具系统架构

#### 工具基础接口
```python
class BaseTool(ABC):
    """工具基础类"""
    
    def __init__(self):
        self.name: str = self.__class__.__name__
        self.category: ToolCategory = ToolCategory.INPUT
        self.input_keys: List[str] = []
        self.output_keys: List[str] = []
        self.dependencies: List[str] = []
    
    @abstractmethod
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑"""
        pass
    
    def execute(self, state: UniversalState) -> Dict[str, Any]:
        """标准执行接口"""
        pass
```

#### 工具注册系统
```python
class ToolRegistry:
    """工具注册中心"""
    
    @classmethod
    def register(cls, category: ToolCategory, name: Optional[str] = None):
        """工具注册装饰器"""
        def decorator(tool_class: Type[BaseTool]):
            # 注册逻辑
            return tool_class
        return decorator
    
    @classmethod
    def create_tool(cls, name: str) -> Optional[BaseTool]:
        """创建工具实例"""
        pass
```

### 4. 多轨道编排系统

#### 编排器设计
```python
class MultiTrackOrchestrator:
    """多轨道编排器"""
    
    async def execute_edit_plan(self, intents: List[EditIntent], project_state: ProjectState) -> Dict[str, Any]:
        """执行编辑计划"""
        
        # 1. 将意图转换为操作
        operations = self._convert_intents_to_operations(intents)
        
        # 2. 分析依赖关系
        dependency_graph = self._analyze_dependencies(operations)
        
        # 3. 创建执行计划
        execution_plan = self._create_execution_plan(operations, dependency_graph)
        
        # 4. 并行执行
        results = await self._execute_parallel(execution_plan, project_state)
        
        return results
```

## 📁 项目目录结构

```
xhs_video_creator/
├── README.md                           # 项目说明
├── requirements.txt                    # 依赖管理
├── main.py                            # 主入口
├── 
├── src/                               # 源代码目录
│   ├── __init__.py
│   ├── core/                          # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── state.py                   # 状态管理
│   │   ├── session.py                 # 会话管理
│   │   ├── intent_parser.py           # 意图解析
│   │   ├── orchestrator.py            # 编排引擎
│   │   └── project_manager.py         # 项目管理
│   │
│   ├── engines/                       # 核心引擎
│   │   ├── __init__.py
│   │   ├── understanding/             # 理解引擎
│   │   │   ├── video_parser.py        # 视频解析器
│   │   │   ├── content_analyzer.py    # 内容分析器
│   │   │   └── metadata_extractor.py  # 元数据提取器
│   │   ├── editing/                   # 编辑引擎
│   │   │   ├── track_manager.py       # 轨道管理器
│   │   │   ├── timeline_engine.py     # 时间轴引擎
│   │   │   └── effect_processor.py    # 特效处理器
│   │   ├── rendering/                 # 渲染引擎
│   │   │   ├── video_renderer.py      # 视频渲染器
│   │   │   └── export_manager.py      # 导出管理器
│   │   └── ai/                        # AI能力引擎
│   │       ├── llm_service.py         # 大语言模型服务
│   │       ├── tts_service.py         # 语音合成服务
│   │       └── image_gen_service.py   # 图像生成服务
│   │
│   ├── tools/                         # 工具生态
│   │   ├── __init__.py
│   │   ├── base/                      # 工具基础
│   │   │   ├── tool_interface.py      # 工具接口
│   │   │   └── tool_registry.py       # 工具注册
│   │   ├── video/                     # 视频工具
│   │   │   ├── video_input.py         # 视频输入
│   │   │   ├── video_cutter.py        # 视频剪切
│   │   │   └── video_merger.py        # 视频合并
│   │   ├── audio/                     # 音频工具
│   │   │   ├── audio_replacer.py      # 音频替换
│   │   │   ├── tts_generator.py       # TTS生成
│   │   │   └── music_generator.py     # 音乐生成
│   │   ├── text/                      # 文本工具
│   │   │   ├── text_replacer.py       # 文本替换
│   │   │   └── subtitle_generator.py  # 字幕生成
│   │   └── ai/                        # AI工具
│   │       ├── style_transfer.py      # 风格转换
│   │       └── content_generator.py   # 内容生成
│   │
│   ├── workflows/                     # 工作流
│   │   ├── __init__.py
│   │   ├── video_understanding.py     # 视频理解工作流
│   │   ├── video_editing.py           # 视频编辑工作流
│   │   └── video_rendering.py         # 视频渲染工作流
│   │
│   ├── api/                           # API接口
│   │   ├── __init__.py
│   │   ├── routes/                    # 路由定义
│   │   ├── models/                    # 数据模型
│   │   └── middleware/                # 中间件
│   │
│   ├── web/                           # Web界面
│   │   ├── static/                    # 静态资源
│   │   ├── templates/                 # 模板文件
│   │   └── components/                # 组件
│   │
│   └── utils/                         # 工具函数
│       ├── __init__.py
│       ├── file_utils.py              # 文件工具
│       ├── cache_utils.py             # 缓存工具
│       └── config.py                  # 配置管理
│
├── tests/                             # 测试目录
│   ├── unit/                          # 单元测试
│   ├── integration/                   # 集成测试
│   └── e2e/                           # 端到端测试
│
├── docs/                              # 文档目录
│   ├── api/                           # API文档
│   ├── user/                          # 用户文档
│   └── dev/                           # 开发文档
│
├── scripts/                           # 脚本工具
│   ├── setup.sh                       # 环境设置
│   ├── deploy.sh                      # 部署脚本
│   └── test.sh                        # 测试脚本
│
├── config/                            # 配置文件
│   ├── development.yaml               # 开发环境配置
│   ├── production.yaml                # 生产环境配置
│   └── docker-compose.yml             # Docker配置
│
└── output/                            # 输出目录
    ├── projects/                      # 项目文件
    ├── temp/                          # 临时文件
    └── exports/                       # 导出文件
```

## 🔄 数据流设计

### 核心数据流
```
用户上传视频 → 视频理解 → 结构化JSON → 用户编辑意图 → 意图解析 → 
工具匹配 → 多轨道编排 → 并行执行 → 状态更新 → 视频渲染 → 结果输出
```

### 状态同步机制
- **实时同步**: WebSocket推送状态变更
- **增量更新**: 只传输变更的部分  
- **冲突解决**: 基于时间戳的冲突解决策略
- **版本控制**: 支持操作回滚和历史查看

## 🛠️ 技术规范

### 开发技术栈

#### 后端技术
- **Python 3.11+**: 主要开发语言
- **FastAPI**: Web框架和API服务
- **LangGraph**: 工作流编排引擎
- **LangChain**: LLM集成框架
- **SQLAlchemy**: ORM数据库操作
- **Redis**: 缓存和会话存储
- **PostgreSQL**: 主数据库
- **FFmpeg**: 视频处理引擎

#### 前端技术
- **React 18**: 前端框架
- **TypeScript**: 类型安全
- **Vite**: 构建工具
- **TailwindCSS**: 样式框架
- **Zustand**: 状态管理
- **React Query**: 数据获取
- **WebSocket**: 实时通信

#### AI服务
- **OpenAI GPT-4**: 意图理解和内容生成
- **Google Gemini**: 视频理解分析
- **Azure TTS**: 语音合成服务
- **Stable Diffusion**: 图像生成
- **Whisper**: 语音识别

#### 基础设施
- **Docker**: 容器化部署
- **Kubernetes**: 容器编排
- **MinIO**: 对象存储
- **Nginx**: 反向代理
- **Prometheus**: 监控系统
- **Grafana**: 可视化面板

### 数据库设计

#### 核心表结构
```sql
-- 项目表
CREATE TABLE projects (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    video_url VARCHAR(500),
    video_metadata JSONB,
    current_state JSONB,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 编辑操作表
CREATE TABLE edit_operations (
    id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(id),
    operation_type VARCHAR(50) NOT NULL,
    target_track VARCHAR(100),
    target_segment VARCHAR(100),
    parameters JSONB,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- 资源文件表
CREATE TABLE assets (
    id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(id),
    asset_type VARCHAR(50) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 用户会话表
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(id),
    session_data JSONB,
    last_activity TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP
);
```

### API接口设计

#### RESTful API规范
```yaml
# 项目管理API
POST   /api/v1/projects                    # 创建项目
GET    /api/v1/projects                    # 获取项目列表
GET    /api/v1/projects/{id}               # 获取项目详情
PUT    /api/v1/projects/{id}               # 更新项目
DELETE /api/v1/projects/{id}               # 删除项目

# 视频处理API
POST   /api/v1/projects/{id}/upload        # 上传视频
POST   /api/v1/projects/{id}/analyze       # 分析视频
GET    /api/v1/projects/{id}/state         # 获取项目状态

# 编辑操作API
POST   /api/v1/projects/{id}/edit          # 提交编辑意图
GET    /api/v1/projects/{id}/operations    # 获取操作历史
POST   /api/v1/projects/{id}/render        # 渲染视频
GET    /api/v1/projects/{id}/export        # 导出视频

# WebSocket API
WS     /ws/projects/{id}                   # 实时状态更新
```

#### 数据模型定义
```python
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    video_url: Optional[str] = None

class ProjectResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    video_metadata: Optional[Dict[str, Any]]
    current_state: Optional[Dict[str, Any]]
    version: int
    created_at: datetime
    updated_at: datetime

class EditRequest(BaseModel):
    user_input: str  # 自然语言编辑需求
    context: Optional[Dict[str, Any]] = None

class EditOperation(BaseModel):
    operation_type: str
    target_track: str
    target_segment: Optional[str]
    parameters: Dict[str, Any]

class RenderRequest(BaseModel):
    output_format: str = "mp4"
    quality: str = "high"
    resolution: str = "1920x1080"
```

## 🚀 部署架构

### 微服务架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   API Gateway   │    │  Load Balancer  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
    ┌────────────────────────────┼────────────────────────────┐
    │                            │                            │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Core Service   │    │  Video Service  │    │  AI Service     │
│  (业务逻辑)      │    │  (视频处理)      │    │  (AI能力)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
    │   PostgreSQL    │    │     Redis       │    │     MinIO       │
    │   (主数据库)     │    │   (缓存/会话)    │    │   (文件存储)     │
    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "src.api.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Kubernetes部署
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-creator-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: video-creator-api
  template:
    metadata:
      labels:
        app: video-creator-api
    spec:
      containers:
      - name: api
        image: video-creator:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 📊 性能指标

### 系统性能目标
- **视频上传**: 支持最大500MB文件，上传时间<2分钟
- **视频理解**: 10分钟视频分析时间<5分钟
- **编辑响应**: 简单编辑操作响应时间<3秒
- **视频渲染**: 10分钟视频渲染时间<10分钟
- **并发用户**: 支持100+并发用户
- **系统可用性**: 99.9%可用性目标

### 监控指标
```python
# 关键监控指标
METRICS = {
    "api_response_time": "API响应时间",
    "video_processing_time": "视频处理时间",
    "render_queue_length": "渲染队列长度",
    "active_sessions": "活跃会话数",
    "error_rate": "错误率",
    "resource_usage": "资源使用率"
}
```

## 🔒 安全设计

### 安全措施
1. **身份认证**: JWT Token认证
2. **权限控制**: RBAC角色权限控制
3. **数据加密**: 敏感数据加密存储
4. **文件安全**: 文件类型检查和病毒扫描
5. **API限流**: 防止API滥用
6. **日志审计**: 完整的操作日志记录

### 数据隐私
- 用户视频数据加密存储
- 定期清理临时文件
- 符合GDPR数据保护要求
- 用户数据删除机制

---

**这个完整的架构文档涵盖了从技术选型到部署运维的各个方面，为项目实施提供了全面的技术指导。**
