# 智能图片生成助手

> 基于 LangGraph 构建的智能图文生成系统，支持动态工具组合和多样化输出格式

## 🌟 项目特色

- **🧠 智能化**: LLM驱动的意图理解和工具选择
- **🔧 模块化**: 松耦合的工具组件设计
- **⚡ 灵活性**: 动态工具组合，支持多种生成风格
- **🧪 可测试**: 完整的单元测试和集成测试
- **📈 可扩展**: 易于添加新工具和预设流程

## 🏗️ 系统架构

```
用户请求 → 意图分析 → 执行计划 → 工具选择 → 动态执行 → 结果输出
```

### 核心组件

- **智能规划器**: 分析用户意图，生成执行计划
- **工具选择器**: 智能选择合适的工具组合
- **动态执行引擎**: 按序执行工具链
- **工具注册系统**: 自动发现和管理工具

## 📦 工具库

### 输入工具 (Input)
- `input.text_input` - 文本输入处理
- `input.csv_reader` - CSV文件数据读取

### 风格转换工具 (Style Converters)
- `style_converter.xiaohongshu` - 小红书风格转换

### 内容处理工具 (Content Processors)  
- `content_processor.segmenter` - 智能文本分段

### 生成工具 (Generators)
- `generator.html` - HTML卡片生成

### 输出工具 (Output)
- `output.html2image` - HTML转图片转换

## 🎨 预设流程

### 小红书图文生成
```python
from graphs.preset_graphs import XiaohongshuPresetGraph

preset = XiaohongshuPresetGraph()
result = preset.execute(initial_state)
```

**工具链**: `text_input` → `xiaohongshu_style` → `segmenter` → `html` → `html2image`

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装playwright浏览器
playwright install chromium
```

### 2. 配置API

设置环境变量：
```bash
export CUSTOM_OPENAI_API_KEY="your-api-key"
export CUSTOM_OPENAI_BASE_URL="your-base-url"
```

### 3. 基础使用

```python
from core.assistant import IntelligentImageAssistant

# 创建智能助手
assistant = IntelligentImageAssistant()

# 生成小红书风格图文
result = assistant.process_request(
    "把这条新闻转换成小红书风格的图文：AI芯片性能提升50%"
)

# 查看生成结果
print(f"生成图片: {result['image_paths']}")
```

### 4. CSV数据源

```python
# 处理CSV数据
result = assistant.process_request(
    "从CSV文件中读取ID为1的新闻，转换成小红书风格"
)
```

## 🧪 测试

### 运行所有工具测试
```bash
python test_all_tools.py
```

### 端到端测试
```bash
python test_end_to_end.py
```

### 单个工具测试
```bash
# 测试文本输入工具
python -m tools.input.text_input

# 测试小红书风格转换
python -m tools.style_converters.xiaohongshu_style
```

## 📂 项目结构

```
xhs_intelligent_assistant/
├── core/                           # 核心框架
│   ├── assistant.py               # 主助手类
│   ├── planner.py                # 智能规划器
│   ├── selector.py               # 工具选择器
│   ├── executor.py               # 执行引擎
│   └── state.py                  # 状态管理
├── tools/                         # 工具库
│   ├── base/                     # 工具基类
│   ├── input/                    # 输入处理工具
│   ├── style_converters/         # 风格转换工具
│   ├── content_processors/       # 内容处理工具
│   ├── generators/              # 生成工具
│   └── output/                  # 输出工具
├── graphs/                       # LangGraph图定义
│   └── preset_graphs/           # 预设图模板
├── docs/                        # 文档
├── tests/                       # 测试文件
├── output/                      # 输出目录
│   └── images/                 # 生成的图片
└── venv/                       # 虚拟环境
```

## 🔧 开发指南

### 添加新工具

1. **创建工具类**
```python
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.STYLE_CONVERTER, "meme")
class MemeStyleTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.description = "转换为meme风格"
        self.input_keys = ["raw_content"]
        self.output_keys = ["processed_content"]
    
    def process(self, input_data):
        # 实现处理逻辑
        return {"processed_content": "处理后的内容"}
```

2. **添加测试函数**
```python
def test_meme_style_tool():
    tool = MemeStyleTool()
    result = tool.process({"raw_content": "测试文本"})
    assert "processed_content" in result
```

3. **注册到系统**
```python
# 在主程序中导入以触发注册
import tools.style_converters.meme_style
```

### 创建新的预设流程

```python
class MemePresetGraph:
    def __init__(self):
        self.tools = [
            "input.text_input",
            "analyzer.content", 
            "style_converter.meme",
            "generator.line_art",
            "output.image"
        ]
    
    def execute(self, state):
        # 实现执行逻辑
        pass
```

## 📊 性能指标

### 测试结果
- ✅ 工具单元测试: 6/6 通过
- ✅ 端到端测试: 2/2 通过  
- ✅ 图片生成成功率: 80%+
- ⚡ 平均处理时间: 30-60秒

### 支持的输入格式
- 📝 纯文本
- 📊 CSV数据文件
- 🔗 URL链接 (待实现)

### 支持的输出格式
- 🖼️ PNG图片
- 📄 HTML文件
- 📋 PDF文档 (待实现)

## 🛣️ 发展路线图

### 短期目标 (1-2周)
- [ ] 添加meme风格转换工具
- [ ] 创建商务风格预设流程
- [ ] 集成LangGraph图执行
- [ ] Web界面原型

### 中期目标 (1个月)
- [ ] 支持视频生成
- [ ] 添加更多输入源
- [ ] 性能优化
- [ ] 用户界面完善

### 长期目标 (3个月)
- [ ] 多语言支持
- [ ] 插件系统
- [ ] 云端部署
- [ ] API服务

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [LangChain](https://github.com/langchain-ai/langchain) - LLM应用框架
- [LangGraph](https://github.com/langchain-ai/langgraph) - 图执行引擎
- [Playwright](https://playwright.dev/) - 浏览器自动化

---

💡 **提示**: 这是一个智能化的图文生成系统，可以根据用户需求自动选择合适的处理流程。欢迎尝试不同的输入和风格！