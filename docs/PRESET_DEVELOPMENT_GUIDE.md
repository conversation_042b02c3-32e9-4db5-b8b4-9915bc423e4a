# LangGraph Preset 开发交接指南

## 🎯 核心架构理念

### 三层分离设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Preset Flow   │    │  Tool Registry  │    │ Universal State │
│   (业务编排)     │◄──►│   (能力插件)     │◄──►│   (数据流)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**核心思想**：业务逻辑与技术实现解耦，数据与处理分离

## 🏗️ 开发核心原则

### 1. **状态驱动模式**
- 所有数据通过 `UniversalState` 流转
- 工具间通过状态字段通信，避免直接耦合
- 新媒体类型只需扩展状态字段

### 2. **工具组合哲学**
- 每个工具专注单一职责
- 通过 `ToolRegistry` 注册，按需组合
- 工具可跨预设复用

### 3. **参数集中管理**
- 所有配置在 `self.params` 中定义
- 运行时通过状态传递给工具
- 配置与代码分离

### 4. **统一错误处理**
- 工具返回格式：`{"error_message": "详细错误信息"}`
- Preset层统一检查和处理
- 快速失败，不阻塞其他流程

## 🚀 快速开发流程

### Step 1: 复制基础框架
```bash
# 复制现有preset作为模板
cp graphs/preset_graphs/story_cards_preset.py graphs/preset_graphs/your_new_preset.py
```

### Step 2: 定义数据流
在 `core/state.py` 中扩展字段：
```python
# 根据你的媒体类型添加字段
audio_paths: Optional[List[str]]           # 音频文件路径
video_segments: Optional[List[Dict]]       # 视频片段信息
stream_config: Optional[Dict]              # 流配置信息
```

### Step 3: 配置参数
```python
self.params = {
    # 输入配置
    "input_format": "mp4",
    "quality_level": "high",
    
    # 处理配置  
    "processing_strategy": "real_time",
    "output_format": "stream",
    
    # 业务特定配置
    "your_specific_param": "value"
}
```

### Step 4: 定义工具链
```python
def execute(self, initial_state):
    # 1. 数据预处理
    result1 = self._execute_tool("input.your_reader", state)
    
    # 2. 核心处理  
    result2 = self._execute_tool("processor.your_processor", state)
    
    # 3. 结果输出
    result3 = self._execute_tool("output.your_exporter", state)
    
    return final_state
```

### Step 5: 复用通用方法
直接复制这些方法（无需修改）：
- `_check_tool_error()` - 错误检查
- `_setup_session()` - 会话管理
- `_format_context()` - 上下文格式化

## 🛠️ 工具开发模式

### 创建新工具
```python
@ToolRegistry.register(ToolCategory.GENERATOR, "your_tool")
class YourTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.input_keys = ["input_field"]
        self.output_keys = ["output_field"]
    
    def process(self, input_data):
        # 核心处理逻辑
        return {"output_field": result}
```

### 工具分类指南
- `INPUT`: 数据输入 (file, stream, api)
- `ANALYZER`: 内容分析 (quality, sentiment)  
- `PROCESSOR`: 数据处理 (segment, transform)
- `GENERATOR`: 内容生成 (text, image, audio)
- `OUTPUT`: 结果输出 (file, upload, stream)

## 📁 项目结构说明

```
graphs/preset_graphs/          # 业务预设层
├── story_cards_preset.py      # 图文卡片预设 (参考模板)
└── your_new_preset.py         # 你的新预设

tools/                         # 工具能力层
├── input/                     # 输入工具
├── analyzers/                 # 分析工具
├── generators/                # 生成工具
└── output/                    # 输出工具

core/                          # 基础设施层
├── state.py                   # 状态管理
├── parameter_manager.py       # 参数管理
└── tool_interface.py          # 工具接口
```

## 🎯 实战经验总结

### 开发顺序建议
1. 先定义数据流（状态字段）
2. 再设计工具链（处理步骤）
3. 最后实现细节（具体工具）

### 调试技巧
- 在每个处理步骤后打印状态
- 使用 `_check_tool_error()` 统一错误处理
- 利用 `session_id` 追踪执行过程

### 常见陷阱
- ❌ 在Preset中写具体实现逻辑
- ❌ 工具间直接调用
- ❌ 参数硬编码在代码中
- ✅ 保持分层清晰，职责单一

## 🔧 扩展点说明

### 支持新媒体类型
1. 在 `UniversalState` 中添加媒体相关字段
2. 创建对应的工具 (input/processor/output)
3. 编写预设组合这些工具

### 添加新处理能力
1. 实现 `BaseTool` 接口
2. 注册到 `ToolRegistry`
3. 在预设中调用

### 自定义输出格式
1. 实现 `OUTPUT` 类别的工具
2. 在预设的最后步骤调用

## 📚 参考资料

- **完整示例**: `graphs/preset_graphs/story_cards_preset.py`
- **工具示例**: `tools/generators/ai_image_generator.py`  
- **状态管理**: `core/state.py`
- **工具注册**: `tools/base/tool_registry.py`

## 💡 设计哲学

这个架构的核心是**组合优于继承**：
- 通过组合工具实现复杂功能
- 通过状态传递实现数据流转  
- 通过参数配置实现灵活控制

无论你要做图文生成、音视频处理、还是实时流媒体，都能复用这套架构模式！