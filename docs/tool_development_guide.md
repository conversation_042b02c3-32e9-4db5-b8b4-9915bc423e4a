# 通用Tool开发指南

基于FluxImageEditor的开发经验，总结出通用Tool编写的最佳实践。

## 核心设计原则

### 1. 单一职责原则
每个Tool只做一件事，并且做好这件事。

```python
# ✅ 好的设计 - 专注于图片编辑
class FluxImageEditor(BaseTool):
    """专门负责图片编辑的工具"""
    
# ❌ 不好的设计 - 职责混乱
class ImageProcessor(BaseTool):
    """既做编辑又做生成还做转换..."""
```

### 2. 清晰的输入输出契约
明确定义Tool的输入和输出，便于组合和调试。

```python
class FluxImageEditor(BaseTool):
    def __init__(self):
        super().__init__()
        # 明确定义输入输出
        self.input_keys = ["input_image", "edit_prompt", "edit_config"]
        self.output_keys = ["edited_image_url", "edit_metadata"]
```

### 3. 配置驱动的设计
通过配置参数控制行为，而不是硬编码。

```python
# ✅ 配置驱动
def _build_edit_payload(self, input_image: str, edit_prompt: str, edit_config: Dict[str, Any]):
    default_config = {
        "model": "black-forest-labs/flux-kontext-pro",
        "aspect_ratio": "match_input_image",
        "output_format": "png"
    }
    # 用户配置覆盖默认值
    config = {**default_config, **edit_config}
```

## Tool架构模板

### 基础结构
```python
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.GENERATOR, "your_tool_name")
class YourTool(BaseTool):
    """工具描述"""
    
    def __init__(self):
        super().__init__()
        self.category = ToolCategory.GENERATOR  # 选择合适的类别
        self.description = "详细描述工具功能"
        self.input_keys = ["input1", "input2"]  # 必需输入
        self.output_keys = ["output1", "output2"]  # 输出字段
        
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑"""
        # 1. 参数提取和验证
        # 2. 核心业务逻辑
        # 3. 结果封装和返回
        pass
```

### 分层设计模式
```python
def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """主处理流程 - 编排各个子步骤"""
    try:
        # 第1层：输入验证
        self._validate_inputs(input_data)
        
        # 第2层：配置准备
        config = self._prepare_config(input_data)
        
        # 第3层：核心业务逻辑
        result = self._execute_core_logic(input_data, config)
        
        # 第4层：后处理
        final_result = self._post_process(result)
        
        return final_result
        
    except Exception as e:
        return {"error_message": f"处理失败: {str(e)}"}

def _validate_inputs(self, input_data: Dict[str, Any]) -> None:
    """输入验证层"""
    pass

def _prepare_config(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
    """配置准备层"""
    pass

def _execute_core_logic(self, input_data: Dict[str, Any], config: Dict[str, Any]) -> Any:
    """核心业务逻辑层"""
    pass

def _post_process(self, raw_result: Any) -> Dict[str, Any]:
    """后处理层"""
    pass
```

## 最佳实践

### 1. 健壮的错误处理
```python
def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        # 业务逻辑
        pass
    except requests.exceptions.RequestException as e:
        # 网络请求错误
        return {"error_message": f"网络请求失败: {str(e)}"}
    except ValueError as e:
        # 参数验证错误
        return {"error_message": f"参数错误: {str(e)}"}
    except Exception as e:
        # 其他未知错误
        return {"error_message": f"未知错误: {str(e)}"}
```

### 2. 灵活的配置管理
```python
def _get_api_config(self) -> Dict[str, str]:
    """集中管理API配置"""
    required_vars = ["FLUX_API_KEY", "FLUX_BASE_URL"]
    config = {}
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            raise ValueError(f"环境变量中必须设置 {var}")
        config[var.lower()] = value
    
    return config
```

### 3. 工具组合设计
```python
def _upload_to_storage(self, image_url: str) -> Dict[str, str]:
    """使用其他工具进行后处理"""
    from tools.output.cos_uploader import CosUploaderTool
    uploader = CosUploaderTool()
    
    # 工具链式调用
    result = uploader.process({
        "file_content": self._download_image(image_url),
        "file_extension": "png"
    })
    
    return result
```

### 4. 详细的元数据返回
```python
return {
    "edited_image_url": final_url,
    "edit_metadata": {
        # 追踪信息
        "task_id": task_id,
        "original_image": input_image,
        "edit_prompt": edit_prompt,
        
        # 配置信息
        "model": config["model"],
        "aspect_ratio": config["aspect_ratio"],
        
        # 执行信息
        "storage_info": storage_result,
        "processing_time": "completed"
    }
}
```

## 工具类别指南

### INPUT (输入工具)
- 处理用户输入和外部数据源
- 例：CSV读取、文本输入、文件上传

### ANALYZER (分析工具) 
- 分析和解析数据
- 例：意图识别、内容分析、数据解析

### STYLE_CONVERTER (风格转换工具)
- 转换内容风格和格式
- 例：小红书风格转换、文案重写

### CONTENT_PROCESSOR (内容处理工具)
- 处理和变换内容
- 例：文本分段、内容过滤、格式转换

### GENERATOR (生成工具)
- 生成新的内容或资源
- 例：HTML生成、图片生成、文本生成

### OUTPUT (输出工具)
- 输出和导出结果
- 例：文件保存、云端上传、格式转换

## 测试指南

### 1. 单元测试结构
```python
def test_your_tool():
    """测试工具功能"""
    print("🧪 测试 YourTool")
    
    tool = YourTool()
    
    # 测试用例1：正常情况
    test_input = {"param1": "value1", "param2": "value2"}
    result = tool.process(test_input)
    assert "output1" in result
    print("✅ 正常情况测试通过")
    
    # 测试用例2：边界情况
    edge_input = {"param1": "", "param2": None}
    result = tool.process(edge_input)
    assert "error_message" in result
    print("✅ 边界情况测试通过")
    
    # 测试用例3：错误情况
    error_input = {}
    result = tool.process(error_input)
    assert "error_message" in result
    print("✅ 错误情况测试通过")
```

### 2. 集成测试
```python
def test_tool_integration():
    """测试工具集成"""
    # 模拟真实环境配置
    # 测试与其他工具的集成
    # 验证状态管理的正确性
```

## 文档要求

### 1. 代码注释
```python
def _validate_input_image(self, image_url: str) -> bool:
    """
    验证输入图片URL
    
    Args:
        image_url: 图片URL
        
    Returns:
        bool: 是否有效
    """
    if not image_url.startswith(('http://', 'https://')):
        return False
    return True
```

### 2. 使用示例
为每个工具创建详细的使用示例，包含：
- 基础用法
- 高级配置
- 错误处理
- 与状态管理的集成

## 性能优化

### 1. 异步操作
对于IO密集型操作（API调用、文件上传），考虑异步实现。

### 2. 缓存机制
对于重复性操作，实现适当的缓存。

### 3. 资源管理
及时释放资源，避免内存泄露。

## 总结

通过遵循这些设计原则和最佳实践，我们可以创建：
- 🔧 **可复用的工具**：清晰的接口，易于组合
- 🛡️ **健壮的系统**：完善的错误处理和验证
- 📈 **可扩展的架构**：分层设计，易于维护
- 🧪 **可测试的代码**：明确的输入输出，便于验证

记住：**好的工具就像乐高积木，简单、可靠、易于组合！**