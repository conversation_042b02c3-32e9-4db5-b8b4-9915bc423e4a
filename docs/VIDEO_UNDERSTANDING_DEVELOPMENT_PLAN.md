# 视频理解流程开发计划

## 📋 项目概述

基于现有的智能图片生成助手架构，开发一个完整的视频理解流程，使用Gemini多模态模型分析视频内容并输出结构化的视频脚本JSON。

## 🎯 核心需求

### 输入要求
- **支持格式**: URL输入 + 本地文件上传
- **视频格式**: MP4、AVI、MOV、MKV、WEBM、FLV、WMV、M4V等
- **文件大小**: 限制20MB以内
- **验证机制**: 格式检查、大小检查、可访问性验证

### 分析深度
- **时间精度**: 秒级时间轴分析
- **多语言支持**: 依赖Gemini原生多语言能力
- **角色识别**: 依赖Gemini原生识别能力
- **内容分析**: 场景、镜头类型、摄像机运动、对话、音效等

### 输出格式
严格按照以下JSON结构输出：

```json
{
  "videoMetaDataDTO": {
    "videoTitle": "string",
    "videoCover": "string", 
    "videoTheme": "string",
    "narrativeStructure": "string",
    "best3sec": "string",
    "visualStyle": "string",
    "bgmStyle": "string",
    "characterSheet": [
      {
        "name": "string",
        "description": "string", 
        "promptCn": "string",
        "promptEn": "string"
      }
    ],
    "others": "string"  // AI自由发挥重要信息
  },
  "shots": [
    {
      "shotNumber": 0,
      "startTime": 0,
      "endTime": 0, 
      "duration": 0,
      "scene": "string",
      "characters": ["string"],
      "shotType": "EXTREME_LONG_SHOT|LONG_SHOT|MEDIUM_SHOT|CLOSE_UP|EXTREME_CLOSE_UP",
      "cameraMovement": "STATIC|PAN|TILT|ZOOM_IN|ZOOM_OUT|DOLLY|TRACKING",
      "description": "string",
      "bgmDescription": "string",
      "bgmSource": "string", 
      "dialogue": ["string"],
      "onScreenText": ["string"],
      "sfxDescription": ["string"]
    }
  ]
}
```

### 集成方式
- 先开发独立工作流
- 基于现有BaseTool架构
- 使用UniversalState状态管理
- 集成到LangGraph工作流中

## 🏗️ 技术架构

### 工具系统设计
```
tools/video_processors/
├── video_input.py           # 视频输入处理工具
├── video_script_analyzer.py # 视频脚本分析工具 (已创建)
└── script_validator.py      # 脚本格式验证工具

tools/output/
└── video_script_output.py   # 脚本输出工具
```

### 工作流设计
```mermaid
graph TD
    A[视频输入] --> B[格式验证]
    B --> C[Gemini分析]
    C --> D[JSON解析]
    D --> E[格式验证]
    E --> F[输出保存]
```

### 状态管理扩展
扩展UniversalState支持视频处理：
- video_file_path: 视频文件路径
- video_metadata: 视频元数据
- video_script_json: 结构化脚本
- analysis_summary: 分析摘要

## 📅 开发计划

### Phase 1: 基础工具开发
- [x] ~~需求分析和架构设计~~
- [ ] **视频输入处理工具开发** (当前进行中)
  - 支持URL和本地文件
  - 格式验证和大小限制
  - 元数据提取
- [ ] **视频脚本分析工具完善**
  - 优化Gemini API调用
  - 完善提示词设计
  - 提高JSON输出质量

### Phase 2: 验证和输出
- [ ] **脚本格式验证工具**
  - JSON结构验证
  - 字段完整性检查
  - 数据类型验证
  - 错误修复机制
- [ ] **输出工具开发**
  - 文件保存功能
  - 格式化输出
  - 结果展示

### Phase 3: 工作流集成
- [ ] **LangGraph工作流构建**
  - 节点定义和连接
  - 状态流转管理
  - 错误处理机制
- [ ] **工具注册和集成**
  - 注册到工具注册表
  - 集成到智能助手
  - 工作流测试

### Phase 4: 测试和优化
- [ ] **功能测试**
  - 单元测试
  - 集成测试
  - 端到端测试
- [ ] **性能优化**
  - API调用优化
  - 错误处理改进
  - 用户体验提升

## 🔧 技术实现要点

### Gemini API集成
- 使用OpenAI兼容的Chat Completions API
- 模型: `gemini-2.5-pro-preview-06-05`
- 温度设置: 0.1 (确保结构化输出一致性)
- 超时设置: 180秒 (视频分析耗时较长)

### 提示词设计
- 明确JSON格式要求
- 详细的字段说明
- 镜头类型和摄像机运动枚举
- 时间轴分析要求
- others字段的自由发挥指导

### 错误处理
- API调用失败处理
- JSON解析错误恢复
- 文件格式不支持处理
- 网络超时重试机制

### 配置管理
- 环境变量配置 (VIDEO_UNDERSTAND_KEY, VIDEO_UNDERSTAND_URL)
- 兼容现有配置系统
- 灵活的模型选择

## 📊 验收标准

### 功能验收
- [ ] 支持URL和本地文件输入
- [ ] 正确处理多种视频格式
- [ ] 严格执行20MB大小限制
- [ ] 输出完整的JSON结构
- [ ] 秒级时间轴准确性
- [ ] 多语言视频支持

### 质量验收  
- [ ] 代码符合现有架构规范
- [ ] 完善的错误处理机制
- [ ] 详细的日志记录
- [ ] 单元测试覆盖率 > 80%
- [ ] 文档完整性

### 性能验收
- [ ] 单个视频处理时间 < 3分钟
- [ ] API调用成功率 > 95%
- [ ] JSON解析成功率 > 90%
- [ ] 内存使用合理

## 🚀 后续扩展计划

### 功能扩展
- 批量视频处理
- 视频片段分析
- 实时视频流处理
- 自定义分析模板

### 集成扩展
- 与现有图片生成流程联动
- 视频编辑工具集成
- 云存储支持
- API接口开放

## 📝 开发注意事项

1. **严格遵循现有架构**: 继承BaseTool，使用UniversalState
2. **API配置兼容性**: 复用现有配置系统
3. **错误处理完善**: 考虑各种异常情况
4. **日志记录详细**: 便于调试和监控
5. **文档同步更新**: 保持文档与代码一致
6. **测试驱动开发**: 先写测试再实现功能

## 🎉 开发完成状态

### ✅ 已完成功能
- [x] **视频输入处理工具** - 支持URL和本地文件，格式验证，20MB限制
- [x] **视频脚本分析工具** - 基于Gemini的多模态分析，优化提示词
- [x] **脚本格式验证工具** - JSON格式验证和自动修复
- [x] **输出工具** - 文件保存和结果展示
- [x] **完整工作流** - 基于LangGraph的端到端流程
- [x] **测试脚本** - 全面的功能测试

### 📁 文件结构
```
tools/video_processors/
├── video_input.py           ✅ 视频输入处理
├── video_script_analyzer.py ✅ Gemini脚本分析
├── script_validator.py      ✅ 格式验证
└── __init__.py              ✅ 工具注册

tools/output/
└── video_script_output.py   ✅ 输出处理

video_understanding_workflow.py ✅ 主工作流
test_video_understanding.py     ✅ 测试脚本
```

## 🚀 使用指南

### 环境配置
```bash
# 设置API密钥
export VIDEO_UNDERSTAND_KEY="your-gemini-api-key"
export VIDEO_UNDERSTAND_URL="your-api-base-url"

# 或使用OpenAI兼容配置
export OPENAI_API_KEY="your-api-key"
export OPENAI_BASE_URL="your-base-url"
```

### 快速开始
```bash
# 测试工作流
python test_video_understanding.py

# 分析视频URL
python video_understanding_workflow.py --video_url "https://example.com/video.mp4"

# 分析本地文件
python video_understanding_workflow.py --video_path "/path/to/video.mp4"
```

### 输出示例
生成的JSON文件包含完整的视频脚本结构，保存在 `output/video_scripts/` 目录。

---

## 🎯 支持的Ability类型

系统支持9种核心ability类型，可根据镜头内容智能选择：

1. **TEXT_TO_IMAGE** - 文生图，适用于静态场景、角色特写、风景镜头
2. **IMAGE_TO_IMAGE** - 图生图，适用于风格转换、图像编辑
3. **TEXT_TO_VIDEO** - 文生视频，适用于动态场景、运动镜头
4. **IMAGE_TO_VIDEO** - 图生视频，适用于从静态到动态的转换
5. **DIGITAL_HUMAN** - 数字人，适用于角色对话、表情变化
6. **MOTION_CAPTURE** - 动作捕捉，适用于复杂动作、舞蹈
7. **FACE_SWAP** - 换脸，适用于角色替换
8. **VOICE_CLONE** - 声音克隆，适用于配音生成
9. **STYLE_TRANSFER** - 风格迁移，适用于艺术风格转换

### 智能选择示例
```json
{
  "prompts": {
    "ability": [
      {
        "abilityType": "TEXT_TO_VIDEO",
        "parameters": {
          "duration": "7",
          "fps": 24,
          "motion": "A giant rabbit emerges from a burrow"
        }
      }
    ],
    "cn": "详细的中文提示词...",
    "en": "Detailed English prompt..."
  }
}
```

## 🚀 大文件预处理解决方案

### 📊 问题分析
- **20MB限制**: 现代视频文件通常超过20MB
- **用户体验**: 频繁遇到"文件过大"错误
- **内容完整性**: 不能因为大小限制而丢失重要内容

### 🛠️ 解决方案：压缩 + 分段

#### 渐进式处理策略
```
原始视频 → 检查大小 → 尝试压缩 → 检查效果 → 分段处理 → 分别分析 → 合并结果
```

#### 技术实现
1. **智能压缩** - 多级压缩策略，保持内容完整
2. **智能分段** - 按时间或场景分割，确保每段≤20MB
3. **分段分析** - 每段独立调用Gemini分析
4. **结果合并** - 智能合并时间轴和镜头编号

### 📁 新增工具
- `VideoPreprocessor` - 视频预处理（压缩+分段）
- `SegmentMerger` - 分段结果合并
- `VideoUnderstandingWorkflowV2` - 支持大文件的完整工作流

### ✅ 测试验证
- ✅ 压缩功能正常
- ✅ 分段功能正常
- ✅ 结果合并准确
- ✅ 时间轴连续性保持
- ✅ 镜头编号正确调整

---

**文档版本**: v4.0
**创建时间**: 2025-01-25
**最后更新**: 2025-01-25
**开发状态**: ✅ 完成（支持大文件）
**负责人**: AI Assistant
