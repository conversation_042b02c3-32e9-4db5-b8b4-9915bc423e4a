# 视频二创系统设计方案

## 🎯 系统概述

### 核心功能流程
```
用户上传视频 → 视频理解 → 生成JSON → 用户二创指令 → JSON修改 → 内容匹配 → 工具选择 → 生成二创视频
```

### 系统特点
- **智能理解**: 深度解析视频内容，生成结构化JSON
- **灵活二创**: 支持多种二创指令，如内容替换、风格转换、元素添加等
- **自动匹配**: 根据JSON内容自动选择合适的生成工具
- **无缝集成**: 基于现有架构，充分利用工具生态系统

## 🏗️ 系统架构设计

### 1. 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────────────────────────────────────────────────┤
│  视频上传接口  │  二创指令接口  │  结果下载接口              │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    业务编排层                                │
├─────────────────────────────────────────────────────────────┤
│  视频理解工作流  │  二创处理工作流  │  视频生成工作流        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    核心处理层                                │
├─────────────────────────────────────────────────────────────┤
│  JSON解析器  │  指令解析器  │  内容匹配器  │  工具选择器    │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                    工具生态层                                │
├─────────────────────────────────────────────────────────────┤
│  视频理解  │  图像生成  │  文本生成  │  音频处理  │  视频合成 │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据流设计
```
原始视频 → 视频理解JSON → 二创指令 → 修改后JSON → 内容生成 → 二创视频
```

## 📋 核心组件设计

### 1. 视频理解JSON格式

#### 标准化JSON结构
```json
{
  "video_metadata": {
    "duration": 120.5,
    "resolution": "1920x1080",
    "fps": 30,
    "format": "mp4"
  },
  "scenes": [
    {
      "scene_id": "scene_001",
      "start_time": 0.0,
      "end_time": 15.2,
      "description": "开场介绍场景",
      "visual_elements": {
        "main_subject": "主播",
        "background": "办公室环境",
        "objects": ["电脑", "书籍", "咖啡杯"],
        "colors": ["蓝色", "白色", "木色"],
        "style": "现代简约"
      },
      "audio_elements": {
        "speech": {
          "speaker": "主播",
          "content": "大家好，今天我们来聊聊...",
          "emotion": "友好",
          "speed": "正常"
        },
        "background_music": {
          "genre": "轻音乐",
          "mood": "轻松"
        },
        "sound_effects": []
      },
      "text_elements": {
        "titles": ["今日话题"],
        "subtitles": "大家好，今天我们来聊聊...",
        "annotations": []
      },
      "editing_info": {
        "transitions": "淡入",
        "effects": [],
        "camera_movement": "静态"
      }
    }
  ],
  "global_elements": {
    "theme": "科技分享",
    "tone": "专业友好",
    "target_audience": "技术爱好者",
    "key_messages": ["技术分享", "实用技巧"]
  }
}
```

### 2. 二创指令系统

#### 指令类型定义
```python
from enum import Enum
from typing import Dict, Any, List

class CreativeAction(Enum):
    REPLACE_CONTENT = "replace_content"      # 替换内容
    CHANGE_STYLE = "change_style"            # 改变风格
    ADD_ELEMENT = "add_element"              # 添加元素
    REMOVE_ELEMENT = "remove_element"        # 移除元素
    MODIFY_TIMING = "modify_timing"          # 修改时间
    CHANGE_VOICE = "change_voice"            # 改变声音
    ADD_EFFECT = "add_effect"                # 添加特效

class CreativeInstruction:
    def __init__(self):
        self.action: CreativeAction = None
        self.target: str = ""                # 目标对象
        self.parameters: Dict[str, Any] = {} # 操作参数
        self.scope: str = "scene"            # 作用范围: scene/global
        self.scene_ids: List[str] = []       # 影响的场景ID
```

#### 指令解析示例
```python
# 用户输入: "把第一个场景的背景换成海滩"
instruction = CreativeInstruction()
instruction.action = CreativeAction.REPLACE_CONTENT
instruction.target = "visual_elements.background"
instruction.parameters = {"new_value": "海滩环境"}
instruction.scope = "scene"
instruction.scene_ids = ["scene_001"]

# 用户输入: "把整个视频的风格改成卡通风格"
instruction = CreativeInstruction()
instruction.action = CreativeAction.CHANGE_STYLE
instruction.target = "visual_elements.style"
instruction.parameters = {"new_style": "卡通风格"}
instruction.scope = "global"
```

### 3. 内容匹配和工具选择

#### 内容类型映射
```python
CONTENT_TOOL_MAPPING = {
    # 视觉内容
    "visual_elements.main_subject": {
        "人物": ["ai_character_generator", "stable_diffusion"],
        "动物": ["animal_generator", "stable_diffusion"],
        "物体": ["object_generator", "stable_diffusion"]
    },
    "visual_elements.background": {
        "室内": ["interior_generator", "stable_diffusion"],
        "室外": ["landscape_generator", "stable_diffusion"],
        "抽象": ["abstract_generator", "stable_diffusion"]
    },
    
    # 音频内容
    "audio_elements.speech": {
        "男声": ["male_voice_generator"],
        "女声": ["female_voice_generator"],
        "儿童声": ["child_voice_generator"]
    },
    "audio_elements.background_music": {
        "轻音乐": ["light_music_generator"],
        "电子音乐": ["electronic_music_generator"],
        "古典音乐": ["classical_music_generator"]
    },
    
    # 文本内容
    "text_elements.titles": ["text_generator", "title_designer"],
    "text_elements.subtitles": ["subtitle_generator"],
    
    # 特效
    "editing_info.effects": {
        "粒子特效": ["particle_effect_generator"],
        "光效": ["light_effect_generator"],
        "转场": ["transition_generator"]
    }
}
```

## 🔧 核心工作流设计

### 1. 视频理解增强工作流
```python
class EnhancedVideoUnderstandingWorkflow:
    """增强的视频理解工作流，输出结构化JSON"""
    
    def execute(self, video_path: str) -> Dict[str, Any]:
        # 1. 基础视频理解
        basic_analysis = self._basic_video_analysis(video_path)
        
        # 2. 场景分割和详细分析
        scenes = self._scene_segmentation(video_path)
        detailed_scenes = []
        
        for scene in scenes:
            scene_analysis = {
                "scene_id": scene["id"],
                "start_time": scene["start"],
                "end_time": scene["end"],
                "visual_elements": self._analyze_visual_elements(scene),
                "audio_elements": self._analyze_audio_elements(scene),
                "text_elements": self._analyze_text_elements(scene),
                "editing_info": self._analyze_editing_info(scene)
            }
            detailed_scenes.append(scene_analysis)
        
        # 3. 全局元素分析
        global_elements = self._analyze_global_elements(basic_analysis)
        
        return {
            "video_metadata": basic_analysis["metadata"],
            "scenes": detailed_scenes,
            "global_elements": global_elements
        }
```

### 2. 二创处理工作流
```python
class CreativeEditingWorkflow:
    """二创处理工作流"""
    
    def execute(self, original_json: Dict, instructions: List[CreativeInstruction]) -> Dict[str, Any]:
        modified_json = original_json.copy()
        generation_tasks = []
        
        for instruction in instructions:
            # 1. 解析指令
            parsed_instruction = self._parse_instruction(instruction)
            
            # 2. 修改JSON
            modified_json = self._apply_instruction(modified_json, parsed_instruction)
            
            # 3. 生成内容任务
            tasks = self._create_generation_tasks(parsed_instruction)
            generation_tasks.extend(tasks)
        
        return {
            "modified_json": modified_json,
            "generation_tasks": generation_tasks
        }
```

### 3. 视频生成工作流
```python
class VideoGenerationWorkflow:
    """视频生成工作流"""
    
    def execute(self, modified_json: Dict, generation_tasks: List) -> str:
        generated_assets = {}
        
        # 1. 并行生成所有资源
        for task in generation_tasks:
            asset = self._generate_asset(task)
            generated_assets[task["id"]] = asset
        
        # 2. 视频合成
        video_path = self._compose_video(modified_json, generated_assets)
        
        return video_path
```

## 🎨 用户交互设计

### API接口设计
```python
# 1. 视频上传和理解
POST /api/video/upload
{
    "video_file": "base64_encoded_video",
    "analysis_options": {
        "detail_level": "high",
        "include_audio": true,
        "include_text": true
    }
}

# 2. 二创指令提交
POST /api/video/creative-edit
{
    "video_id": "video_123",
    "instructions": [
        {
            "action": "replace_content",
            "target": "visual_elements.background",
            "parameters": {"new_value": "海滩环境"},
            "scene_ids": ["scene_001"]
        }
    ]
}

# 3. 生成状态查询
GET /api/video/generation-status/{task_id}

# 4. 结果下载
GET /api/video/download/{video_id}
```

### 前端交互流程
1. **视频上传**: 拖拽上传，显示理解进度
2. **JSON展示**: 可视化展示视频结构
3. **二创编辑**: 直观的编辑界面，支持预览
4. **生成监控**: 实时显示生成进度
5. **结果预览**: 在线预览和下载

## 📊 技术实现要点

### 1. 性能优化
- **并行处理**: 多个生成任务并行执行
- **缓存机制**: 缓存常用的生成结果
- **增量更新**: 只重新生成修改的部分

### 2. 质量保证
- **内容一致性**: 确保生成内容与原视频风格一致
- **时间同步**: 保证音视频同步
- **质量检测**: 自动检测生成质量

### 3. 扩展性设计
- **插件化工具**: 支持新的生成工具接入
- **模板系统**: 预定义常用的二创模板
- **批量处理**: 支持批量视频处理

---

**下一步**: 开始实现核心组件的详细代码
