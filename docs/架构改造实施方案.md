# 视频二创平台架构改造实施方案

## 🎯 改造目标

将现有的视频理解系统改造为基于蛙蛙框架的极简双节点架构，实现：
1. **视频理解工作流** → **Video Understanding Expert Tool**
2. **复杂的多步骤处理** → **执行引擎驱动的智能编排**
3. **用户友好的自然语言交互** → **Master Agent统一处理**

## 📋 改造步骤

### 第一阶段：核心架构搭建 (1-2周)

#### 1.1 创建双节点Graph结构

```python
# src/video_creation_graph/builder.py
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

class VideoCreationGraphBuilder:
    """视频二创图构建器"""
    
    def __init__(self):
        self.builder = StateGraph(VideoCreationState)
        self._setup_nodes()
        self._setup_edges()
    
    def _setup_nodes(self):
        """设置节点"""
        # 只有2个核心节点
        self.builder.add_node("master_agent", master_agent_node)
        self.builder.add_node("execution_engine", execution_engine_node)
        
        # 设置入口点
        self.builder.set_entry_point("master_agent")
    
    def _setup_edges(self):
        """设置边和路由"""
        # 智能路由
        self.builder.add_conditional_edges(
            "master_agent",
            should_continue,
            {
                "execution_engine": "execution_engine",
                "master_agent": "master_agent", 
                END: END
            }
        )
        
        # 执行引擎回到Master Agent
        self.builder.add_edge("execution_engine", "master_agent")
    
    def build(self, checkpointer=None):
        """构建图"""
        if checkpointer is None:
            checkpointer = MemorySaver()
        return self.builder.compile(checkpointer=checkpointer)
```

#### 1.2 定义状态结构

```python
# src/video_creation_graph/types.py
from typing import TypedDict, Annotated, Optional, List, Dict, Any
from langgraph.graph.message import add_messages

class VideoCreationState(TypedDict):
    """视频二创状态"""
    messages: Annotated[list, add_messages]
    plan: Optional[Dict[str, Any]]
    
    # 视频相关
    original_video: Optional[Dict[str, Any]]
    video_json: Optional[Dict[str, Any]]
    edited_json: Optional[Dict[str, Any]]
    rendered_videos: Optional[List[str]]
    
    # 编辑相关
    edit_instructions: Optional[str]
    edit_history: Optional[List[Dict]]
    
    # 模板和执行
    template_id: Optional[str]
    template_params: Optional[Dict[str, Any]]
    current_step: Optional[str]
```

#### 1.3 智能路由逻辑

```python
# src/video_creation_graph/routing.py
from langchain_core.messages import AIMessage

def should_continue(state: VideoCreationState) -> str:
    """智能路由判断"""
    messages = state.get("messages", [])
    if not messages:
        return "master_agent"
    
    last_message = messages[-1]
    
    # 1. 检查是否需要执行引擎
    if should_use_execution_engine(state):
        plan = state.get("plan")
        if plan and not is_plan_complete(plan):
            return "execution_engine"
    
    # 2. 检查是否结束对话
    if isinstance(last_message, AIMessage) and not getattr(last_message, 'tool_calls', None):
        return END
    
    # 3. 继续Master Agent处理
    return "master_agent"

def should_use_execution_engine(state: VideoCreationState) -> bool:
    """判断是否使用执行引擎"""
    plan = state.get("plan")
    if not plan:
        return False
    
    steps = plan.get("steps", [])
    
    # 单步或简单任务，Master Agent直接处理
    if len(steps) <= 1:
        return False
    
    # 来自模板的计划，使用执行引擎
    if plan.get("is_from_template", False):
        return True
    
    # 多步骤复杂任务，使用执行引擎
    if len(steps) > 2:
        return True
    
    return False
```

### 第二阶段：专家工具封装 (2-3周)

#### 2.1 Video Understanding Expert

```python
# src/tools/experts/video_understanding_expert.py
from langchain_core.tools import StructuredTool
from langchain_core.messages import HumanMessage
from typing import Dict, Any, Optional

def get_video_understanding_expert_tool() -> StructuredTool:
    """创建视频理解专家工具"""
    
    def run_video_understanding_expert(
        video_input: str,
        analysis_request: str = "请详细分析这个视频的内容结构",
        context: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        视频理解专家工具
        
        Args:
            video_input: 视频路径或URL
            analysis_request: 分析需求描述
            context: 上下文信息
            
        Returns:
            标准化的专家工具输出格式
        """
        try:
            # 调用现有的视频理解工作流
            from src.workflows.video_understanding import run_video_understanding_v2
            
            # 判断输入类型
            if video_input.startswith(('http://', 'https://')):
                result = run_video_understanding_v2(
                    video_url=video_input,
                    user_request=analysis_request
                )
            else:
                result = run_video_understanding_v2(
                    video_path=video_input,
                    user_request=analysis_request
                )
            
            if result and not result.get("error_message"):
                return {
                    "success": True,
                    "content": f"视频理解完成，识别了{len(result.get('video_script_json', {}).get('videoScriptDTO', {}).get('shots', []))}个镜头",
                    "assets": {
                        "video_json": result.get("video_script_json"),
                        "metadata": result.get("video_metadata"),
                        "json_file": result.get("output_file_path"),
                        "cos_url": result.get("cos_url")
                    },
                    "metadata": {
                        "execution_time": result.get("processing_time", 0),
                        "model_used": "video_understanding_v2",
                        "shots_count": len(result.get('video_script_json', {}).get('videoScriptDTO', {}).get('shots', []))
                    }
                }
            else:
                return {
                    "success": False,
                    "content": "视频理解失败",
                    "error": result.get("error_message", "未知错误"),
                    "assets": {},
                    "metadata": {}
                }
                
        except Exception as e:
            return {
                "success": False,
                "content": f"视频理解专家执行失败: {str(e)}",
                "error": str(e),
                "assets": {},
                "metadata": {}
            }
    
    return StructuredTool.from_function(
        func=run_video_understanding_expert,
        name="video_understanding_expert",
        description="""
        专业的视频理解和分析工具，能够：
        1. 深度分析视频内容结构
        2. 识别镜头、角色、场景、对话
        3. 生成详细的结构化JSON描述
        4. 支持大文件视频处理（无大小限制）
        
        适用场景：视频内容分析、结构化理解、二创前的准备工作
        """
    )
```

#### 2.2 Video Editing Expert

```python
# src/tools/experts/video_editing_expert.py
def get_video_editing_expert_tool() -> StructuredTool:
    """创建视频编辑专家工具"""
    
    def run_video_editing_expert(
        video_json: Dict[str, Any],
        edit_instructions: str,
        edit_type: str = "content_replacement"
    ) -> Dict[str, Any]:
        """
        视频编辑专家工具
        
        Args:
            video_json: 视频结构JSON
            edit_instructions: 编辑指令（自然语言）
            edit_type: 编辑类型
            
        Returns:
            编辑后的视频JSON和操作摘要
        """
        try:
            # 创建视频编辑Agent
            editing_agent = create_video_editing_agent()
            
            # 构建编辑任务
            task_description = f"""
            基于以下视频JSON结构，执行用户的编辑需求：
            
            用户需求：{edit_instructions}
            编辑类型：{edit_type}
            
            请分析视频结构，理解用户意图，并生成编辑后的JSON。
            """
            
            # 调用编辑Agent
            result = editing_agent.invoke({
                "messages": [HumanMessage(content=task_description)],
                "video_json": video_json,
                "edit_instructions": edit_instructions
            })
            
            # 解析编辑结果
            edited_json = extract_edited_json_from_result(result)
            edit_operations = extract_edit_operations_from_result(result)
            
            return {
                "success": True,
                "content": f"完成视频编辑，执行了{len(edit_operations)}项操作",
                "assets": {
                    "edited_json": edited_json,
                    "edit_operations": edit_operations,
                    "original_json": video_json
                },
                "metadata": {
                    "edit_type": edit_type,
                    "operations_count": len(edit_operations),
                    "changed_segments": [op.get("target_segment") for op in edit_operations]
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "content": f"视频编辑专家执行失败: {str(e)}",
                "error": str(e),
                "assets": {},
                "metadata": {}
            }
    
    return StructuredTool.from_function(
        func=run_video_editing_expert,
        name="video_editing_expert",
        description="""
        智能视频编辑工具，能够：
        1. 理解自然语言编辑指令
        2. 基于视频JSON结构进行精确编辑
        3. 支持内容替换、风格转换、时长调整等
        4. 生成详细的编辑操作记录
        
        适用场景：语音替换、背景音乐更换、文字内容修改、风格转换等
        """
    )
```

#### 2.3 Video Rendering Expert

```python
# src/tools/experts/video_rendering_expert.py
def get_video_rendering_expert_tool() -> StructuredTool:
    """创建视频渲染专家工具"""
    
    def run_video_rendering_expert(
        edited_json: Dict[str, Any],
        render_config: Optional[Dict[str, Any]] = None,
        output_format: str = "mp4"
    ) -> Dict[str, Any]:
        """
        视频渲染专家工具
        
        Args:
            edited_json: 编辑后的视频JSON
            render_config: 渲染配置
            output_format: 输出格式
            
        Returns:
            渲染后的视频文件路径和相关信息
        """
        try:
            # 创建视频渲染Agent
            rendering_agent = create_video_rendering_agent()
            
            # 默认渲染配置
            if render_config is None:
                render_config = {
                    "resolution": "1920x1080",
                    "fps": 30,
                    "quality": "high"
                }
            
            # 构建渲染任务
            task_description = f"""
            将编辑后的视频JSON渲染为实际视频文件：
            
            输出格式：{output_format}
            渲染配置：{render_config}
            
            请调用相应的AI服务生成所需的音频、图像等素材，然后合成最终视频。
            """
            
            # 调用渲染Agent
            result = rendering_agent.invoke({
                "messages": [HumanMessage(content=task_description)],
                "edited_json": edited_json,
                "render_config": render_config
            })
            
            # 解析渲染结果
            rendered_video_path = extract_video_path_from_result(result)
            render_summary = extract_render_summary_from_result(result)
            
            return {
                "success": True,
                "content": f"视频渲染完成：{rendered_video_path}",
                "assets": {
                    "video": rendered_video_path,
                    "render_config": render_config,
                    "source_json": edited_json
                },
                "metadata": {
                    "output_format": output_format,
                    "render_time": result.get("render_time", 0),
                    "file_size": get_file_size(rendered_video_path)
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "content": f"视频渲染专家执行失败: {str(e)}",
                "error": str(e),
                "assets": {},
                "metadata": {}
            }
    
    return StructuredTool.from_function(
        func=run_video_rendering_expert,
        name="video_rendering_expert",
        description="""
        专业的视频渲染工具，能够：
        1. 将编辑后的JSON转换为实际视频
        2. 调用各种AI服务生成音频、图像素材
        3. 支持多种输出格式和质量设置
        4. 提供详细的渲染进度和结果信息
        
        适用场景：最终视频生成、格式转换、质量优化等
        """
    )
```

### 第三阶段：Master Agent集成 (1周)

#### 3.1 Master Agent节点

```python
# src/video_creation_graph/nodes.py
from langchain_core.messages import HumanMessage
from langchain.agents import create_react_agent

def master_agent_node(state: VideoCreationState, config):
    """Master Agent节点"""
    
    # 集成所有专家工具
    expert_tools = [
        get_video_understanding_expert_tool(),
        get_video_editing_expert_tool(),
        get_video_rendering_expert_tool(),
        # 可以继续添加其他专家工具
    ]
    
    # 状态管理工具
    state_management_tools = [
        get_current_plan_tool(),
        get_next_pending_step_tool(),
        update_step_status_tool(),
        # ... 其他状态管理工具
    ]
    
    # 规划工具
    planning_tools = [
        planner_tool(),
        reviser_tool()
    ]
    
    # 模板工具
    template_tools = [
        recommend_template_tool(),
        create_plan_from_template_tool(),
        get_available_templates_tool()
    ]
    
    # 所有工具
    all_tools = expert_tools + state_management_tools + planning_tools + template_tools
    
    # 创建Master Agent
    master_agent = create_react_agent(
        name="video_creation_master_agent",
        model=get_llm_model(),
        tools=all_tools,
        prompt=get_master_agent_prompt()
    )
    
    # 执行Agent
    return master_agent.invoke(state, config)

def execution_engine_node(state: VideoCreationState):
    """执行引擎节点"""
    plan = state.get("plan")
    if not plan:
        return state
    
    # 获取下一个待执行步骤
    next_step = get_next_pending_step(plan)
    if not next_step:
        return state
    
    # 更新当前步骤
    state["current_step"] = next_step["step_id"]
    
    # 构建执行指令
    execution_instruction = f"""
    执行计划步骤：{next_step['name']}
    
    步骤描述：{next_step['description']}
    建议工具：{next_step.get('tool_to_use', 'auto')}
    
    请执行这个步骤，并在完成后调用update_step_status工具更新步骤状态。
    """
    
    # 添加执行指令到消息
    state["messages"].append(HumanMessage(content=execution_instruction))
    
    return state
```

## 🚀 迁移策略

### 现有代码复用

1. **保留现有工具系统**
   - 你的工具注册系统可以继续使用
   - 现有的工具可以作为专家Agent的底层实现

2. **工作流封装**
   - `video_understanding.py` → Video Understanding Expert的核心逻辑
   - 现有的各种处理工具 → 各专家Agent的工具集

3. **状态管理扩展**
   - 扩展现有的 `UniversalState` 为 `VideoCreationState`
   - 保持向后兼容性

### 渐进式迁移

**阶段1**: 搭建新架构框架，保持现有功能不变
**阶段2**: 逐步将现有工作流封装为专家工具
**阶段3**: 添加新的编辑和渲染能力
**阶段4**: 优化用户体验和性能

## 📊 预期收益

1. **架构简化**: 从复杂的多节点架构简化为2节点架构
2. **功能增强**: 从单一视频理解扩展为完整的二创平台
3. **用户体验**: 自然语言交互，降低使用门槛
4. **扩展性**: 易于添加新的专家能力和编辑模板
5. **维护性**: 清晰的职责分离，易于调试和维护

---

**这个改造方案充分利用了你现有的优秀基础，通过借鉴蛙蛙框架的设计理念，将系统升级为更强大、更易用的视频二创平台！**
