# 多Agent视频二创架构实现示例

## 🎯 核心实现思路

展示主Agent如何通过JSON操作和子Agent调用，实现完整的视频二创流程。

## 🤖 主Agent实现

```python
# src/agents/master_video_creator.py
from langchain.agents import create_react_agent
from langchain_core.tools import tool
from typing import Dict, Any, List

class MasterVideoCreatorAgent:
    """主视频创作Agent"""
    
    def __init__(self):
        self.llm = get_llm_model()
        self.tools = self._setup_tools()
        self.agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self._get_master_prompt()
        )
        
        # 初始化子Agent
        self.visual_agent = VisualAgent()
        self.audio_agent = AudioAgent()
        self.video_agent = VideoAgent()
        self.text_agent = TextAgent()
    
    def _setup_tools(self):
        """设置主Agent的工具集"""
        return [
            self.video_understanding_tool,
            self.json_editor_tool,
            self.video_generator_tool,
            self.planner_tool,
            self.call_visual_agent,
            self.call_audio_agent,
            self.call_video_agent,
            self.call_text_agent
        ]
    
    @tool
    def video_understanding_tool(self, video_input: str, analysis_request: str = "详细分析视频内容") -> str:
        """
        视频理解工具 - 将视频转换为结构化JSON
        
        Args:
            video_input: 视频路径或URL
            analysis_request: 分析需求描述
            
        Returns:
            JSON格式的视频结构描述
        """
        try:
            # 调用你现有的视频理解工作流
            from src.workflows.video_understanding import run_video_understanding_v2
            
            if video_input.startswith(('http://', 'https://')):
                result = run_video_understanding_v2(
                    video_url=video_input,
                    user_request=analysis_request
                )
            else:
                result = run_video_understanding_v2(
                    video_path=video_input,
                    user_request=analysis_request
                )
            
            if result and not result.get("error_message"):
                # 保存到Agent状态
                self.current_video_json = result.get("video_script_json")
                self.video_metadata = result.get("video_metadata")
                
                shots_count = len(result.get('video_script_json', {}).get('videoScriptDTO', {}).get('shots', []))
                
                return f"""
                视频理解完成！
                - 识别了 {shots_count} 个镜头
                - 生成了完整的JSON结构
                - 包含场景、角色、对话等详细信息
                
                JSON已保存，可以使用json_editor_tool进行编辑。
                """
            else:
                return f"视频理解失败: {result.get('error_message', '未知错误')}"
                
        except Exception as e:
            return f"视频理解工具执行失败: {str(e)}"
    
    @tool
    def json_editor_tool(self, edit_instructions: str) -> str:
        """
        JSON编辑器 - 根据用户指令智能编辑视频JSON
        
        Args:
            edit_instructions: 编辑指令，如"把第一段的背景音乐换成轻松的"
            
        Returns:
            编辑结果描述
        """
        if not hasattr(self, 'current_video_json') or not self.current_video_json:
            return "错误: 请先使用video_understanding_tool分析视频"
        
        try:
            # 使用LLM理解编辑指令并修改JSON
            edited_json = self._edit_json_with_llm(
                self.current_video_json, 
                edit_instructions
            )
            
            # 分析变更
            changes = self._analyze_json_changes(
                self.current_video_json, 
                edited_json
            )
            
            # 更新当前JSON
            self.current_video_json = edited_json
            
            return f"""
            JSON编辑完成！
            
            编辑指令: {edit_instructions}
            
            主要变更:
            {self._format_changes(changes)}
            
            如需生成新素材，请调用相应的专家Agent。
            完成后可使用video_generator_tool生成最终视频。
            """
            
        except Exception as e:
            return f"JSON编辑失败: {str(e)}"
    
    @tool
    def video_generator_tool(self, render_config: str = "default") -> str:
        """
        视频生成器 - 根据当前JSON生成最终视频
        
        Args:
            render_config: 渲染配置，如"high_quality", "fast", "4k"等
            
        Returns:
            生成的视频路径和相关信息
        """
        if not hasattr(self, 'current_video_json') or not self.current_video_json:
            return "错误: 请先准备好要渲染的JSON"
        
        try:
            # 这里是你说的"那一套流程"
            video_path = self._generate_video_from_json(
                self.current_video_json, 
                render_config
            )
            
            return f"""
            🎬 视频生成完成！
            
            输出路径: {video_path}
            渲染配置: {render_config}
            视频时长: {self._get_video_duration(video_path)}秒
            文件大小: {self._get_file_size(video_path)}MB
            
            视频已保存，可以下载或进一步编辑。
            """
            
        except Exception as e:
            return f"视频生成失败: {str(e)}"
    
    @tool
    def call_visual_agent(self, task_description: str, task_params: str = "{}") -> str:
        """
        调用视觉专家Agent
        
        Args:
            task_description: 任务描述，如"生成卡通风格的角色图像"
            task_params: 任务参数JSON字符串
            
        Returns:
            视觉Agent的执行结果
        """
        try:
            import json
            params = json.loads(task_params) if task_params != "{}" else {}
            
            result = self.visual_agent.execute(task_description, params)
            
            if result.get("success"):
                # 如果生成了新素材，可以选择性地更新JSON
                assets = result.get("assets", {})
                if assets:
                    self._update_json_with_assets(assets, "visual")
                
                return f"""
                ✨ 视觉Agent执行完成！
                
                任务: {task_description}
                结果: {result.get('summary', '处理完成')}
                生成素材: {len(assets)}个
                
                素材已准备就绪，可以继续其他操作。
                """
            else:
                return f"视觉Agent执行失败: {result.get('error', '未知错误')}"
                
        except Exception as e:
            return f"调用视觉Agent失败: {str(e)}"
    
    @tool
    def call_audio_agent(self, task_description: str, task_params: str = "{}") -> str:
        """
        调用音频专家Agent
        
        Args:
            task_description: 任务描述，如"生成轻松的背景音乐"
            task_params: 任务参数JSON字符串
            
        Returns:
            音频Agent的执行结果
        """
        try:
            import json
            params = json.loads(task_params) if task_params != "{}" else {}
            
            result = self.audio_agent.execute(task_description, params)
            
            if result.get("success"):
                assets = result.get("assets", {})
                if assets:
                    self._update_json_with_assets(assets, "audio")
                
                return f"""
                🎵 音频Agent执行完成！
                
                任务: {task_description}
                结果: {result.get('summary', '处理完成')}
                生成素材: {len(assets)}个
                
                音频素材已准备就绪。
                """
            else:
                return f"音频Agent执行失败: {result.get('error', '未知错误')}"
                
        except Exception as e:
            return f"调用音频Agent失败: {str(e)}"
    
    def _edit_json_with_llm(self, video_json: Dict, instructions: str) -> Dict:
        """使用LLM智能编辑JSON"""
        edit_prompt = f"""
        你是一个专业的视频JSON编辑器。请根据用户指令修改视频JSON结构。
        
        当前视频JSON:
        {json.dumps(video_json, ensure_ascii=False, indent=2)}
        
        用户编辑指令: {instructions}
        
        请分析指令，修改相应的JSON字段，并返回完整的修改后JSON。
        注意保持JSON结构的完整性和一致性。
        """
        
        # 调用LLM进行JSON编辑
        response = self.llm.invoke(edit_prompt)
        
        # 解析LLM返回的JSON
        try:
            import json
            edited_json = json.loads(response.content)
            return edited_json
        except:
            # 如果解析失败，返回原JSON
            return video_json
    
    def _generate_video_from_json(self, video_json: Dict, config: str) -> str:
        """从JSON生成视频的核心流程"""
        # 这里实现你说的"那一套流程"
        # 1. 解析JSON，识别需要的素材
        # 2. 调用各种AI服务生成素材
        # 3. 使用FFmpeg等工具合成视频
        
        # 示例实现
        output_path = f"output/generated_video_{int(time.time())}.mp4"
        
        # 实际的视频生成逻辑
        self._render_video_from_json(video_json, output_path, config)
        
        return output_path
    
    def execute(self, user_input: str) -> str:
        """执行用户请求"""
        return self.agent.invoke({"input": user_input})
```

## 🎨 专家子Agent实现示例

```python
# src/agents/visual_agent.py
class VisualAgent:
    """视觉创作专家Agent"""
    
    def __init__(self):
        self.llm = get_llm_model()
        self.tools = [
            self.text_to_image,
            self.image_to_image,
            self.style_transfer,
            self.image_edit,
            self.background_removal
        ]
        self.agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self._get_visual_prompt()
        )
    
    @tool
    def text_to_image(self, prompt: str, style: str = "realistic", size: str = "1024x1024") -> str:
        """
        文生图工具
        
        Args:
            prompt: 图像描述
            style: 风格 (realistic, cartoon, anime, oil_painting等)
            size: 尺寸 (1024x1024, 512x512等)
            
        Returns:
            生成的图像URL
        """
        try:
            # 调用图像生成API (FLUX, DALL-E等)
            image_url = self._call_image_generation_api(prompt, style, size)
            return f"图像生成成功: {image_url}"
        except Exception as e:
            return f"图像生成失败: {str(e)}"
    
    @tool
    def style_transfer(self, source_image: str, target_style: str) -> str:
        """
        风格转换工具
        
        Args:
            source_image: 源图像URL或路径
            target_style: 目标风格描述
            
        Returns:
            风格转换后的图像URL
        """
        try:
            styled_image_url = self._call_style_transfer_api(source_image, target_style)
            return f"风格转换成功: {styled_image_url}"
        except Exception as e:
            return f"风格转换失败: {str(e)}"
    
    def execute(self, task_description: str, task_params: Dict = None) -> Dict:
        """执行视觉任务"""
        try:
            # 构建任务上下文
            context = f"任务描述: {task_description}"
            if task_params:
                context += f"\n任务参数: {task_params}"
            
            # 调用Agent执行
            result = self.agent.invoke({"input": context})
            
            # 解析结果，提取生成的素材
            assets = self._extract_assets_from_result(result)
            
            return {
                "success": True,
                "summary": f"视觉任务完成: {task_description}",
                "assets": assets,
                "details": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "assets": {}
            }
```

## 🔄 使用示例

### 完整二创流程

```python
# 用户输入
user_request = "把这个视频改成卡通风格，背景音乐换成轻松愉快的"

# 主Agent处理
master_agent = MasterVideoCreatorAgent()

# Agent会自动执行以下流程:
# 1. video_understanding_tool(video_url) - 理解视频生成JSON
# 2. json_editor_tool("改成卡通风格+换背景音乐") - 编辑JSON
# 3. call_visual_agent("转换为卡通风格") - 生成卡通图像
# 4. call_audio_agent("生成轻松背景音乐") - 生成音乐
# 5. video_generator_tool() - 合成最终视频

result = master_agent.execute(user_request)
print(result)
```

### 细节调整流程

```python
# 用户想要精细调整
user_request = "把第3个镜头的角色表情改得更开心，眼睛要更大一些"

# Agent会执行:
# 1. 分析当前JSON，定位第3镜头
# 2. call_visual_agent("修改角色表情", {"segment": 3, "changes": "更开心+眼睛更大"})
# 3. json_editor_tool("更新第3镜头图像") - 更新JSON
# 4. video_generator_tool("只重新渲染第3镜头") - 局部更新

result = master_agent.execute(user_request)
```

## 🎯 架构优势体现

### 1. 分层清晰
- **主Agent**: 专注流程编排和JSON操作
- **子Agent**: 专精各自领域，工具丰富

### 2. 灵活性强
- **完整流程**: 一句话完成复杂二创
- **细节控制**: 精确调整任何元素

### 3. 易于扩展
- **新增工具**: 在对应专家Agent中添加
- **新增能力**: 通过子Agent扩展

### 4. 复用现有
- **视频理解**: 直接复用你的工作流
- **JSON操作**: 成为核心竞争力

这个架构既保持了整体的简洁性，又提供了强大的细节控制能力。你觉得这样的设计如何？
