"""
视频预处理工具 - 处理大文件的压缩和分段
"""
import os
import tempfile
import subprocess
import math
from typing import Dict, Any, List, Optional, Tuple
from tools.base.tool_interface import BaseTool, ToolCategory

class VideoPreprocessor(BaseTool):
    """视频预处理工具 - 压缩和分段处理大视频文件"""
    
    def __init__(self):
        super().__init__()
        self.name = "VideoPreprocessor"
        self.category = ToolCategory.CONTENT_PROCESSOR
        self.description = "处理超过20MB的视频文件，通过压缩和分段确保符合大小限制"
        self.input_keys = ["video_file_path", "target_size_mb"]
        self.output_keys = ["processed_videos", "processing_info", "total_segments"]
        
        # 配置参数
        self.max_file_size = 20 * 1024 * 1024  # 20MB
        self.target_segment_size = 15 * 1024 * 1024  # 15MB目标，留5MB缓冲
        self.temp_dir = tempfile.gettempdir()
        
        # 压缩参数配置
        self.compression_levels = [
            {"resolution": "1280x720", "crf": 23, "fps": 30},    # 轻度压缩
            {"resolution": "854x480", "crf": 28, "fps": 24},     # 中度压缩
            {"resolution": "640x360", "crf": 32, "fps": 20},     # 重度压缩
        ]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理视频预处理"""
        try:
            video_path = input_data.get("video_file_path")
            target_size_mb = input_data.get("target_size_mb", 20)
            
            if not video_path or not os.path.exists(video_path):
                return {"error_message": "视频文件不存在"}
            
            original_size = os.path.getsize(video_path)
            target_size = target_size_mb * 1024 * 1024
            
            print(f"📹 开始预处理视频: {os.path.basename(video_path)}")
            print(f"📊 原始大小: {original_size / 1024 / 1024:.1f}MB")
            print(f"🎯 目标大小: {target_size_mb}MB")
            
            # 如果文件已经符合大小要求
            if original_size <= target_size:
                return {
                    "processed_videos": [{"path": video_path, "segment": 0}],
                    "processing_info": {
                        "method": "no_processing",
                        "original_size_mb": original_size / 1024 / 1024,
                        "final_size_mb": original_size / 1024 / 1024,
                        "compression_ratio": 1.0
                    },
                    "total_segments": 1
                }
            
            # 尝试压缩
            compressed_result = self._try_compression(video_path, target_size)
            
            if compressed_result["success"]:
                compressed_path = compressed_result["compressed_path"]

                # 上传压缩后的视频到COS获取URL
                cos_url = self._upload_segment_to_cos(compressed_path, 1)

                # 使用COS URL或本地路径
                final_path = cos_url if cos_url else compressed_path

                processed_video = {
                    "path": final_path,
                    "local_path": compressed_path,
                    "segment": 0,
                    "uploaded_to_cos": bool(cos_url)
                }

                if cos_url:
                    processed_video["cos_url"] = cos_url

                return {
                    "processed_videos": [processed_video],
                    "processing_info": compressed_result["info"],
                    "total_segments": 1
                }
            
            # 压缩失败，进行分段处理
            print("🔄 压缩无法达到目标大小，开始分段处理...")
            segment_result = self._segment_and_compress(video_path, target_size)
            
            return {
                "processed_videos": segment_result["segments"],
                "processing_info": segment_result["info"],
                "total_segments": len(segment_result["segments"])
            }
            
        except Exception as e:
            return {"error_message": f"视频预处理失败: {str(e)}"}
    
    def _try_compression(self, video_path: str, target_size: int) -> Dict[str, Any]:
        """尝试压缩视频"""
        
        for i, config in enumerate(self.compression_levels):
            print(f"🔧 尝试压缩级别 {i+1}: {config['resolution']}, CRF={config['crf']}")
            
            compressed_path = self._compress_video(video_path, config)
            
            if compressed_path and os.path.exists(compressed_path):
                compressed_size = os.path.getsize(compressed_path)
                
                print(f"📊 压缩后大小: {compressed_size / 1024 / 1024:.1f}MB")
                
                if compressed_size <= target_size:
                    original_size = os.path.getsize(video_path)
                    return {
                        "success": True,
                        "compressed_path": compressed_path,
                        "info": {
                            "method": "compression",
                            "compression_level": i + 1,
                            "original_size_mb": original_size / 1024 / 1024,
                            "final_size_mb": compressed_size / 1024 / 1024,
                            "compression_ratio": compressed_size / original_size,
                            "settings": config
                        }
                    }
        
        return {"success": False}
    
    def _compress_video(self, video_path: str, config: Dict[str, Any]) -> Optional[str]:
        """压缩单个视频文件"""
        try:
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(
                self.temp_dir, 
                f"{base_name}_compressed_{config['crf']}.mp4"
            )
            
            # 构建ffmpeg命令
            cmd = [
                "ffmpeg", "-y",  # -y 覆盖输出文件
                "-i", video_path,
                "-vf", f"scale={config['resolution']}",  # 调整分辨率
                "-r", str(config['fps']),  # 设置帧率
                "-crf", str(config['crf']),  # 设置质量
                "-preset", "medium",  # 编码速度
                "-c:a", "aac",  # 音频编码
                "-b:a", "128k",  # 音频码率
                output_path
            ]
            
            # 执行压缩
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0 and os.path.exists(output_path):
                return output_path
            else:
                print(f"❌ 压缩失败: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print("❌ 压缩超时")
            return None
        except FileNotFoundError:
            print("❌ 未找到ffmpeg，请安装ffmpeg")
            return None
        except Exception as e:
            print(f"❌ 压缩出错: {str(e)}")
            return None
    
    def _segment_and_compress(self, video_path: str, target_size: int) -> Dict[str, Any]:
        """分段并压缩视频"""
        
        # 获取视频时长
        duration = self._get_video_duration(video_path)
        if not duration:
            raise Exception("无法获取视频时长")
        
        print(f"⏱️ 视频时长: {duration:.1f}秒")
        
        # 估算需要的分段数
        original_size = os.path.getsize(video_path)
        estimated_segments = math.ceil(original_size / self.target_segment_size)
        segment_duration = duration / estimated_segments
        
        print(f"📊 预计分段数: {estimated_segments}")
        print(f"⏱️ 每段时长: {segment_duration:.1f}秒")
        
        segments = []
        processing_info = {
            "method": "segmentation",
            "original_size_mb": original_size / 1024 / 1024,
            "total_segments": estimated_segments,
            "segment_duration": segment_duration,
            "segments_info": []
        }
        
        # 分段处理
        for i in range(estimated_segments):
            start_time = i * segment_duration
            end_time = min((i + 1) * segment_duration, duration)
            
            print(f"🔄 处理分段 {i+1}/{estimated_segments}: {start_time:.1f}s - {end_time:.1f}s")
            
            segment_path = self._extract_segment(video_path, start_time, end_time, i)
            
            if segment_path:
                # 检查分段大小，如果还是太大就压缩
                segment_size = os.path.getsize(segment_path)
                
                if segment_size > target_size:
                    print(f"🔧 分段 {i+1} 仍然过大，进行压缩...")
                    compressed_segment = self._compress_segment(segment_path, target_size)
                    if compressed_segment:
                        segment_path = compressed_segment
                        segment_size = os.path.getsize(segment_path)
                
                # 尝试上传分段到COS获取URL
                cos_url = self._upload_segment_to_cos(segment_path, i + 1)

                segment_info = {
                    "path": cos_url if cos_url else segment_path,  # 优先使用COS URL
                    "local_path": segment_path,  # 保留本地路径用于清理
                    "segment": i + 1,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": end_time - start_time,
                    "size_mb": segment_size / 1024 / 1024
                }

                if cos_url:
                    segment_info["cos_url"] = cos_url
                    segment_info["uploaded_to_cos"] = True
                else:
                    segment_info["uploaded_to_cos"] = False

                segments.append(segment_info)
                
                processing_info["segments_info"].append({
                    "segment": i + 1,
                    "size_mb": segment_size / 1024 / 1024,
                    "duration": end_time - start_time
                })
        
        total_final_size = sum(seg["size_mb"] for seg in segments)
        processing_info["final_size_mb"] = total_final_size
        processing_info["compression_ratio"] = (total_final_size * 1024 * 1024) / original_size
        
        return {
            "segments": segments,
            "info": processing_info
        }

    def _get_video_duration(self, video_path: str) -> Optional[float]:
        """获取视频时长（秒）"""
        try:
            cmd = [
                "ffprobe", "-v", "quiet",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                video_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                return float(result.stdout.strip())
            else:
                return None

        except Exception as e:
            print(f"❌ 获取视频时长失败: {str(e)}")
            return None

    def _extract_segment(self, video_path: str, start_time: float, end_time: float, segment_index: int) -> Optional[str]:
        """提取视频片段"""
        try:
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = os.path.join(
                self.temp_dir,
                f"{base_name}_segment_{segment_index:03d}.mp4"
            )

            duration = end_time - start_time

            cmd = [
                "ffmpeg", "-y",
                "-i", video_path,
                "-ss", str(start_time),  # 开始时间
                "-t", str(duration),     # 持续时间
                "-c", "copy",            # 复制流，不重新编码（快速）
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

            if result.returncode == 0 and os.path.exists(output_path):
                return output_path
            else:
                print(f"❌ 提取分段失败: {result.stderr}")
                return None

        except Exception as e:
            print(f"❌ 提取分段出错: {str(e)}")
            return None

    def _compress_segment(self, segment_path: str, target_size: int) -> Optional[str]:
        """压缩单个分段"""

        # 对分段使用更激进的压缩
        aggressive_config = {
            "resolution": "640x360",
            "crf": 35,
            "fps": 20
        }

        try:
            base_name = os.path.splitext(os.path.basename(segment_path))[0]
            output_path = os.path.join(
                self.temp_dir,
                f"{base_name}_compressed.mp4"
            )

            cmd = [
                "ffmpeg", "-y",
                "-i", segment_path,
                "-vf", f"scale={aggressive_config['resolution']}",
                "-r", str(aggressive_config['fps']),
                "-crf", str(aggressive_config['crf']),
                "-preset", "fast",
                "-c:a", "aac",
                "-b:a", "64k",  # 更低的音频码率
                output_path
            ]

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)

            if result.returncode == 0 and os.path.exists(output_path):
                # 删除原始分段文件
                try:
                    os.remove(segment_path)
                except:
                    pass
                return output_path
            else:
                return segment_path  # 返回原始分段

        except Exception as e:
            print(f"❌ 压缩分段出错: {str(e)}")
            return segment_path

    def cleanup_temp_files(self, file_paths: List[str]):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path) and self.temp_dir in file_path:
                    os.remove(file_path)
            except Exception as e:
                print(f"⚠️ 清理临时文件失败: {file_path}, {str(e)}")

    def check_ffmpeg_availability(self) -> bool:
        """检查ffmpeg是否可用"""
        try:
            subprocess.run(["ffmpeg", "-version"], capture_output=True, timeout=10)
            subprocess.run(["ffprobe", "-version"], capture_output=True, timeout=10)
            return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def _upload_segment_to_cos(self, segment_path: str, segment_number: int) -> Optional[str]:
        """上传分段文件到COS获取URL"""
        try:
            # 检查是否配置了COS
            cos_configured = all([
                os.getenv("TENCENT_SECRET_ID"),
                os.getenv("TENCENT_SECRET_KEY"),
                os.getenv("COS_REGION"),
                os.getenv("COS_BUCKET")
            ])

            if not cos_configured:
                print(f"💡 COS未配置，分段{segment_number}将使用本地路径")
                return None

            # 导入COS上传工具
            try:
                from tools.output.cos_uploader import CosUploaderTool
                print(f"📤 上传分段{segment_number}到COS...")

                cos_uploader = CosUploaderTool()

                # 获取文件扩展名
                file_ext = os.path.splitext(segment_path)[1][1:]  # 去掉点号

                upload_result = cos_uploader.process({
                    "file_path": segment_path,
                    "file_extension": file_ext
                })

                if upload_result.get("error_message"):
                    print(f"⚠️ 分段{segment_number}上传失败: {upload_result['error_message']}")
                    return None

                cos_url = upload_result.get("cos_url")
                print(f"✅ 分段{segment_number}已上传: {cos_url}")

                return cos_url

            except ImportError:
                print(f"⚠️ COS上传工具不可用，分段{segment_number}使用本地路径")
                return None

        except Exception as e:
            print(f"⚠️ 分段{segment_number}上传失败: {str(e)}")
            return None
