"""
视频输入处理工具 - 支持URL和本地文件上传
"""
import os
import mimetypes
import requests
import tempfile
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from tools.base.tool_interface import BaseTool, ToolCategory

class VideoInputTool(BaseTool):
    """视频输入工具 - 处理视频文件输入和验证"""
    
    def __init__(self):
        super().__init__()
        self.name = "VideoInputTool"
        self.category = ToolCategory.INPUT
        self.description = "处理视频文件输入，支持URL和本地文件，多格式支持，限制20MB"
        self.input_keys = ["video_path", "video_url", "user_request", "enable_preprocessing"]
        self.output_keys = ["video_file_path", "video_metadata", "is_local_file", "processed_videos", "processing_info"]
        
        # 支持的视频格式
        self.supported_formats = {
            '.mp4': 'video/mp4',
            '.avi': 'video/x-msvideo', 
            '.mov': 'video/quicktime',
            '.mkv': 'video/x-matroska',
            '.webm': 'video/webm',
            '.flv': 'video/x-flv',
            '.wmv': 'video/x-ms-wmv',
            '.m4v': 'video/x-m4v'
        }
        
        # 文件大小限制 (20MB)
        self.max_file_size = 20 * 1024 * 1024
        
        # 临时文件目录
        self.temp_dir = tempfile.gettempdir()
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理视频输入"""
        try:
            video_path = input_data.get("video_path")
            video_url = input_data.get("video_url")
            enable_preprocessing = input_data.get("enable_preprocessing", True)

            # 基础输入处理
            if video_path:
                result = self._process_local_file(video_path)
            elif video_url:
                result = self._process_url(video_url)
            else:
                return {
                    "error_message": "请提供视频文件路径(video_path)或URL(video_url)"
                }

            if result.get("error_message"):
                return result

            # 检查是否需要预处理
            if enable_preprocessing:
                preprocessing_result = self._handle_preprocessing(result)
                if preprocessing_result:
                    result.update(preprocessing_result)

            return result

        except Exception as e:
            return {
                "error_message": f"视频输入处理失败: {str(e)}"
            }
    
    def _process_local_file(self, video_path: str) -> Dict[str, Any]:
        """处理本地视频文件"""
        if not os.path.exists(video_path):
            return {"error_message": f"视频文件不存在: {video_path}"}
        
        # 检查文件格式
        file_ext = os.path.splitext(video_path)[1].lower()
        if file_ext not in self.supported_formats:
            return {
                "error_message": f"不支持的视频格式: {file_ext}. 支持的格式: {list(self.supported_formats.keys())}"
            }
        
        # 检查文件大小
        file_size = os.path.getsize(video_path)
        if file_size > self.max_file_size:
            return {
                "error_message": f"视频文件过大: {file_size / 1024 / 1024:.1f}MB, 最大支持: {self.max_file_size / 1024 / 1024}MB"
            }
        
        # 获取视频元数据
        metadata = self._get_video_metadata(video_path, file_size)
        
        return {
            "video_file_path": video_path,
            "video_metadata": metadata,
            "is_local_file": True
        }
    
    def _process_url(self, video_url: str) -> Dict[str, Any]:
        """处理视频URL"""
        try:
            # 验证URL格式
            parsed_url = urlparse(video_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                return {"error_message": "无效的视频URL格式"}
            
            # 检查URL是否可访问（HEAD请求）
            try:
                head_response = requests.head(video_url, timeout=10, allow_redirects=True)
                
                # 检查内容类型
                content_type = head_response.headers.get('content-type', '').lower()
                if not any(video_type in content_type for video_type in ['video/', 'application/octet-stream']):
                    # 如果HEAD请求没有返回视频类型，尝试从URL扩展名判断
                    file_ext = os.path.splitext(parsed_url.path)[1].lower()
                    if file_ext not in self.supported_formats:
                        return {
                            "error_message": f"URL不是有效的视频文件。Content-Type: {content_type}, 扩展名: {file_ext}"
                        }
                
                # 检查文件大小（不在这里返回错误，让预处理逻辑处理）
                content_length = head_response.headers.get('content-length')
                if content_length:
                    file_size = int(content_length)
                    if file_size > self.max_file_size:
                        print(f"🔍 检测到大文件: {file_size / 1024 / 1024:.1f}MB > {self.max_file_size / 1024 / 1024}MB，将启用预处理")
                else:
                    file_size = None
                    
            except requests.RequestException as e:
                # 如果HEAD请求失败，仍然允许继续，但记录警告
                print(f"警告: 无法验证URL可访问性: {e}")
                file_size = None
                content_type = "unknown"
            
            # 获取URL元数据
            metadata = self._get_url_metadata(video_url, file_size, content_type)
            
            return {
                "video_file_path": video_url,
                "video_metadata": metadata,
                "is_local_file": False
            }
            
        except Exception as e:
            return {
                "error_message": f"URL处理失败: {str(e)}"
            }
    
    def _get_video_metadata(self, video_path: str, file_size: int) -> Dict[str, Any]:
        """获取本地视频文件元数据"""
        file_ext = os.path.splitext(video_path)[1].lower()
        
        return {
            "source_type": "local_file",
            "file_path": video_path,
            "file_name": os.path.basename(video_path),
            "file_size": file_size,
            "file_size_mb": round(file_size / 1024 / 1024, 2),
            "file_format": file_ext,
            "mime_type": self.supported_formats.get(file_ext, "unknown"),
            "is_accessible": True
        }
    
    def _get_url_metadata(self, video_url: str, file_size: Optional[int], content_type: str) -> Dict[str, Any]:
        """获取URL视频元数据"""
        parsed_url = urlparse(video_url)
        file_ext = os.path.splitext(parsed_url.path)[1].lower()
        
        metadata = {
            "source_type": "url",
            "url": video_url,
            "domain": parsed_url.netloc,
            "file_name": os.path.basename(parsed_url.path) or "video_from_url",
            "file_format": file_ext if file_ext in self.supported_formats else "unknown",
            "content_type": content_type,
            "is_accessible": True
        }
        
        if file_size:
            metadata.update({
                "file_size": file_size,
                "file_size_mb": round(file_size / 1024 / 1024, 2)
            })
        
        return metadata

    def _handle_preprocessing(self, input_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理视频预处理"""
        try:
            video_path = input_result.get("video_file_path")
            video_metadata = input_result.get("video_metadata", {})

            if not video_path:
                return None

            # 对于URL，检查是否需要预处理
            if video_metadata.get("source_type") == "url":
                file_size = video_metadata.get("file_size")

                if file_size and file_size > self.max_file_size:
                    print(f"🌐 URL视频超过{self.max_file_size / 1024 / 1024}MB，需要预处理...")
                    # URL视频需要先下载再预处理
                    return self._handle_url_preprocessing(video_path, video_metadata)
                else:
                    print("🌐 URL视频大小合适，直接处理")
                    return {
                        "processed_videos": [{"path": video_path, "segment": 0}],
                        "processing_info": {"method": "no_processing", "reason": "url_size_ok"}
                    }

            # 检查文件大小
            file_size = video_metadata.get("file_size", 0)
            if file_size <= self.max_file_size:
                return {
                    "processed_videos": [{"path": video_path, "segment": 0}],
                    "processing_info": {"method": "no_processing", "reason": "size_ok"}
                }

            print(f"📹 视频文件超过{self.max_file_size / 1024 / 1024}MB，开始预处理...")

            # 导入并使用预处理工具
            from .video_preprocessor import VideoPreprocessor

            preprocessor = VideoPreprocessor()

            # 检查ffmpeg可用性
            if not preprocessor.check_ffmpeg_availability():
                print("❌ 未找到ffmpeg，无法进行预处理")
                return {
                    "error_message": "视频文件过大且无法进行预处理（需要安装ffmpeg）"
                }

            # 执行预处理
            preprocess_result = preprocessor.process({
                "video_file_path": video_path,
                "target_size_mb": self.max_file_size / 1024 / 1024
            })

            if preprocess_result.get("error_message"):
                return {"error_message": preprocess_result["error_message"]}

            return {
                "processed_videos": preprocess_result.get("processed_videos", []),
                "processing_info": preprocess_result.get("processing_info", {}),
                "total_segments": preprocess_result.get("total_segments", 1)
            }

        except Exception as e:
            print(f"❌ 预处理失败: {str(e)}")
            return {"error_message": f"视频预处理失败: {str(e)}"}

        return None

    def _handle_url_preprocessing(self, video_url: str, video_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """处理URL视频的预处理"""
        try:
            print("📥 开始下载URL视频进行预处理...")

            # 导入预处理工具
            from .video_preprocessor import VideoPreprocessor

            preprocessor = VideoPreprocessor()

            # 检查ffmpeg可用性
            if not preprocessor.check_ffmpeg_availability():
                print("❌ 未找到ffmpeg，无法进行URL视频预处理")
                # 如果无法预处理，仍然返回原URL，让用户知道风险
                return {
                    "processed_videos": [{"path": video_url, "segment": 0}],
                    "processing_info": {
                        "method": "no_processing",
                        "reason": "ffmpeg_unavailable",
                        "warning": "视频可能超过大小限制"
                    }
                }

            # 下载视频到临时文件
            download_result = self._download_url_video(video_url, video_metadata)

            if download_result.get("error_message"):
                return {"error_message": download_result["error_message"]}

            temp_video_path = download_result["temp_file_path"]

            print(f"✅ 视频下载完成: {download_result['downloaded_size'] / 1024 / 1024:.1f}MB")

            # 可选：将下载的视频上传到COS进行缓存
            cos_cache_result = self._try_cos_cache(temp_video_path, video_url)

            # 对下载的文件进行预处理
            preprocess_result = preprocessor.process({
                "video_file_path": temp_video_path,
                "target_size_mb": self.max_file_size / 1024 / 1024
            })

            if preprocess_result.get("error_message"):
                # 清理临时文件
                try:
                    os.remove(temp_video_path)
                except:
                    pass
                return {"error_message": preprocess_result["error_message"]}

            # 更新处理信息，标记为URL来源
            processing_info = preprocess_result.get("processing_info", {})
            processing_info["source_type"] = "url"
            processing_info["original_url"] = video_url
            processing_info["temp_download"] = temp_video_path

            # 添加COS缓存信息
            if cos_cache_result.get("cos_url"):
                processing_info["cos_cache_url"] = cos_cache_result["cos_url"]
                processing_info["cos_metadata"] = cos_cache_result.get("upload_metadata")

            return {
                "processed_videos": preprocess_result.get("processed_videos", []),
                "processing_info": processing_info,
                "total_segments": preprocess_result.get("total_segments", 1)
            }

        except Exception as e:
            print(f"❌ URL视频预处理失败: {str(e)}")
            return {"error_message": f"URL视频预处理失败: {str(e)}"}

    def _download_url_video(self, video_url: str, video_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """下载URL视频到临时文件"""
        try:
            print("📥 正在下载视频...")

            # 使用requests下载
            response = requests.get(video_url, stream=True, timeout=60)
            response.raise_for_status()

            # 获取文件扩展名
            parsed_url = urlparse(video_url)
            file_ext = os.path.splitext(parsed_url.path)[1].lower()
            if not file_ext or file_ext not in self.supported_formats:
                file_ext = '.mp4'  # 默认扩展名

            # 创建临时文件
            temp_file = tempfile.NamedTemporaryFile(
                delete=False,
                suffix=file_ext,
                dir=self.temp_dir
            )

            # 下载文件
            downloaded_size = 0
            chunk_size = 8192

            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    temp_file.write(chunk)
                    downloaded_size += len(chunk)

                    # 显示下载进度
                    if downloaded_size % (1024 * 1024) == 0:  # 每MB显示一次
                        print(f"📥 已下载: {downloaded_size / 1024 / 1024:.1f}MB")

                    # 检查大小限制（下载时允许更大，因为后续会压缩）
                    max_download_size = 200 * 1024 * 1024  # 200MB下载限制
                    if downloaded_size > max_download_size:
                        temp_file.close()
                        os.unlink(temp_file.name)
                        return {
                            "error_message": f"下载的视频文件过大，超过{max_download_size / 1024 / 1024}MB限制"
                        }

            temp_file.close()

            print(f"✅ 下载完成: {downloaded_size / 1024 / 1024:.1f}MB")

            return {
                "temp_file_path": temp_file.name,
                "downloaded_size": downloaded_size,
                "success": True
            }

        except requests.exceptions.RequestException as e:
            return {
                "error_message": f"下载视频失败: {str(e)}",
                "success": False
            }
        except Exception as e:
            return {
                "error_message": f"下载视频出错: {str(e)}",
                "success": False
            }

    def _try_cos_cache(self, video_path: str, original_url: str) -> Dict[str, Any]:
        """尝试将视频上传到COS进行缓存"""
        try:
            # 检查是否配置了COS
            cos_configured = all([
                os.getenv("TENCENT_SECRET_ID"),
                os.getenv("TENCENT_SECRET_KEY"),
                os.getenv("COS_REGION"),
                os.getenv("COS_BUCKET")
            ])

            if not cos_configured:
                print("💡 COS未配置，跳过视频缓存")
                return {}

            # 导入COS上传工具
            try:
                from tools.output.cos_uploader import CosUploaderTool
                print("📤 开始上传视频到COS进行缓存...")

                cos_uploader = CosUploaderTool()

                # 获取视频文件扩展名
                file_ext = os.path.splitext(video_path)[1][1:]  # 去掉点号

                upload_result = cos_uploader.process({
                    "file_path": video_path,
                    "file_extension": file_ext
                })

                if upload_result.get("error_message"):
                    print(f"⚠️ COS上传失败: {upload_result['error_message']}")
                    return {}

                cos_url = upload_result.get("cos_url")
                upload_metadata = upload_result.get("upload_metadata", {})

                print(f"✅ 视频已缓存到COS: {cos_url}")

                return {
                    "cos_url": cos_url,
                    "upload_metadata": upload_metadata,
                    "original_url": original_url,
                    "cache_time": self._get_current_timestamp()
                }

            except ImportError:
                print("⚠️ COS上传工具不可用，跳过缓存")
                return {}

        except Exception as e:
            print(f"⚠️ COS缓存失败: {str(e)}")
            return {}

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
