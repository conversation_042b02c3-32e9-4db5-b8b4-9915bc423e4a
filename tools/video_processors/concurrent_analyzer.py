"""
并发视频分析工具 - 提高分段处理效率
"""
import asyncio
import concurrent.futures
from typing import Dict, Any, List
from tools.base.tool_interface import BaseTool, ToolCategory
from tools.video_processors.video_script_analyzer import VideoScriptAnalyzer

class ConcurrentVideoAnalyzer(BaseTool):
    """并发视频分析工具 - 同时处理多个分段"""
    
    def __init__(self):
        super().__init__()
        self.name = "ConcurrentVideoAnalyzer"
        self.category = ToolCategory.ANALYZER
        self.description = "并发处理多个视频分段，提高分析效率"
        self.input_keys = ["processed_videos", "user_request", "max_workers"]
        self.output_keys = ["segment_results", "processing_summary"]
        
        # 默认并发数
        self.default_max_workers = 3  # 避免API限流
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """并发处理视频分段"""
        try:
            processed_videos = input_data.get("processed_videos", [])
            user_request = input_data.get("user_request", "")
            max_workers = input_data.get("max_workers", self.default_max_workers)
            
            if not processed_videos:
                return {"error_message": "没有提供视频分段"}
            
            total_segments = len(processed_videos)
            
            if total_segments == 1:
                # 单个分段，直接处理
                return self._process_single_segment(processed_videos[0], user_request)
            
            print(f"🚀 开始并发分析 {total_segments} 个分段（并发数: {max_workers}）...")
            
            # 并发处理分段
            segment_results = self._process_segments_concurrently(
                processed_videos, user_request, max_workers
            )
            
            # 统计处理结果
            successful_results = [r for r in segment_results if not r.get("error_message")]
            failed_results = [r for r in segment_results if r.get("error_message")]
            
            processing_summary = {
                "total_segments": total_segments,
                "successful_segments": len(successful_results),
                "failed_segments": len(failed_results),
                "success_rate": len(successful_results) / total_segments,
                "concurrent_workers": max_workers
            }
            
            print(f"✅ 并发分析完成: {len(successful_results)}/{total_segments} 成功")
            
            if failed_results:
                print(f"⚠️ {len(failed_results)} 个分段分析失败")
                for i, failed in enumerate(failed_results):
                    print(f"  分段{i+1}: {failed.get('error_message', 'unknown error')}")
            
            return {
                "segment_results": successful_results,
                "processing_summary": processing_summary,
                "failed_segments": failed_results
            }
            
        except Exception as e:
            return {"error_message": f"并发分析失败: {str(e)}"}
    
    def _process_single_segment(self, video_info: Dict[str, Any], user_request: str) -> Dict[str, Any]:
        """处理单个分段"""
        print("🤖 处理单个视频...")
        
        analyzer = VideoScriptAnalyzer()
        
        result = analyzer.process({
            "video_url": video_info.get("path"),
            "analysis_requirements": user_request
        })
        
        if result.get("error_message"):
            return {"error_message": result["error_message"]}
        
        return {
            "segment_results": [result],
            "processing_summary": {
                "total_segments": 1,
                "successful_segments": 1,
                "failed_segments": 0,
                "success_rate": 1.0,
                "concurrent_workers": 1
            }
        }
    
    def _process_segments_concurrently(self, processed_videos: List[Dict[str, Any]], 
                                     user_request: str, max_workers: int) -> List[Dict[str, Any]]:
        """并发处理多个分段"""
        
        # 使用线程池并发处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_segment = {}
            
            for i, video_info in enumerate(processed_videos):
                future = executor.submit(
                    self._analyze_single_segment,
                    video_info, user_request, i + 1, len(processed_videos)
                )
                future_to_segment[future] = i
            
            # 收集结果
            results = [None] * len(processed_videos)
            
            for future in concurrent.futures.as_completed(future_to_segment):
                segment_index = future_to_segment[future]
                try:
                    result = future.result()
                    results[segment_index] = result
                    
                    if result.get("error_message"):
                        print(f"❌ 分段{segment_index + 1}分析失败: {result['error_message']}")
                    else:
                        print(f"✅ 分段{segment_index + 1}分析完成")
                        
                except Exception as e:
                    print(f"❌ 分段{segment_index + 1}处理异常: {str(e)}")
                    results[segment_index] = {"error_message": str(e)}
        
        return results
    
    def _analyze_single_segment(self, video_info: Dict[str, Any], user_request: str, 
                              segment_num: int, total_segments: int) -> Dict[str, Any]:
        """分析单个分段"""
        try:
            video_path = video_info.get("path")
            
            print(f"🔄 开始分析分段{segment_num}/{total_segments}...")
            
            analyzer = VideoScriptAnalyzer()
            
            # 准备分析输入
            analysis_input = {
                "video_url": video_path,
                "analysis_requirements": user_request
            }
            
            # 如果是分段，添加分段信息
            if total_segments > 1:
                segment_info = f"这是视频的第{segment_num}段（共{total_segments}段）。"
                if "start_time" in video_info:
                    segment_info += f"时间范围：{video_info['start_time']:.1f}s - {video_info['end_time']:.1f}s。"
                analysis_input["analysis_requirements"] = segment_info + analysis_input["analysis_requirements"]
            
            # 执行分析
            result = analyzer.process(analysis_input)
            
            if result.get("error_message"):
                return {"error_message": result["error_message"]}
            
            # 添加分段信息到结果
            result["segment_info"] = {
                "segment_number": segment_num,
                "total_segments": total_segments,
                "video_info": video_info
            }
            
            return result
            
        except Exception as e:
            return {"error_message": f"分段{segment_num}分析失败: {str(e)}"}
    
    def get_optimal_workers(self, total_segments: int) -> int:
        """获取最优并发数"""
        if total_segments <= 2:
            return 1
        elif total_segments <= 4:
            return 2
        elif total_segments <= 8:
            return 3
        else:
            return 4  # 最大并发数，避免API限流
