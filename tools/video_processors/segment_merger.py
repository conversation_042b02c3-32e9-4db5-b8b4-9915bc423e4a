"""
分段结果合并工具 - 合并多个视频分段的分析结果
"""
from typing import Dict, Any, List
from tools.base.tool_interface import BaseTool, ToolCategory

class SegmentMerger(BaseTool):
    """分段结果合并工具 - 将多个分段的分析结果合并为完整的视频脚本"""
    
    def __init__(self):
        super().__init__()
        self.name = "SegmentMerger"
        self.category = ToolCategory.CONTENT_PROCESSOR
        self.description = "合并多个视频分段的分析结果为完整的视频脚本"
        self.input_keys = ["segment_results", "processing_info"]
        self.output_keys = ["merged_script", "merge_summary"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理分段结果合并"""
        try:
            segment_results = input_data.get("segment_results", [])
            processing_info = input_data.get("processing_info", {})
            
            if not segment_results:
                return {"error_message": "没有提供分段结果"}
            
            if len(segment_results) == 1:
                # 只有一个分段，直接返回
                return {
                    "merged_script": segment_results[0].get("video_script_json", {}),
                    "merge_summary": {
                        "total_segments": 1,
                        "merge_method": "single_segment",
                        "total_shots": len(segment_results[0].get("video_script_json", {}).get("shots", [])),
                        "processing_info": processing_info
                    }
                }
            
            print(f"🔄 开始合并 {len(segment_results)} 个分段的分析结果...")
            
            # 合并结果
            merged_result = self._merge_segments(segment_results, processing_info)
            
            return {
                "merged_script": merged_result["script"],
                "merge_summary": merged_result["summary"]
            }
            
        except Exception as e:
            return {"error_message": f"分段结果合并失败: {str(e)}"}
    
    def _merge_segments(self, segment_results: List[Dict[str, Any]], processing_info: Dict[str, Any]) -> Dict[str, Any]:
        """合并多个分段的结果"""
        
        # 初始化合并后的脚本结构
        merged_script = {
            "videoMetaDataDTO": {},
            "shots": []
        }
        
        # 收集所有分段的信息
        all_metadata = []
        all_shots = []
        total_duration = 0
        
        for i, segment_result in enumerate(segment_results):
            script = segment_result.get("video_script_json", {})
            segment_info = processing_info.get("segments_info", [])
            
            # 收集元数据
            metadata = script.get("videoMetaDataDTO", {})
            if metadata:
                all_metadata.append(metadata)
            
            # 收集镜头，调整时间偏移
            shots = script.get("shots", [])
            if shots:
                # 计算时间偏移
                time_offset = 0
                if i < len(segment_info):
                    # 使用前面所有分段的时长作为偏移
                    time_offset = sum(
                        seg.get("duration", 0) 
                        for seg in segment_info[:i]
                    )
                
                # 调整镜头时间和编号
                adjusted_shots = self._adjust_shots_timing(shots, time_offset, len(all_shots))
                all_shots.extend(adjusted_shots)
        
        # 合并元数据
        merged_script["videoMetaDataDTO"] = self._merge_metadata(all_metadata, processing_info)
        
        # 设置合并后的镜头
        merged_script["shots"] = all_shots
        
        # 生成合并摘要
        summary = {
            "total_segments": len(segment_results),
            "merge_method": "time_offset_adjustment",
            "total_shots": len(all_shots),
            "total_duration": sum(shot.get("endTime", 0) for shot in all_shots) if all_shots else 0,
            "processing_info": processing_info,
            "segments_summary": [
                {
                    "segment": i + 1,
                    "shots_count": len(result.get("video_script_json", {}).get("shots", [])),
                    "duration": processing_info.get("segments_info", [{}])[i].get("duration", 0) if i < len(processing_info.get("segments_info", [])) else 0
                }
                for i, result in enumerate(segment_results)
            ]
        }
        
        return {
            "script": merged_script,
            "summary": summary
        }
    
    def _adjust_shots_timing(self, shots: List[Dict[str, Any]], time_offset: float, shot_number_offset: int) -> List[Dict[str, Any]]:
        """调整镜头的时间和编号"""
        adjusted_shots = []
        
        for shot in shots:
            adjusted_shot = shot.copy()
            
            # 调整镜头编号
            original_shot_number = shot.get("shotNumber", 0)
            adjusted_shot["shotNumber"] = shot_number_offset + original_shot_number
            
            # 调整时间
            original_start = shot.get("startTime", 0)
            original_end = shot.get("endTime", 0)
            
            adjusted_shot["startTime"] = original_start + time_offset
            adjusted_shot["endTime"] = original_end + time_offset
            adjusted_shot["duration"] = original_end - original_start
            
            adjusted_shots.append(adjusted_shot)
        
        return adjusted_shots
    
    def _merge_metadata(self, all_metadata: List[Dict[str, Any]], processing_info: Dict[str, Any]) -> Dict[str, Any]:
        """合并视频元数据"""
        if not all_metadata:
            return self._create_default_metadata(processing_info)
        
        # 使用第一个分段的元数据作为基础
        base_metadata = all_metadata[0].copy()
        
        # 合并标题
        titles = [meta.get("videoTitle", "") for meta in all_metadata if meta.get("videoTitle")]
        if titles:
            # 如果标题相似，使用第一个；否则创建组合标题
            unique_titles = list(set(titles))
            if len(unique_titles) == 1:
                base_metadata["videoTitle"] = unique_titles[0]
            else:
                base_metadata["videoTitle"] = f"{unique_titles[0]} (完整版)"
        
        # 合并主题
        themes = [meta.get("videoTheme", "") for meta in all_metadata if meta.get("videoTheme")]
        if themes:
            unique_themes = list(set(themes))
            base_metadata["videoTheme"] = ", ".join(unique_themes[:3])  # 最多3个主题
        
        # 合并角色表
        all_characters = []
        seen_characters = set()
        
        for metadata in all_metadata:
            characters = metadata.get("characterSheet", [])
            for char in characters:
                char_name = char.get("name", "")
                if char_name and char_name not in seen_characters:
                    all_characters.append(char)
                    seen_characters.add(char_name)
        
        base_metadata["characterSheet"] = all_characters
        
        # 更新others字段，添加分段信息
        others_parts = []
        
        # 收集原有的others信息
        original_others = [meta.get("others", "") for meta in all_metadata if meta.get("others")]
        if original_others:
            others_parts.extend(original_others)
        
        # 添加分段处理信息
        processing_method = processing_info.get("method", "unknown")
        if processing_method == "segmentation":
            total_segments = processing_info.get("total_segments", 0)
            others_parts.append(f"此视频经过分段处理，共{total_segments}个片段分析后合并")
        elif processing_method == "compression":
            compression_ratio = processing_info.get("compression_ratio", 1.0)
            others_parts.append(f"此视频经过压缩处理，压缩比{compression_ratio:.2f}")
        
        base_metadata["others"] = "; ".join(others_parts)
        
        # 更新best3sec，选择第一个分段的
        if not base_metadata.get("best3sec") and all_metadata:
            for metadata in all_metadata:
                if metadata.get("best3sec"):
                    base_metadata["best3sec"] = metadata["best3sec"]
                    break
        
        return base_metadata
    
    def _create_default_metadata(self, processing_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建默认的元数据"""
        return {
            "videoTitle": "分段处理视频",
            "videoCover": "多分段视频合并",
            "videoTheme": "分段分析",
            "narrativeStructure": "分段式结构",
            "best3sec": "0:00-0:03",
            "visualStyle": "多样化风格",
            "bgmStyle": "混合音乐风格",
            "characterSheet": [],
            "others": f"此视频经过{processing_info.get('total_segments', 0)}个分段处理后合并"
        }
    
    def validate_merged_result(self, merged_script: Dict[str, Any]) -> Dict[str, Any]:
        """验证合并后的结果"""
        issues = []
        
        # 检查镜头编号连续性
        shots = merged_script.get("shots", [])
        if shots:
            shot_numbers = [shot.get("shotNumber", 0) for shot in shots]
            expected_numbers = list(range(1, len(shots) + 1))
            
            if shot_numbers != expected_numbers:
                issues.append("镜头编号不连续")
        
        # 检查时间轴连续性
        for i in range(len(shots) - 1):
            current_end = shots[i].get("endTime", 0)
            next_start = shots[i + 1].get("startTime", 0)
            
            # 允许小的时间间隙（1秒以内）
            if abs(next_start - current_end) > 1:
                issues.append(f"镜头{i+1}和{i+2}之间时间不连续")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "total_shots": len(shots),
            "total_duration": max((shot.get("endTime", 0) for shot in shots), default=0)
        }
