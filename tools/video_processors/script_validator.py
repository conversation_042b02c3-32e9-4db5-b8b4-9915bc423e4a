"""
视频脚本格式验证工具 - 验证和标准化JSON输出
"""
import json
import logging
from typing import Dict, Any, List, Optional, Union
from tools.base.tool_interface import BaseTool, ToolCategory

logger = logging.getLogger(__name__)

class ScriptValidatorTool(BaseTool):
    """脚本格式验证工具 - 验证和修复视频脚本JSON格式"""
    
    def __init__(self):
        super().__init__()
        self.name = "ScriptValidatorTool"
        self.category = ToolCategory.CONTENT_PROCESSOR
        self.description = "验证和标准化视频脚本JSON格式，确保符合接口规范"
        self.input_keys = ["video_script_json", "raw_analysis"]
        self.output_keys = ["validated_script", "validation_report"]
        
        # 有效的镜头类型
        self.valid_shot_types = {
            "EXTREME_LONG_SHOT", "LONG_SHOT", "MEDIUM_SHOT", 
            "CLOSE_UP", "EXTREME_CLOSE_UP"
        }
        
        # 有效的摄像机运动
        self.valid_camera_movements = {
            "STATIC", "PAN", "TILT", "ZOOM_IN",
            "ZOOM_OUT", "DOLLY", "TRACKING"
        }

        # 有效的ability类型
        self.valid_ability_types = {
            "TEXT_TO_IMAGE",      # 文生图
            "IMAGE_TO_IMAGE",     # 图生图
            "TEXT_TO_VIDEO",      # 文生视频
            "IMAGE_TO_VIDEO",     # 图生视频
            "DIGITAL_HUMAN",      # 数字人
            "MOTION_CAPTURE",     # 动作捕捉
            "FACE_SWAP",          # 换脸
            "VOICE_CLONE",        # 声音克隆
            "STYLE_TRANSFER"      # 风格迁移
        }
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理脚本验证"""
        try:
            script_json = input_data.get("video_script_json")
            raw_analysis = input_data.get("raw_analysis", "")
            
            if not script_json:
                return {"error_message": "没有提供视频脚本JSON数据"}
            
            # 验证和修复脚本
            validation_result = self._validate_and_fix_script(script_json)
            
            # 生成验证报告
            report = self._generate_validation_report(validation_result)
            
            return {
                "validated_script": validation_result["script"],
                "validation_report": report,
                "is_valid": validation_result["is_valid"],
                "fixes_applied": validation_result["fixes_applied"]
            }
            
        except Exception as e:
            logger.error(f"脚本验证失败: {e}", exc_info=True)
            return {"error_message": f"脚本验证失败: {str(e)}"}
    
    def _validate_and_fix_script(self, script_json: Dict[str, Any]) -> Dict[str, Any]:
        """验证和修复脚本JSON"""
        fixes_applied = []
        is_valid = True
        
        # 确保顶级结构存在
        if "videoMetaDataDTO" not in script_json:
            script_json["videoMetaDataDTO"] = {}
            fixes_applied.append("添加缺失的videoMetaDataDTO字段")
            is_valid = False
        
        if "shots" not in script_json:
            script_json["shots"] = []
            fixes_applied.append("添加缺失的shots字段")
            is_valid = False
        
        # 验证和修复元数据
        metadata_fixes = self._fix_metadata(script_json["videoMetaDataDTO"])
        fixes_applied.extend(metadata_fixes)
        if metadata_fixes:
            is_valid = False
        
        # 验证和修复镜头数据
        shots_fixes = self._fix_shots(script_json["shots"])
        fixes_applied.extend(shots_fixes)
        if shots_fixes:
            is_valid = False
        
        return {
            "script": script_json,
            "is_valid": is_valid,
            "fixes_applied": fixes_applied
        }
    
    def _fix_metadata(self, metadata: Dict[str, Any]) -> List[str]:
        """修复元数据字段"""
        fixes = []
        
        # 必需的字符串字段
        required_string_fields = [
            "videoTitle", "videoCover", "videoTheme", 
            "narrativeStructure", "best3sec", "visualStyle", 
            "bgmStyle", "others"
        ]
        
        for field in required_string_fields:
            if field not in metadata or not isinstance(metadata[field], str):
                metadata[field] = ""
                fixes.append(f"修复元数据字段: {field}")
        
        # 角色表字段
        if "characterSheet" not in metadata or not isinstance(metadata["characterSheet"], list):
            metadata["characterSheet"] = []
            fixes.append("修复characterSheet字段")
        
        # 验证角色表中的每个角色
        for i, character in enumerate(metadata["characterSheet"]):
            if not isinstance(character, dict):
                metadata["characterSheet"][i] = {
                    "name": "",
                    "description": "",
                    "promptCn": "",
                    "promptEn": ""
                }
                fixes.append(f"修复角色{i+1}的格式")
            else:
                char_fields = ["name", "description", "promptCn", "promptEn"]
                for field in char_fields:
                    if field not in character or not isinstance(character[field], str):
                        character[field] = ""
                        fixes.append(f"修复角色{i+1}的{field}字段")
        
        return fixes
    
    def _fix_shots(self, shots: List[Dict[str, Any]]) -> List[str]:
        """修复镜头数据"""
        fixes = []
        
        for i, shot in enumerate(shots):
            if not isinstance(shot, dict):
                shots[i] = self._create_default_shot(i + 1)
                fixes.append(f"重建镜头{i+1}的数据结构")
                continue
            
            # 修复数字字段
            numeric_fields = ["shotNumber", "startTime", "endTime", "duration"]
            for field in numeric_fields:
                if field not in shot or not isinstance(shot[field], (int, float)):
                    if field == "shotNumber":
                        shot[field] = i + 1
                    else:
                        shot[field] = 0
                    fixes.append(f"修复镜头{i+1}的{field}字段")
            
            # 修复字符串字段
            string_fields = ["scene", "description", "bgmDescription", "bgmSource"]
            for field in string_fields:
                if field not in shot or not isinstance(shot[field], str):
                    shot[field] = ""
                    fixes.append(f"修复镜头{i+1}的{field}字段")
            
            # 修复数组字段
            array_fields = ["characters", "dialogue", "onScreenText", "sfxDescription"]
            for field in array_fields:
                if field not in shot or not isinstance(shot[field], list):
                    shot[field] = []
                    fixes.append(f"修复镜头{i+1}的{field}字段")
                else:
                    # 确保数组中都是字符串
                    shot[field] = [str(item) for item in shot[field]]

            # 修复prompts字段
            if "prompts" not in shot or not isinstance(shot["prompts"], dict):
                shot["prompts"] = self._create_default_prompts()
                fixes.append(f"修复镜头{i+1}的prompts字段")
            else:
                prompts_fixes = self._fix_prompts(shot["prompts"], i+1)
                fixes.extend(prompts_fixes)
            
            # 修复枚举字段
            if "shotType" not in shot or shot["shotType"] not in self.valid_shot_types:
                shot["shotType"] = "MEDIUM_SHOT"  # 默认值
                fixes.append(f"修复镜头{i+1}的shotType字段")
            
            if "cameraMovement" not in shot or shot["cameraMovement"] not in self.valid_camera_movements:
                shot["cameraMovement"] = "STATIC"  # 默认值
                fixes.append(f"修复镜头{i+1}的cameraMovement字段")
            
            # 确保时间逻辑正确
            if shot["endTime"] <= shot["startTime"]:
                shot["endTime"] = shot["startTime"] + 1
                fixes.append(f"修复镜头{i+1}的时间逻辑")
            
            shot["duration"] = shot["endTime"] - shot["startTime"]
        
        return fixes
    
    def _create_default_shot(self, shot_number: int) -> Dict[str, Any]:
        """创建默认镜头数据"""
        return {
            "shotNumber": shot_number,
            "startTime": 0,
            "endTime": 1,
            "duration": 1,
            "scene": "",
            "characters": [],
            "shotType": "MEDIUM_SHOT",
            "cameraMovement": "STATIC",
            "description": "",
            "bgmDescription": "",
            "bgmSource": "",
            "dialogue": [],
            "onScreenText": [],
            "sfxDescription": [],
            "prompts": self._create_default_prompts()
        }
    
    def _generate_validation_report(self, validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成验证报告"""
        script = validation_result["script"]
        
        return {
            "is_valid": validation_result["is_valid"],
            "total_fixes": len(validation_result["fixes_applied"]),
            "fixes_applied": validation_result["fixes_applied"],
            "script_stats": {
                "total_shots": len(script.get("shots", [])),
                "has_metadata": bool(script.get("videoMetaDataDTO")),
                "character_count": len(script.get("videoMetaDataDTO", {}).get("characterSheet", [])),
                "total_duration": self._calculate_total_duration(script.get("shots", []))
            },
            "validation_timestamp": self._get_timestamp()
        }
    
    def _calculate_total_duration(self, shots: List[Dict[str, Any]]) -> float:
        """计算视频总时长"""
        if not shots:
            return 0.0
        
        try:
            max_end_time = max(shot.get("endTime", 0) for shot in shots)
            return float(max_end_time)
        except (ValueError, TypeError):
            return 0.0
    
    def _create_default_prompts(self) -> Dict[str, Any]:
        """创建默认的prompts结构"""
        return {
            "ability": [
                {
                    "abilityType": "TEXT_TO_IMAGE",
                    "parameters": {
                        "style": "cinematic",
                        "quality": "high",
                        "aspect_ratio": "16:9"
                    }
                }
            ],
            "cn": "",
            "en": ""
        }

    def _fix_prompts(self, prompts: Dict[str, Any], shot_number: int) -> List[str]:
        """修复prompts字段"""
        fixes = []

        # 修复ability字段
        if "ability" not in prompts or not isinstance(prompts["ability"], list):
            prompts["ability"] = [
                {
                    "abilityType": "TEXT_TO_IMAGE",
                    "parameters": {
                        "style": "cinematic",
                        "quality": "high",
                        "aspect_ratio": "16:9"
                    }
                }
            ]
            fixes.append(f"修复镜头{shot_number}的prompts.ability字段")
        else:
            # 验证ability数组中的每个元素
            for i, ability in enumerate(prompts["ability"]):
                if not isinstance(ability, dict):
                    prompts["ability"][i] = {
                        "abilityType": "TEXT_TO_IMAGE",
                        "parameters": {}
                    }
                    fixes.append(f"修复镜头{shot_number}的ability[{i}]结构")
                else:
                    if "abilityType" not in ability:
                        ability["abilityType"] = "TEXT_TO_IMAGE"
                        fixes.append(f"修复镜头{shot_number}的ability[{i}].abilityType字段")
                    elif ability["abilityType"] not in self.valid_ability_types:
                        # 如果是无效的ability类型，设为默认值
                        ability["abilityType"] = "TEXT_TO_IMAGE"
                        fixes.append(f"修复镜头{shot_number}的ability[{i}].abilityType为有效值")

                    if "parameters" not in ability or not isinstance(ability["parameters"], dict):
                        ability["parameters"] = {
                            "style": "cinematic",
                            "quality": "high",
                            "aspect_ratio": "16:9"
                        }
                        fixes.append(f"修复镜头{shot_number}的ability[{i}].parameters字段")

        # 修复cn和en字段
        for field in ["cn", "en"]:
            if field not in prompts or not isinstance(prompts[field], str):
                prompts[field] = ""
                fixes.append(f"修复镜头{shot_number}的prompts.{field}字段")

        return fixes

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
