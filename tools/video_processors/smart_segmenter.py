"""
智能分段工具 - 减少分段数量，降低API调用成本
"""
import os
import math
from typing import Dict, Any, List
from tools.base.tool_interface import BaseTool, ToolCategory

class SmartSegmenter(BaseTool):
    """智能分段工具 - 优化分段策略，减少API调用"""
    
    def __init__(self):
        super().__init__()
        self.name = "SmartSegmenter"
        self.category = ToolCategory.CONTENT_PROCESSOR
        self.description = "智能分段策略，平衡文件大小和API调用成本"
        self.input_keys = ["video_path", "target_size_mb", "max_segments"]
        self.output_keys = ["segmentation_plan", "estimated_cost"]
        
        # 配置参数
        self.max_segment_size = 18 * 1024 * 1024  # 18MB，留2MB缓冲
        self.preferred_segment_count = 4  # 首选分段数
        self.max_segment_count = 6  # 最大分段数
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成智能分段计划"""
        try:
            video_path = input_data.get("video_path")
            target_size_mb = input_data.get("target_size_mb", 20)
            max_segments = input_data.get("max_segments", self.max_segment_count)
            
            if not video_path or not os.path.exists(video_path):
                return {"error_message": "视频文件不存在"}
            
            file_size = os.path.getsize(video_path)
            target_size = target_size_mb * 1024 * 1024
            
            print(f"📊 智能分段分析:")
            print(f"  📹 文件大小: {file_size / 1024 / 1024:.1f}MB")
            print(f"  🎯 目标大小: {target_size_mb}MB")
            
            # 如果文件已经符合要求
            if file_size <= target_size:
                return {
                    "segmentation_plan": {
                        "strategy": "no_segmentation",
                        "segments": 1,
                        "reason": "文件大小已符合要求"
                    },
                    "estimated_cost": {"api_calls": 1, "cost_level": "low"}
                }
            
            # 计算分段策略
            plan = self._calculate_segmentation_plan(file_size, target_size, max_segments)
            
            print(f"📋 分段计划:")
            print(f"  ✂️ 分段数: {plan['segments']}")
            print(f"  📊 策略: {plan['strategy']}")
            print(f"  💰 预估成本: {plan['estimated_cost']['cost_level']}")
            
            return {
                "segmentation_plan": plan,
                "estimated_cost": plan["estimated_cost"]
            }
            
        except Exception as e:
            return {"error_message": f"智能分段分析失败: {str(e)}"}
    
    def _calculate_segmentation_plan(self, file_size: int, target_size: int, max_segments: int) -> Dict[str, Any]:
        """计算分段计划"""
        
        # 基础分段数（按大小计算）
        basic_segments = math.ceil(file_size / self.max_segment_size)
        
        # 智能优化分段数
        if basic_segments <= 2:
            # 小文件，尽量不分段或少分段
            optimal_segments = min(basic_segments, 2)
            strategy = "minimal_segmentation"
        elif basic_segments <= 4:
            # 中等文件，适度分段
            optimal_segments = min(basic_segments, self.preferred_segment_count)
            strategy = "balanced_segmentation"
        else:
            # 大文件，控制分段数
            optimal_segments = min(basic_segments, max_segments)
            strategy = "controlled_segmentation"
        
        # 计算每段大小
        segment_size = file_size / optimal_segments
        segment_size_mb = segment_size / 1024 / 1024
        
        # 评估成本
        cost_level = self._evaluate_cost(optimal_segments)
        
        return {
            "strategy": strategy,
            "segments": optimal_segments,
            "segment_size_mb": round(segment_size_mb, 1),
            "basic_segments": basic_segments,
            "optimization": f"从{basic_segments}段优化为{optimal_segments}段",
            "estimated_cost": {
                "api_calls": optimal_segments,
                "cost_level": cost_level,
                "savings": max(0, basic_segments - optimal_segments)
            }
        }
    
    def _evaluate_cost(self, segments: int) -> str:
        """评估API调用成本"""
        if segments == 1:
            return "low"
        elif segments <= 3:
            return "medium"
        elif segments <= 6:
            return "high"
        else:
            return "very_high"
    
    def suggest_alternatives(self, file_size_mb: float) -> List[Dict[str, Any]]:
        """建议替代方案"""
        alternatives = []
        
        if file_size_mb > 100:
            alternatives.append({
                "option": "aggressive_compression",
                "description": "激进压缩，降低到480p或更低",
                "pros": "大幅减少分段数",
                "cons": "画质损失较大"
            })
        
        if file_size_mb > 50:
            alternatives.append({
                "option": "key_frame_extraction",
                "description": "提取关键帧，生成精简版视频",
                "pros": "保持关键信息，大幅减小文件",
                "cons": "丢失动态信息"
            })
        
        alternatives.append({
            "option": "time_based_sampling",
            "description": "按时间间隔采样，如每30秒取5秒",
            "pros": "保持时间分布，减小文件",
            "cons": "内容不连续"
        })
        
        return alternatives
