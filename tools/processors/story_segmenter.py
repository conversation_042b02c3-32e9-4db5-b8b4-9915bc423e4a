"""
故事分段处理工具 - 将长文本故事分成多个段落
"""
import re
import logging
from typing import Dict, Any, List
from llm_services import get_llm
from tools.base import BaseTool, ToolCategory, ToolRegistry

logger = logging.getLogger(__name__)

@ToolRegistry.register(ToolCategory.CONTENT_PROCESSOR, "story_segmenter")
class StorySegmenterTool(BaseTool):
    """故事分段处理工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将长文本故事智能分段，便于后续图像生成和展示"
        self.input_keys = ["story_text", "max_segments", "segment_style"]
        self.output_keys = ["segments", "segment_count", "processing_metadata"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理故事分段"""
        
        story_text = input_data.get("story_text", "")
        max_segments = input_data.get("max_segments", 8)
        segment_style = input_data.get("segment_style", "narrative")  # narrative, scene, timeline
        
        if not story_text.strip():
            return {"error_message": "故事文本不能为空"}
        
        logger.info(f"开始故事分段处理，目标段数: {max_segments}，分段风格: {segment_style}")
        
        # 使用LLM智能分段
        segments = self._intelligent_segmentation(story_text, max_segments, segment_style)
        
        if not segments:
            # 回退到简单分段
            logger.warning("LLM分段失败，使用简单分段策略")
            segments = self._simple_segmentation(story_text, max_segments)
        
        # 为每个段落生成描述性标题
        segment_titles = self._generate_segment_titles(segments)
        
        # 构建分段结果
        processed_segments = []
        for i, (segment, title) in enumerate(zip(segments, segment_titles)):
            processed_segments.append({
                "index": i + 1,
                "title": title,
                "content": segment.strip(),
                "word_count": len(segment.strip()),
                "key_elements": self._extract_key_elements(segment)
            })
        
        return {
            "segments": processed_segments,
            "segment_count": len(processed_segments),
            "processing_metadata": {
                "original_length": len(story_text),
                "segmentation_method": "llm" if segments else "simple",
                "segment_style": segment_style,
                "average_segment_length": sum(len(s["content"]) for s in processed_segments) // len(processed_segments)
            }
        }
    
    def _intelligent_segmentation(self, text: str, max_segments: int, style: str) -> List[str]:
        """使用LLM进行智能分段"""
        try:
            # 构建分段提示词
            segmentation_prompt = f"""
请将以下故事文本分成{max_segments}个左右的段落，要求：

1. **分段原则**：
   - 每个段落应该包含一个相对完整的情节或场景
   - 段落之间保持逻辑连贯性
   - 适合后续为每个段落生成配图

2. **分段风格**：{self._get_style_description(style)}

3. **输出格式**：
   请用"---SEGMENT---"作为段落分隔符，直接返回分段后的文本，不要添加其他说明。

原文本：
{text}

分段结果：
"""
            
            logger.info("开始LLM智能分段...")
            llm = get_llm(request_timeout=30)  # 设置30秒超时
            response = llm.invoke(segmentation_prompt)
            result_text = response.content.strip()
            
            logger.info(f"LLM响应长度: {len(result_text)} 字符")
            
            # 解析分段结果
            segments = [s.strip() for s in result_text.split("---SEGMENT---") if s.strip()]
            logger.info(f"解析出 {len(segments)} 个段落")
            
            # 跳过验证直接返回分段结果
            logger.info(f"LLM智能分段成功，共{len(segments)}段")
            return segments
                
        except Exception as e:
            logger.error(f"LLM智能分段失败: {e}")
            # 如果是网络连接错误，提供更明确的错误信息
            if "Connection" in str(e) or "timeout" in str(e).lower():
                logger.error("网络连接问题，请检查网络配置和API服务状态")
            return []
    
    def _get_style_description(self, style: str) -> str:
        """获取分段风格描述"""
        style_descriptions = {
            "narrative": "按照故事情节发展顺序分段，每段包含一个主要事件或情节转折",
            "scene": "按照场景变化分段，每段代表一个具体的时间地点场景",
            "timeline": "按照时间线分段，每段代表一个时间阶段或时期",
            "character": "按照人物视角分段，每段聚焦特定人物的经历"
        }
        return style_descriptions.get(style, style_descriptions["narrative"])
    
    def _simple_segmentation(self, text: str, max_segments: int) -> List[str]:
        """简单分段策略（回退方案）"""
        # 先按自然段分割
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        if len(paragraphs) <= max_segments:
            return paragraphs
        
        # 如果段落太多，则合并相邻段落
        segments = []
        current_segment = ""
        target_length = len(text) // max_segments
        
        for paragraph in paragraphs:
            if len(current_segment) < target_length and current_segment:
                current_segment += "\n" + paragraph
            else:
                if current_segment:
                    segments.append(current_segment)
                current_segment = paragraph
        
        if current_segment:
            segments.append(current_segment)
        
        return segments[:max_segments]
    
    def _validate_segments(self, segments: List[str], original_text: str) -> bool:
        """验证分段质量"""
        if not segments:
            return False
        
        # 检查内容完整性
        total_length = sum(len(s) for s in segments)
        original_length = len(original_text)
        content_ratio = total_length / original_length
        
        if content_ratio < 0.7:  # 内容丢失太多（从0.8调整为0.7）
            logger.warning(f"内容保留比例过低: {content_ratio:.2f}")
            return False
        
        # 检查段落长度合理性 - 根据原文长度动态调整
        avg_length = total_length / len(segments)
        min_length = max(20, original_length // (len(segments) * 3))  # 动态最小长度
        
        if avg_length < min_length:
            logger.warning(f"平均段落长度过短: {avg_length:.1f} < {min_length}")
            return False
        
        # 检查段落数量合理性
        if len(segments) > 10:  # 段落过多
            logger.warning(f"分段过多: {len(segments)} > 10")
            return False
        
        logger.info(f"分段质量验证通过: {len(segments)}段, 平均长度{avg_length:.1f}, 内容保留{content_ratio:.2f}")
        return True
    
    def _generate_segment_titles(self, segments: List[str]) -> List[str]:
        """为每个段落生成描述性标题"""
        titles = []
        for i, segment in enumerate(segments):
            try:
                # 提取段落关键信息生成标题
                title = self._extract_segment_title(segment, i + 1)
                titles.append(title)
            except Exception:
                # 回退到默认标题
                titles.append(f"第{i + 1}段")
        
        return titles
    
    def _extract_segment_title(self, segment: str, index: int) -> str:
        """提取段落标题"""
        # 简单的关键词提取生成标题
        first_sentence = segment.split('。')[0] if '。' in segment else segment[:50]
        
        # 提取一些关键词
        keywords = []
        if "空姐" in first_sentence:
            keywords.append("空姐")
        if "结婚" in first_sentence or "婚" in first_sentence:
            keywords.append("婚姻")
        if "工作" in first_sentence or "上班" in first_sentence:
            keywords.append("工作")
        if "孩子" in first_sentence or "儿子" in first_sentence:
            keywords.append("家庭")
        
        if keywords:
            return f"第{index}段: {' & '.join(keywords[:2])}"
        else:
            return f"第{index}段"
    
    def _extract_key_elements(self, segment: str) -> List[str]:
        """提取段落关键元素"""
        elements = []
        
        # 人物
        characters = ["空姐", "妈", "爸", "儿子", "男的", "爷爷"]
        for char in characters:
            if char in segment:
                elements.append(f"人物:{char}")
        
        # 地点
        locations = ["小区", "法院", "家", "幼儿园", "医院"]
        for loc in locations:
            if loc in segment:
                elements.append(f"地点:{loc}")
        
        # 情感
        emotions = ["生气", "反对", "高兴", "担心", "害怕"]
        for emo in emotions:
            if emo in segment:
                elements.append(f"情感:{emo}")
        
        return elements[:5]  # 最多返回5个关键元素

def test_story_segmenter():
    """测试故事分段工具"""
    print("🧪 测试故事分段工具")
    
    tool = StorySegmenterTool()
    
    # 测试文本
    test_story = """
我们小区有个"空姐"，三十多岁，长得五大三粗又白又胖。大家都叫她"空姐"因为她年轻时候确实当过空姐，那时候身材苗条、个子高，模样也漂亮。

那时候我们这小地方出了这么个大美人，还是个空姐，说媒的天天往她家跑，门槛子都快踩平了，但是人家"空姐"一个都没相中。结果最后谁也没想到，跟一个工地开挖掘机的好上了。

两个老人坚决反对这俩人在一起，这不自家鲜花插人家牛粪上了吗！但是老人反对根本不好使，没多长时间俩人就租个房子住一块儿了。

结婚之后没几个月就生了个儿子，那男的工作不稳定，没活干的时候在家一呆就是几个月，俩人就天天在家干待着，儿子全靠娘家的爹妈养。
"""
    
    test_input = {
        "story_text": test_story,
        "max_segments": 4,
        "segment_style": "narrative"
    }
    
    result = tool.process(test_input)
    
    if result.get("error_message"):
        print(f"❌ 测试失败: {result['error_message']}")
    else:
        print(f"✅ 测试成功! 分段数量: {result['segment_count']}")
        for segment in result["segments"]:
            print(f"\n📖 {segment['title']}")
            print(f"   内容: {segment['content'][:100]}...")
            print(f"   字数: {segment['word_count']}")
            print(f"   关键元素: {segment['key_elements']}")
    
    print("✅ StorySegmenterTool 测试完成!\n")

if __name__ == "__main__":
    test_story_segmenter()