"""
小红书风格转换工具 - 从现有style_converter.py迁移
"""
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from prompts import XIAOHONGSHU_STYLE_PROMPT

@ToolRegistry.register(ToolCategory.STYLE_CONVERTER, "xiaohongshu")
class XiaohongshuStyleTool(BaseTool):
    """小红书风格转换工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将原始文本转换为小红书风格"
        self.input_keys = ["raw_content"]
        self.output_keys = ["processed_content", "style_metadata"]
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换为小红书风格"""
        
        original_text = input_data.get("raw_content")
        
        if not original_text:
            return {
                "error_message": "缺少原始文本内容"
            }
        
        try:
            llm = get_llm()
            prompt = XIAOHONGSHU_STYLE_PROMPT.format(original_text=original_text)
            
            response = llm.invoke(prompt)
            xiaohongshu_text = response.content
            
            print(f"小红书风格转换完成: {xiaohongshu_text[:50]}...")
            
            return {
                "processed_content": xiaohongshu_text,
                "style_metadata": {
                    "style": "xiaohongshu",
                    "original_length": len(original_text),
                    "processed_length": len(xiaohongshu_text)
                }
            }
            
        except Exception as e:
            return {
                "error_message": f"小红书风格转换失败: {str(e)}"
            }

class MockLLM:
    """模拟LLM用于测试"""
    def invoke(self, prompt):
        class MockResponse:
            def __init__(self):
                self.content = "✨超级棒的AI芯片来啦！🔥\n\n💡 性能提升50%是什么概念？简直太强了！\n\n🚀 功耗还降低30%，续航更持久！\n\n#科技前沿 #AI芯片 #科技创新"
        return MockResponse()

def test_xiaohongshu_style_tool():
    """测试小红书风格转换工具"""
    print("🧪 测试 XiaohongshuStyleTool")
    
    tool = XiaohongshuStyleTool()
    
    # 使用mock LLM避免实际API调用
    original_get_llm = None
    try:
        import llm_services
        original_get_llm = llm_services.get_llm
        llm_services.get_llm = lambda: MockLLM()
        
        # 测试用例1：正常文本转换
        test_input = {"raw_content": "科技创新推动产业升级，新一代AI芯片性能提升50%"}
        result = tool.process(test_input)
        
        print(f"输入: {test_input}")
        print(f"输出: {result}")
        
        assert "processed_content" in result
        assert "style_metadata" in result
        assert result["style_metadata"]["style"] == "xiaohongshu"
        print("✅ 测试1通过")
        
        # 测试用例2：空文本
        test_input2 = {"raw_content": ""}
        result2 = tool.process(test_input2)
        print(f"输入: {test_input2}")
        print(f"输出: {result2}")
        assert "error_message" in result2
        print("✅ 测试2通过")
        
        # 测试用例3：缺少参数
        test_input3 = {}
        result3 = tool.process(test_input3)
        print(f"输入: {test_input3}")
        print(f"输出: {result3}")
        assert "error_message" in result3
        print("✅ 测试3通过")
        
        print("✅ XiaohongshuStyleTool 所有测试通过!\n")
        
    finally:
        # 恢复原始LLM
        if original_get_llm:
            llm_services.get_llm = original_get_llm

if __name__ == "__main__":
    test_xiaohongshu_style_tool()