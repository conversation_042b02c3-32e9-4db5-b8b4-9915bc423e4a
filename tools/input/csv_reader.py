"""
CSV数据源工具 - 从现有data_source.py迁移
"""
import csv
import os
from typing import Dict, Any
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.INPUT, "csv_reader")
class CsvReaderTool(BaseTool):
    """CSV文件读取工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "从CSV文件读取指定ID的内容"
        self.input_keys = ["source_identifier"]  # 需要CSV中的ID
        self.output_keys = ["raw_content", "content_metadata"]
        
        # CSV配置
        self.csv_file_path = "mock_rss_news.csv"
        self.id_column = "id"
        self.content_column = "content"
        self.title_column = "title"
        self._cache = None
    
    def _load_csv_data(self) -> Dict[str, Dict[str, Any]]:
        """加载CSV数据"""
        if self._cache is not None:
            return self._cache
        
        data = {}
        try:
            if not os.path.exists(self.csv_file_path):
                print(f"错误: CSV文件未找到于 '{self.csv_file_path}'")
                self._cache = {}
                return {}
            
            with open(self.csv_file_path, mode='r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile, delimiter=',')
                for row in reader:
                    row_id = row.get(self.id_column)
                    if row_id:
                        data[row_id] = row
            
            self._cache = data
            print(f"CSV数据加载成功，共 {len(data)} 条记录。")
            
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            self._cache = {}
        
        return self._cache
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理CSV读取"""
        
        identifier = input_data.get("source_identifier")
        
        if not identifier:
            return {
                "error_message": "缺少source_identifier参数"
            }
        
        # 加载CSV数据
        csv_data = self._load_csv_data()
        
        if not csv_data:
            return {
                "error_message": f"无法加载CSV数据源: {self.csv_file_path}"
            }
        
        # 查找记录
        record = csv_data.get(str(identifier))
        
        if not record:
            return {
                "error_message": f"在CSV中未找到ID为 '{identifier}' 的记录"
            }
        
        # 提取内容
        content = record.get(self.content_column)
        if not content:
            content = record.get(self.title_column)
        
        if not content:
            return {
                "error_message": f"ID '{identifier}' 的记录中缺少有效内容"
            }
        
        return {
            "raw_content": content,
            "content_metadata": {
                "source": "csv",
                "id": identifier,
                "title": record.get(self.title_column, ""),
                "length": len(content)
            }
        }

def test_csv_reader_tool():
    """测试CSV读取工具"""
    print("🧪 测试 CsvReaderTool")
    
    # 创建测试CSV文件
    import csv
    test_csv_path = "test_mock_data.csv"
    test_data = [
        {"id": "1", "title": "测试标题1", "content": "这是测试内容1"},
        {"id": "2", "title": "测试标题2", "content": "这是测试内容2"}
    ]
    
    with open(test_csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=["id", "title", "content"])
        writer.writeheader()
        writer.writerows(test_data)
    
    print(f"创建测试CSV文件: {test_csv_path}")
    
    try:
        tool = CsvReaderTool()
        tool.csv_file_path = test_csv_path  # 使用测试文件
        
        # 测试用例1：读取存在的ID
        test_input = {"source_identifier": "1"}
        result = tool.process(test_input)
        
        print(f"输入: {test_input}")
        print(f"输出: {result}")
        
        assert "raw_content" in result
        assert result["raw_content"] == "这是测试内容1"
        assert result["content_metadata"]["id"] == "1"
        print("✅ 测试1通过")
        
        # 测试用例2：读取不存在的ID
        test_input2 = {"source_identifier": "999"}
        result2 = tool.process(test_input2)
        print(f"输入: {test_input2}")
        print(f"输出: {result2}")
        assert "error_message" in result2
        print("✅ 测试2通过")
        
        # 测试用例3：缺少参数
        test_input3 = {}
        result3 = tool.process(test_input3)
        print(f"输入: {test_input3}")
        print(f"输出: {result3}")
        assert "error_message" in result3
        print("✅ 测试3通过")
        
        print("✅ CsvReaderTool 所有测试通过!\n")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_csv_path):
            os.remove(test_csv_path)
            print(f"清理测试文件: {test_csv_path}")

if __name__ == "__main__":
    test_csv_reader_tool()