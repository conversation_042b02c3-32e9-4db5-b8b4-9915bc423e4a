"""
工具注册系统
"""
from typing import Dict, List, Type, Optional
from .tool_interface import BaseTool, ToolCategory

class ToolRegistry:
    """工具注册中心"""
    
    _tools: Dict[str, Type[BaseTool]] = {}
    _categories: Dict[ToolCategory, List[str]] = {category: [] for category in ToolCategory}
    
    @classmethod
    def register(cls, category: ToolCategory, name: Optional[str] = None):
        """工具注册装饰器"""
        def decorator(tool_class: Type[BaseTool]):
            tool_name = name or tool_class.__name__
            full_name = f"{category.value}.{tool_name}"
            
            # 注册工具
            cls._tools[full_name] = tool_class
            cls._categories[category].append(full_name)
            
            # 设置工具属性
            tool_class._registry_name = full_name
            tool_class._category = category
            
            return tool_class
        return decorator
    
    @classmethod
    def get_tool(cls, name: str) -> Optional[Type[BaseTool]]:
        """获取指定工具类"""
        return cls._tools.get(name)
    
    @classmethod
    def get_tools_by_category(cls, category: ToolCategory) -> Dict[str, Type[BaseTool]]:
        """获取指定类别的所有工具"""
        category_tools = {}
        for tool_name in cls._categories[category]:
            category_tools[tool_name] = cls._tools[tool_name]
        return category_tools
    
    @classmethod
    def list_all_tools(cls) -> List[str]:
        """列出所有已注册的工具"""
        return list(cls._tools.keys())
    
    @classmethod
    def get_tool_info(cls, name: str) -> Optional[Dict]:
        """获取工具信息"""
        tool_class = cls.get_tool(name)
        if tool_class:
            # 创建临时实例获取信息
            temp_instance = tool_class()
            return temp_instance.get_info()
        return None
    
    @classmethod
    def create_tool(cls, name: str) -> Optional[BaseTool]:
        """创建工具实例"""
        tool_class = cls.get_tool(name)
        if tool_class:
            return tool_class()
        return None
    
    @classmethod
    def reset(cls):
        """重置注册表（主要用于测试）"""
        cls._tools.clear()
        cls._categories = {category: [] for category in ToolCategory}