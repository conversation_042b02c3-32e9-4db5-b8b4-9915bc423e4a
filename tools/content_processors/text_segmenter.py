"""
文本分段工具 - 从现有content_segmenter.py迁移
"""
import re
from typing import Dict, Any, List
from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from prompts import CONTENT_SEGMENTATION_PROMPT_INSTRUCTION
from langchain_core.messages import HumanMessage

@ToolRegistry.register(ToolCategory.CONTENT_PROCESSOR, "segmenter")
class TextSegmenterTool(BaseTool):
    """文本智能分段工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将文本智能分段，用于制作多页图文"
        self.input_keys = ["processed_content"]
        self.output_keys = ["content_segments", "segment_metadata"]
        self.segment_separator = "---片段分隔---"
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """智能分段处理"""
        
        text_content = input_data.get("processed_content")
        
        if not text_content:
            return {
                "error_message": "缺少待分段的文本内容"
            }
        
        try:
            llm = get_llm(temperature=0.3)
            
            # 构建完整提示
            full_prompt = CONTENT_SEGMENTATION_PROMPT_INSTRUCTION + "\n" + text_content
            
            # 调用LLM进行分段
            response = llm.invoke([HumanMessage(content=full_prompt)])
            raw_result = response.content.strip()
            
            # 解析分段结果
            segments = self._parse_segments(raw_result, text_content)
            
            if not segments:
                # 回退方案：使用原文作为单一片段
                segments = [text_content]
                print("LLM分段失败，使用原文作为单一片段")
            
            print(f"文本分段完成，共 {len(segments)} 个片段")
            
            return {
                "content_segments": segments,
                "segment_metadata": {
                    "total_segments": len(segments),
                    "method": "llm_intelligent" if len(segments) > 1 else "fallback",
                    "original_length": len(text_content)
                }
            }
            
        except Exception as e:
            # 错误时的回退方案
            segments = [text_content]
            return {
                "content_segments": segments,
                "segment_metadata": {
                    "total_segments": 1,
                    "method": "error_fallback",
                    "error": str(e)
                }
            }
    
    def _parse_segments(self, raw_result: str, original_text: str) -> List[str]:
        """解析LLM返回的分段结果"""
        
        # 按分隔符分割
        segments = raw_result.split(self.segment_separator)
        
        # 清理每个片段
        cleaned_segments = []
        for seg in segments:
            cleaned_seg = seg.strip()
            
            # 移除LLM可能添加的标记
            cleaned_seg = re.sub(r"^[\*\*]*片段\s*\d+\s*[:：][\*\*]*\s*", "", cleaned_seg).strip()
            
            # 过滤掉明显的LLM引导语句
            if self._is_valid_segment(cleaned_seg):
                cleaned_segments.append(cleaned_seg)
        
        # 验证分段结果
        if not cleaned_segments or self._segments_too_short(cleaned_segments):
            return []  # 返回空，触发回退逻辑
        
        return cleaned_segments
    
    def _is_valid_segment(self, segment: str) -> bool:
        """判断是否是有效的内容片段"""
        
        if len(segment) < 10:  # 太短
            return False
        
        # 过滤掉LLM的引导语句
        invalid_starts = [
            "我来帮你", "这样分成", "以上是", "请您提供",
            "占位符", "未能理解", "我注意到"
        ]
        
        for invalid_start in invalid_starts:
            if segment.startswith(invalid_start):
                return False
        
        return True
    
    def _segments_too_short(self, segments: List[str]) -> bool:
        """判断分段结果是否过短"""
        return len(segments) == 1 and len(segments[0]) < 20

class MockLLMSegmenter:
    """模拟LLM用于分段测试"""
    def invoke(self, messages):
        class MockResponse:
            def __init__(self):
                self.content = """✨重磅！新一代AI芯片发布，性能暴增50%！🔥

💡 科技圈又炸锅啦！最新发布的AI芯片简直不要太强！

---片段分隔---

🌟 性能提升50%是什么概念？
- 手机运行速度飞起
- 游戏体验丝滑流畅  
- 拍照效果更惊艳

---片段分隔---

🔋 功耗还降低了30%！
- 续航更持久
- 发热更少
- 性能更稳定

🚀 应用场景超丰富：智能手机、自动驾驶、智能家居

#科技前沿 #AI芯片 #科技创新"""
        return MockResponse()

def test_text_segmenter_tool():
    """测试文本分段工具"""
    print("🧪 测试 TextSegmenterTool")
    
    tool = TextSegmenterTool()
    
    # 使用mock LLM避免实际API调用
    original_get_llm = None
    try:
        import llm_services
        original_get_llm = llm_services.get_llm
        llm_services.get_llm = lambda temperature=0.3: MockLLMSegmenter()
        
        # 测试用例1：正常文本分段
        test_input = {
            "processed_content": "✨重磅！新一代AI芯片发布，性能暴增50%！🔥 💡 科技圈又炸锅啦！最新发布的AI芯片简直不要太强！功耗还降低了30%！续航更持久、发热更少、性能更稳定。"
        }
        result = tool.process(test_input)
        
        print(f"输入: {test_input}")
        print(f"输出: {result}")
        
        assert "content_segments" in result
        assert "segment_metadata" in result
        assert isinstance(result["content_segments"], list)
        assert len(result["content_segments"]) > 1  # 应该分段成功
        print("✅ 测试1通过")
        
        # 测试用例2：空文本
        test_input2 = {"processed_content": ""}
        result2 = tool.process(test_input2)
        print(f"输入: {test_input2}")
        print(f"输出: {result2}")
        assert "error_message" in result2
        print("✅ 测试2通过")
        
        # 测试用例3：缺少参数
        test_input3 = {}
        result3 = tool.process(test_input3)
        print(f"输入: {test_input3}")
        print(f"输出: {result3}")
        assert "error_message" in result3
        print("✅ 测试3通过")
        
        print("✅ TextSegmenterTool 所有测试通过!\n")
        
    finally:
        # 恢复原始LLM
        if original_get_llm:
            llm_services.get_llm = original_get_llm

if __name__ == "__main__":
    test_text_segmenter_tool()