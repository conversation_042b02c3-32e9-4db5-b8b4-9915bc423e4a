"""
图片转换工具 - 从现有html2image.py迁移
"""
import os
from typing import Dict, Any, List
from tools.base import BaseTool, ToolCategory, ToolRegistry
from playwright.sync_api import sync_playwright

@ToolRegistry.register(ToolCategory.OUTPUT, "html2image")
class ImageConverterTool(BaseTool):
    """HTML转图片工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "将HTML内容转换为图片文件"
        self.input_keys = ["html_contents", "output_dir", "file_prefix", "session_id"]
        self.output_keys = ["image_paths", "final_outputs"]
        
        # 默认输出配置
        self.default_output_dir = "output/images"
        self.width = 600
        self.height = 800
        self.device_scale = 2
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """HTML转图片处理"""
        
        html_contents = input_data.get("html_contents")
        
        if not html_contents:
            return {
                "error_message": "缺少HTML内容"
            }
        
        # 获取配置参数
        output_dir = input_data.get("output_dir", self.default_output_dir)
        file_prefix = input_data.get("file_prefix", "card")
        session_id = input_data.get("session_id", "")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            image_paths = []
            html_paths = []
            
            for i, html_content in enumerate(html_contents):
                print(f"转换第 {i+1} 个HTML为图片...")
                
                # 构建文件名（添加session_id避免覆盖）
                if session_id:
                    base_name = f"{file_prefix}_{session_id}_{i+1}"
                else:
                    base_name = f"{file_prefix}_{i+1}"
                
                # 保存HTML文件
                html_path = os.path.join(output_dir, f"{base_name}.html")
                with open(html_path, "w", encoding="utf-8") as f:
                    f.write(html_content)
                html_paths.append(html_path)
                
                # 转换为图片
                image_path = os.path.join(output_dir, f"{base_name}.png")
                success = self._convert_html_to_image(html_path, image_path)
                
                if success and os.path.exists(image_path):
                    image_paths.append(image_path)
                    print(f"图片生成成功: {image_path}")
                else:
                    print(f"图片生成失败: {image_path}")
            
            if not image_paths:
                return {
                    "error_message": "没有成功生成任何图片"
                }
            
            print(f"图片转换完成，共生成 {len(image_paths)} 张图片")
            
            return {
                "image_paths": image_paths,
                "final_outputs": image_paths,  # 向后兼容
                "conversion_metadata": {
                    "total_images": len(image_paths),
                    "html_files": html_paths,
                    "output_dir": output_dir
                }
            }
            
        except Exception as e:
            return {
                "error_message": f"图片转换失败: {str(e)}"
            }
    
    def _convert_html_to_image(self, html_path: str, image_path: str) -> bool:
        """使用Playwright将HTML转换为图片"""
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch()
                page = browser.new_page(
                    viewport={'width': self.width, 'height': self.height},
                    device_scale_factor=self.device_scale
                )
                
                # 加载HTML文件
                abs_html_path = os.path.abspath(html_path)
                file_url = f'file://{abs_html_path}'
                page.goto(file_url, wait_until='networkidle')
                
                # 尝试智能截图
                success = self._smart_screenshot(page, image_path)
                
                browser.close()
                return success
                
        except Exception as e:
            print(f"Playwright转换失败: {e}")
            return False
    
    def _smart_screenshot(self, page, image_path: str) -> bool:
        """智能截图，尝试多种选择器"""
        
        selectors = ['.card', '.cover-card', '.main-content']
        
        for selector in selectors:
            try:
                element = page.query_selector(selector)
                if element:
                    # 调整元素样式确保完整显示
                    page.evaluate(f"""
                        (sel) => {{
                            const el = document.querySelector(sel);
                            if (el) {{
                                el.style.height = 'auto !important';
                                el.style.overflow = 'visible !important';
                                document.body.style.height = 'auto !important';
                                document.body.style.overflow = 'visible !important';
                            }}
                        }}
                    """, selector)
                    
                    page.wait_for_timeout(300)
                    
                    # 截取元素
                    element.screenshot(path=image_path)
                    print(f"使用选择器 '{selector}' 截图成功")
                    return True
                    
            except Exception as e:
                print(f"选择器 '{selector}' 截图失败: {e}")
                continue
        
        # 回退：截取整个页面
        try:
            page.evaluate("""
                () => {
                    document.body.style.height = 'auto !important';
                    document.body.style.overflow = 'visible !important';
                }
            """)
            page.wait_for_timeout(200)
            page.screenshot(path=image_path, full_page=True)
            print("使用全页截图")
            return True
            
        except Exception as e:
            print(f"全页截图也失败: {e}")
            return False

def test_image_converter_tool():
    """测试图片转换工具"""
    print("🧪 测试 ImageConverterTool")
    
    tool = ImageConverterTool()
    
    # 创建测试HTML内容
    test_html = """<!DOCTYPE html>
<html>
<head><title>测试</title></head>
<body><div class="card">测试内容</div></body>
</html>"""
    
    # 测试用例1：正常HTML转换（使用mock避免实际浏览器操作）
    test_input = {"html_contents": [test_html, test_html]}
    
    # Mock playwright操作
    original_convert = tool._convert_html_to_image
    tool._convert_html_to_image = lambda html_path, image_path: True  # Mock成功
    
    try:
        result = tool.process(test_input)
        
        print(f"输入HTML数量: {len(test_input['html_contents'])}")
        print(f"输出: {result}")
        
        assert "image_paths" in result
        assert "final_outputs" in result
        assert "conversion_metadata" in result
        assert len(result["image_paths"]) == 2  # 应该生成2个图片
        print("✅ 测试1通过")
        
        # 测试用例2：空HTML内容
        test_input2 = {"html_contents": []}
        result2 = tool.process(test_input2)
        print(f"输入: {test_input2}")
        print(f"输出: {result2}")
        assert "error_message" in result2
        print("✅ 测试2通过")
        
        # 测试用例3：缺少参数
        test_input3 = {}
        result3 = tool.process(test_input3)
        print(f"输入: {test_input3}")
        print(f"输出: {result3}")
        assert "error_message" in result3
        print("✅ 测试3通过")
        
        print("✅ ImageConverterTool 所有测试通过!\n")
        
    finally:
        # 恢复原始方法
        tool._convert_html_to_image = original_convert
        
        # 清理测试文件
        import glob
        test_files = glob.glob("output/images/card_*.html") + glob.glob("output/images/card_*.png")
        for file in test_files:
            try:
                os.remove(file)
            except:
                pass

if __name__ == "__main__":
    test_image_converter_tool()