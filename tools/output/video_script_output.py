"""
视频脚本输出工具 - 保存和展示视频脚本结果
"""
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
from tools.base.tool_interface import BaseTool, ToolCategory

class VideoScriptOutputTool(BaseTool):
    """视频脚本输出工具 - 保存脚本到文件并展示结果"""
    
    def __init__(self):
        super().__init__()
        self.name = "VideoScriptOutputTool"
        self.category = ToolCategory.OUTPUT
        self.description = "保存视频脚本JSON到文件并生成结果摘要"
        self.input_keys = ["validated_script", "video_metadata", "validation_report"]
        self.output_keys = ["output_file_path", "script_summary", "success_message"]
        
        # 输出目录
        self.output_dir = "output/video_scripts"
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir, exist_ok=True)
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理脚本输出"""
        try:
            validated_script = input_data.get("validated_script")
            video_metadata = input_data.get("video_metadata", {})
            validation_report = input_data.get("validation_report", {})
            
            if not validated_script:
                return {"error_message": "没有提供有效的脚本数据"}
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            video_name = self._extract_video_name(video_metadata)
            filename = f"video_script_{video_name}_{timestamp}.json"
            file_path = os.path.join(self.output_dir, filename)
            
            # 准备输出数据
            output_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "video_source": video_metadata.get("source_type", "unknown"),
                    "video_file": video_metadata.get("file_name", "unknown"),
                    "validation_report": validation_report
                },
                "script": validated_script
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            # 生成脚本摘要
            summary = self._generate_script_summary(validated_script, validation_report)

            # 尝试上传到COS
            cos_result = self._try_upload_to_cos(file_path)

            # 生成成功消息
            success_msg = self._generate_success_message(file_path, summary, cos_result)

            result = {
                "output_file_path": file_path,
                "script_summary": summary,
                "success_message": success_msg,
                "file_size": os.path.getsize(file_path)
            }

            # 添加COS信息
            if cos_result.get("cos_url"):
                result["cos_url"] = cos_result["cos_url"]
                result["cos_metadata"] = cos_result.get("upload_metadata")

            return result
            
        except Exception as e:
            return {"error_message": f"脚本输出失败: {str(e)}"}
    
    def _extract_video_name(self, video_metadata: Dict[str, Any]) -> str:
        """从视频元数据中提取视频名称"""
        file_name = video_metadata.get("file_name", "unknown_video")
        
        # 移除扩展名
        if "." in file_name:
            file_name = file_name.rsplit(".", 1)[0]
        
        # 清理文件名，只保留字母数字和下划线
        clean_name = "".join(c if c.isalnum() or c == "_" else "_" for c in file_name)
        
        # 限制长度
        return clean_name[:20] if clean_name else "video"
    
    def _generate_script_summary(self, script: Dict[str, Any], validation_report: Dict[str, Any]) -> Dict[str, Any]:
        """生成脚本摘要"""
        metadata = script.get("videoMetaDataDTO", {})
        shots = script.get("shots", [])
        
        # 计算统计信息
        total_shots = len(shots)
        total_duration = max((shot.get("endTime", 0) for shot in shots), default=0)
        character_count = len(metadata.get("characterSheet", []))
        
        # 统计镜头类型
        shot_types = {}
        camera_movements = {}
        for shot in shots:
            shot_type = shot.get("shotType", "UNKNOWN")
            camera_movement = shot.get("cameraMovement", "UNKNOWN")
            shot_types[shot_type] = shot_types.get(shot_type, 0) + 1
            camera_movements[camera_movement] = camera_movements.get(camera_movement, 0) + 1
        
        # 提取对话数量
        total_dialogues = sum(len(shot.get("dialogue", [])) for shot in shots)
        
        return {
            "video_info": {
                "title": metadata.get("videoTitle", "未知标题"),
                "theme": metadata.get("videoTheme", "未知主题"),
                "visual_style": metadata.get("visualStyle", "未知风格"),
                "narrative_structure": metadata.get("narrativeStructure", "未知结构")
            },
            "statistics": {
                "total_shots": total_shots,
                "total_duration_seconds": total_duration,
                "character_count": character_count,
                "total_dialogues": total_dialogues,
                "shot_types_distribution": shot_types,
                "camera_movements_distribution": camera_movements
            },
            "validation_info": {
                "is_valid": validation_report.get("is_valid", False),
                "fixes_applied": validation_report.get("total_fixes", 0),
                "validation_timestamp": validation_report.get("validation_timestamp", "")
            },
            "highlights": {
                "best_moment": metadata.get("best3sec", "未指定"),
                "bgm_style": metadata.get("bgmStyle", "未指定"),
                "key_insights": metadata.get("others", "无额外信息")
            }
        }
    
    def _try_upload_to_cos(self, file_path: str) -> Dict[str, Any]:
        """尝试将脚本文件上传到COS"""
        try:
            # 检查是否配置了COS
            cos_configured = all([
                os.getenv("TENCENT_SECRET_ID"),
                os.getenv("TENCENT_SECRET_KEY"),
                os.getenv("COS_REGION"),
                os.getenv("COS_BUCKET")
            ])

            if not cos_configured:
                return {}

            # 导入COS上传工具
            try:
                from .cos_uploader import CosUploaderTool
                print("📤 上传脚本到COS...")

                cos_uploader = CosUploaderTool()

                upload_result = cos_uploader.process({
                    "file_path": file_path,
                    "file_extension": "json"
                })

                if upload_result.get("error_message"):
                    print(f"⚠️ COS上传失败: {upload_result['error_message']}")
                    return {}

                cos_url = upload_result.get("cos_url")
                upload_metadata = upload_result.get("upload_metadata", {})

                print(f"✅ 脚本已上传到COS: {cos_url}")

                return {
                    "cos_url": cos_url,
                    "upload_metadata": upload_metadata
                }

            except ImportError:
                return {}

        except Exception as e:
            print(f"⚠️ COS上传失败: {str(e)}")
            return {}

    def _generate_success_message(self, file_path: str, summary: Dict[str, Any], cos_result: Dict[str, Any] = None) -> str:
        """生成成功消息"""
        video_info = summary["video_info"]
        stats = summary["statistics"]
        validation = summary["validation_info"]
        
        message_parts = [
            "🎬 视频脚本分析完成！",
            "",
            f"📹 视频标题: {video_info['title']}",
            f"🎭 视频主题: {video_info['theme']}",
            f"🎨 视觉风格: {video_info['visual_style']}",
            "",
            f"📊 分析统计:",
            f"  • 总镜头数: {stats['total_shots']}",
            f"  • 视频时长: {stats['total_duration_seconds']}秒",
            f"  • 角色数量: {stats['character_count']}",
            f"  • 对话数量: {stats['total_dialogues']}",
            "",
            f"✅ 验证状态: {'通过' if validation['is_valid'] else '已修复' + str(validation['fixes_applied']) + '个问题'}",
            "",
            f"💾 文件已保存: {file_path}",
            "",
            "🎯 最精彩片段: " + summary["highlights"]["best_moment"],
            "🎵 音乐建议: " + summary["highlights"]["bgm_style"]
        ]
        
        if summary["highlights"]["key_insights"]:
            message_parts.extend([
                "",
                "💡 关键洞察: " + summary["highlights"]["key_insights"]
            ])

        # 添加COS链接信息
        if cos_result and cos_result.get("cos_url"):
            message_parts.extend([
                "",
                "🔗 在线访问链接: " + cos_result["cos_url"],
                "📤 文件已上传到云存储，可直接分享使用"
            ])

        return "\n".join(message_parts)
    
    def get_recent_scripts(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近生成的脚本列表"""
        try:
            if not os.path.exists(self.output_dir):
                return []
            
            files = []
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.output_dir, filename)
                    stat = os.stat(file_path)
                    files.append({
                        "filename": filename,
                        "path": file_path,
                        "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "size": stat.st_size
                    })
            
            # 按创建时间排序，返回最新的
            files.sort(key=lambda x: x["created_at"], reverse=True)
            return files[:limit]
            
        except Exception as e:
            return []
    
    def load_script(self, file_path: str) -> Optional[Dict[str, Any]]:
        """加载已保存的脚本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            return None
