"""
AI图像生成工具 - 基于Jmeng API
"""
import os
import json
import requests
import logging
from typing import Dict, Any, Optional

from tools.base import BaseTool, ToolCategory, ToolRegistry
from llm_services import get_llm
from core.prompt_optimizer import get_prompt_optimizer

logger = logging.getLogger(__name__)

@ToolRegistry.register(ToolCategory.GENERATOR, "ai_image")
class AiImageGeneratorTool(BaseTool):
    """AI图像生成工具"""
    
    def __init__(self):
        super().__init__()
        self.description = "基于文本描述生成AI图像，使用LLM优化提示词，支持多种专业风格"
        self.input_keys = ["prompt", "style", "width", "height", "enhance_prompt"]
        self.output_keys = ["image_url", "cos_url", "generation_metadata"]
        self.prompt_optimizer = get_prompt_optimizer()
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成AI图像"""
        
        # 获取输入参数
        prompt = input_data.get("prompt", "")
        style = input_data.get("style", "modern")
        width = input_data.get("width", 810)
        height = input_data.get("height", 1080)
        seed = input_data.get("seed", -1)
        scale = input_data.get("scale", 2.5)
        enhance_prompt = input_data.get("enhance_prompt", True)
        
        if not prompt:
            return {"error_message": "生成提示词不能为空"}
        
        # 使用专业LLM优化提示词
        if enhance_prompt:
            # 准备额外要求
            additional_requirements = {}
            if input_data.get("meme_keywords"):
                additional_requirements["keywords"] = input_data["meme_keywords"]
            if input_data.get("mood"):
                additional_requirements["mood"] = input_data["mood"]
            if input_data.get("avoid_elements"):
                additional_requirements["avoid"] = input_data["avoid_elements"]
            
            enhanced_prompt = self.prompt_optimizer.optimize_prompt(
                content=prompt,
                style_name=style,
                additional_requirements=additional_requirements
            )
            logger.info(f"LLM提示词优化完成: {enhanced_prompt[:100]}...")
        else:
            enhanced_prompt = prompt
            logger.info("跳过LLM提示词优化，使用原始提示词")
        
        # 调用Jmeng API生成图像
        image_result = self._generate_image_with_jmeng(
            enhanced_prompt, width, height, seed, scale
        )
        
        if not image_result or image_result.get("error"):
            return {"error_message": f"图像生成失败: {image_result.get('error', '未知错误')}"}
        
        image_url = image_result["image_url"]
        
        # 上传到COS
        cos_result = self._upload_to_cos(image_url, enhanced_prompt)
        
        return {
            "image_url": image_url,
            "cos_url": cos_result.get("cos_url", image_url),
            "generation_metadata": {
                "original_prompt": prompt,
                "enhanced_prompt": enhanced_prompt,
                "style": style,
                "dimensions": {"width": width, "height": height},
                "seed": seed,
                "scale": scale,
                "api_used": "jmeng"
            }
        }
    
    
    def _generate_image_with_jmeng(self, prompt: str, width: int, height: int, 
                                   seed: int, scale: float) -> Optional[Dict[str, Any]]:
        """使用Jmeng API生成图像"""
        try:
            # 获取API配置
            api_key = os.getenv("JMENG_PROXY_API_KEY")
            if not api_key:
                return {"error": "未配置JMENG_PROXY_API_KEY"}
            
            api_url = "https://one.glbai.com/volcv/v1"
            query_params = {
                "Action": "CVProcess",
                "Version": "2022-08-31",
            }
            
            payload = {
                "req_key": "high_aes_general_v30l_zt2i",
                "prompt": prompt,
                "width": width,
                "height": height,
                "seed": seed,
                "return_url": True,
                "scale": scale,
                "use_pre_llm": False
            }
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key}",
            }
            
            logger.info(f"正在生成图像: {prompt[:50]}...")
            response = requests.post(api_url, params=query_params, headers=headers, 
                                   json=payload, timeout=180)
            
            if response.status_code != 200:
                error_content = response.text
                logger.error(f"Jmeng API错误: {response.status_code} - {error_content}")
                return {"error": f"API调用失败: {response.status_code}"}
            
            response_data = response.json()
            
            # 解析图像URL
            image_urls = self._extract_image_urls(response_data)
            if image_urls:
                logger.info("图像生成成功")
                return {"image_url": image_urls[0]}
            else:
                error_msg = response_data.get("message", "未找到图像URL")
                return {"error": error_msg}
                
        except Exception as e:
            logger.error(f"图像生成异常: {e}")
            return {"error": str(e)}
    
    def _extract_image_urls(self, response_data: dict) -> list:
        """从API响应中提取图像URL"""
        image_urls = []
        
        if response_data.get("code") == 10000 and isinstance(response_data.get("data"), dict):
            image_urls = response_data["data"].get("image_urls", [])
        elif "image_urls" in response_data and isinstance(response_data["image_urls"], list):
            image_urls = response_data["image_urls"]
        elif "url" in response_data and isinstance(response_data["url"], str):
            image_urls = [response_data["url"]]
        elif "data" in response_data and isinstance(response_data["data"], list):
            urls_from_data_list = [item.get("url") for item in response_data["data"] 
                                 if isinstance(item, dict) and item.get("url")]
            if urls_from_data_list:
                image_urls = urls_from_data_list
        
        # 过滤有效URL
        if image_urls and isinstance(image_urls, list):
            valid_urls = [url for url in image_urls if isinstance(url, str) and url.strip()]
            return valid_urls
        
        return []
    
    def _upload_to_cos(self, image_url: str, prompt: str) -> Dict[str, Any]:
        """上传图像到腾讯云COS"""
        try:
            # 导入COS上传工具
            from tools.output.cos_uploader import CosUploaderTool
            
            # 下载图像
            response = requests.get(image_url, timeout=60)
            response.raise_for_status()
            image_bytes = response.content
            
            # 确定文件扩展名
            content_type = response.headers.get('content-type', '').lower()
            extension_map = {
                'image/jpeg': 'jpg',
                'image/png': 'png',
                'image/gif': 'gif',
                'image/webp': 'webp',
            }
            file_extension = extension_map.get(content_type, 'png')
            
            # 使用COS上传工具
            cos_tool = CosUploaderTool()
            cos_input = {
                "file_content": image_bytes,
                "file_extension": file_extension
            }
            
            cos_result = cos_tool.process(cos_input)
            
            if cos_result.get("error_message"):
                logger.warning(f"COS上传失败: {cos_result['error_message']}")
                return {"cos_url": image_url}  # 返回原始URL
            else:
                logger.info(f"图像已上传到COS: {cos_result['cos_url']}")
                return cos_result
                
        except Exception as e:
            logger.error(f"上传到COS时发生错误: {e}")
            return {"cos_url": image_url}  # 返回原始URL

def test_ai_image_generator_tool():
    """测试AI图像生成工具"""
    print("🧪 测试AI图像生成工具")
    
    # 检查API密钥
    if not os.getenv("JMENG_PROXY_API_KEY"):
        print("⚠️ 跳过测试：未配置JMENG_PROXY_API_KEY")
        return
    
    # 导入必要工具以触发注册
    import tools.output.cos_uploader
    
    tool = AiImageGeneratorTool()
    
    # 测试生成图像
    test_input = {
        "prompt": "一只可爱的小猫坐在窗台上看风景",
        "style": "cute",
        "width": 810,
        "height": 1080,
        "enhance_prompt": True
    }
    
    result = tool.process(test_input)
    
    if result.get("error_message"):
        print(f"❌ 测试失败: {result['error_message']}")
    else:
        print(f"✅ 测试成功!")
        print(f"   原始图像URL: {result['image_url']}")
        print(f"   COS URL: {result['cos_url']}")
        print(f"   生成风格: {result['generation_metadata']['style']}")
        print(f"   增强提示词: {result['generation_metadata']['enhanced_prompt'][:100]}...")
    
    print("✅ AiImageGeneratorTool 测试完成!\n")

if __name__ == "__main__":
    test_ai_image_generator_tool()