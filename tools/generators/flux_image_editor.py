"""
通用图片编辑工具 - 基于Flux API
"""
import os
import time
import requests
from typing import Dict, Any, Optional
from tools.base import BaseTool, ToolCategory, ToolRegistry

@ToolRegistry.register(ToolCategory.GENERATOR, "flux_image_editor")
class FluxImageEditor(BaseTool):
    """基于Flux API的图片编辑工具"""
    
    def __init__(self):
        super().__init__()
        self.category = ToolCategory.GENERATOR
        self.description = "使用Flux API对图片进行智能编辑，支持风格转换、元素添加/删除等操作"
        self.input_keys = ["input_image", "edit_prompt", "edit_config"]
        self.output_keys = ["edited_image_url", "edit_metadata"]
    
    def _get_api_config(self) -> Dict[str, str]:
        """获取API配置"""
        api_key = os.getenv("FLUX_API_KEY")
        base_url = os.getenv("FLUX_BASE_URL")
        
        if not api_key:
            raise ValueError("环境变量中必须设置 FLUX_API_KEY")
        if not base_url:
            raise ValueError("环境变量中必须设置 FLUX_BASE_URL")
            
        return {"api_key": api_key, "base_url": base_url}
    
    def _validate_input_image(self, image_url: str) -> bool:
        """验证输入图片URL"""
        if not image_url.startswith(('http://', 'https://')):
            return False
        return True
    
    def _build_edit_payload(self, input_image: str, edit_prompt: str, edit_config: Dict[str, Any]) -> Dict[str, Any]:
        """构建编辑请求payload"""
        # 默认配置
        default_config = {
            "model": "black-forest-labs/flux-kontext-pro",
            "aspect_ratio": "match_input_image",
            "output_format": "png",
            "safety_tolerance": 2,
            "seed": None
        }
        
        # 合并用户配置
        config = {**default_config, **edit_config}
        
        payload = {
            "input": {
                "prompt": edit_prompt,
                "input_image": input_image,
                "aspect_ratio": config["aspect_ratio"],
                "output_format": config["output_format"],
                "safety_tolerance": config["safety_tolerance"],
            }
        }
        
        # 添加可选参数
        if config["seed"]:
            payload["input"]["seed"] = config["seed"]
            
        return payload, config
    
    def _submit_edit_task(self, api_config: Dict[str, str], payload: Dict[str, Any], model: str) -> str:
        """提交编辑任务"""
        url = f"{api_config['base_url'].rstrip('/')}/replicate/v1/models/{model}/predictions"
        headers = {
            "Authorization": f"Bearer {api_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        result = response.json()
        
        task_id = result.get("id")
        if not task_id:
            raise ValueError(f"API响应中缺少任务ID: {result}")
            
        return task_id
    
    def _poll_for_result(self, api_config: Dict[str, str], task_id: str, max_attempts: int = 60) -> str:
        """轮询获取编辑结果"""
        polling_url = f"{api_config['base_url'].rstrip('/')}/replicate/v1/predictions/{task_id}"
        headers = {
            "Authorization": f"Bearer {api_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        for attempt in range(max_attempts):
            response = requests.get(polling_url, headers=headers)
            response.raise_for_status()
            result = response.json()
            
            status = result.get("status")
            
            if status == "succeeded":
                output = result.get("output")
                if output:
                    if isinstance(output, list) and len(output) > 0:
                        return output[0]
                    elif isinstance(output, str):
                        return output
                raise ValueError(f"任务完成但缺少输出: {result}")
            
            elif status in ["failed", "canceled"]:
                error_message = result.get('error', '未知错误')
                raise ValueError(f"图片编辑任务失败: {error_message}")
            
            time.sleep(2)  # 等待2秒后重试
        
        raise TimeoutError(f"等待任务完成超时 ({max_attempts * 2}秒)")
    
    def _upload_to_storage(self, image_url: str) -> Dict[str, str]:
        """上传图片到云存储"""
        try:
            # 下载图片
            response = requests.get(image_url, timeout=120)
            response.raise_for_status()
            image_bytes = response.content
            
            # 获取文件扩展名
            content_type = response.headers.get('content-type', '').lower()
            extension_map = {
                'image/jpeg': 'jpg', 
                'image/png': 'png', 
                'image/gif': 'gif', 
                'image/webp': 'webp'
            }
            file_extension = extension_map.get(content_type, 'png')
            
            # 使用COS上传工具
            from tools.output.cos_uploader import CosUploaderTool
            uploader = CosUploaderTool()
            
            upload_result = uploader.process({
                "file_content": image_bytes,
                "file_extension": file_extension
            })
            
            if upload_result.get("error_message"):
                # 如果上传失败，返回原始URL
                return {"url": image_url, "storage": "original"}
            
            return {
                "url": upload_result["cos_url"], 
                "storage": "cos",
                "upload_metadata": upload_result.get("upload_metadata", {})
            }
            
        except Exception as e:
            # 上传失败时返回原始URL
            return {"url": image_url, "storage": "original", "upload_error": str(e)}
    
    def process(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """核心处理逻辑"""
        try:
            # 1. 提取输入参数
            input_image = input_data.get("input_image", "")
            edit_prompt = input_data.get("edit_prompt", "")
            edit_config = input_data.get("edit_config", {})
            
            # 2. 验证输入
            if not input_image:
                raise ValueError("缺少输入图片URL")
            if not edit_prompt:
                raise ValueError("缺少编辑指令")
            if not self._validate_input_image(input_image):
                raise ValueError("输入图片必须是有效的HTTP/HTTPS URL")
            
            # 3. 获取API配置
            api_config = self._get_api_config()
            
            # 4. 构建请求payload
            payload, final_config = self._build_edit_payload(input_image, edit_prompt, edit_config)
            
            # 5. 提交编辑任务
            task_id = self._submit_edit_task(api_config, payload, final_config["model"])
            
            # 6. 轮询获取结果
            result_url = self._poll_for_result(api_config, task_id)
            
            # 7. 可选：上传到云存储
            storage_result = self._upload_to_storage(result_url)
            
            # 8. 返回结果
            return {
                "edited_image_url": storage_result["url"],
                "edit_metadata": {
                    "task_id": task_id,
                    "original_image": input_image,
                    "edit_prompt": edit_prompt,
                    "model": final_config["model"],
                    "aspect_ratio": final_config["aspect_ratio"],
                    "output_format": final_config["output_format"],
                    "storage_info": storage_result,
                    "processing_time": "completed"
                }
            }
            
        except requests.exceptions.RequestException as e:
            error_content = e.response.text if hasattr(e, 'response') and e.response else "No response content"
            return {
                "error_message": f"API请求错误: {str(e)}\n响应内容: {error_content}"
            }
        except Exception as e:
            return {
                "error_message": f"图片编辑失败: {str(e)}"
            }


def test_flux_image_editor():
    """测试图片编辑工具"""
    print("🧪 测试 FluxImageEditor")
    
    tool = FluxImageEditor()
    
    # 测试用例1：正常编辑
    test_input = {
        "input_image": "https://example.com/image.jpg",
        "edit_prompt": "add neon lights and cyberpunk style",
        "edit_config": {
            "model": "black-forest-labs/flux-kontext-pro",
            "aspect_ratio": "16:9"
        }
    }
    
    print(f"工具信息: {tool.get_info()}")
    print(f"测试输入: {test_input}")
    
    # 注意：这个测试需要真实的API key才能执行
    # result = tool.process(test_input)
    # print(f"测试输出: {result}")
    
    print("✅ FluxImageEditor 基础结构测试通过!")


if __name__ == "__main__":
    test_flux_image_editor()