# 更新日志 (Changelog)

## [v2.0.0] - 2025-07-25

### 🚀 重大架构升级 - 模块化重构与功能增强

这是一个重大版本更新，完全重构了项目架构，引入了模块化设计和多项新功能。

#### 🏗️ 核心架构改进

- **模块化重构**: 重新组织项目结构，新增 `core/` 和 `tools/` 目录
- **状态管理系统**: 实现基于状态管理的工作流系统 (`core/state.py`)
- **智能参数管理**: 新增参数管理器 (`core/parameter_manager.py`)
- **提示词优化**: 实现智能提示词优化机制 (`core/prompt_optimizer.py`)
- **工具注册系统**: 建立统一的工具注册和管理机制 (`tools/base/`)

#### 🔧 LLM服务优化

- **超时配置**: 增加 `request_timeout` 参数，默认60秒
- **重试机制**: 添加 `max_retries=3` 自动重试功能
- **错误处理**: 改进错误处理和日志输出
- **性能优化**: 优化LLM调用的稳定性和可靠性

#### 📝 提示词系统升级

- **上下文驱动**: 支持基于上下文的动态风格配置
- **视觉设计增强**: 改进CSS样式控制和视觉设计指导
- **字体配置**: 优化字体、色彩和布局配置系统
- **风格适配**: 增强小红书等平台的风格适配能力

#### 🛠️ 新增工具模块

**内容处理器 (`tools/content_processors/`)**
- `text_segmenter.py`: 智能文本分割
- 故事分段处理器

**生成器 (`tools/generators/`)**
- `ai_image_generator.py`: AI图像生成
- `html_generator.py`: HTML内容生成
- `cover_page_generator.py`: 封面页生成
- `content_page_generator.py`: 内容页生成
- `flux_image_editor.py`: Flux图像编辑
- `image_editor.py`: 通用图像编辑

**输入/输出处理 (`tools/input/`, `tools/output/`)**
- `csv_reader.py`: CSV文件读取
- `text_input.py`: 文本输入处理
- `cos_uploader.py`: 腾讯云COS上传
- `image_converter.py`: 图像格式转换
- `video_script_output.py`: 视频脚本输出

**样式转换器 (`tools/style_converters/`)**
- `xiaohongshu_style.py`: 小红书风格适配

**视频处理器 (`tools/video_processors/`)**
- `video_input.py`: 视频输入处理
- `video_script_analyzer.py`: 视频脚本分析
- `script_validator.py`: 脚本验证

#### 🎯 新增功能特性

- **视频理解工作流**: 完整的视频内容理解和处理流程
- **智能内容生成**: 基于AI的内容自动生成
- **多预设支持**: 支持多种内容生成预设
- **批量处理**: 支持批量内容处理和生成

#### 📚 文档和示例

**新增文档 (`docs/`)**
- `API_REFERENCE.md`: API参考文档
- `DEVELOPMENT.md`: 开发指南
- `PARAMETER_GUIDE.md`: 参数配置指南
- `PRESET_DEVELOPMENT_GUIDE.md`: 预设开发指南
- `tool_development_guide.md`: 工具开发指南

**示例代码 (`examples/`)**
- `complete_meme_flow.py`: 完整表情包生成流程
- `flux_image_editor_example.py`: Flux图像编辑示例
- `meme_generation_demo.py`: 表情包生成演示
- `story_cards_demo.py`: 故事卡片演示

#### 🧪 测试覆盖

新增多个测试文件，确保功能稳定性：
- 端到端测试 (`test_end_to_end.py`)
- 完整流程测试 (`test_complete_flow.py`)
- 工具功能测试 (`test_all_tools.py`)
- 视频理解测试 (`test_video_understanding.py`)
- 小红书流程测试 (`test_xiaohongshu_flow.py`)

#### 📊 统计信息

- **新增文件**: 282个
- **代码行数**: +34,679行
- **模块数量**: 8个主要模块
- **工具数量**: 20+个专用工具

#### 🔄 兼容性

- 保持向后兼容性
- 原有API接口继续可用
- 新功能通过新的模块提供

---

## [v1.0.1] - 2025-07-24

### 📖 文档优化
- 更新 README.md，优化文档结构

## [v1.0.0] - 2025-07-24

### 🎉 初始版本
- 添加小红书图文卡片生成器项目
- 基础的HTML/CSS卡片生成功能
- LLM服务集成
- 基础提示词系统
