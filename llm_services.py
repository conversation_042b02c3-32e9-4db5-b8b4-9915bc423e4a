"""
LLM服务配置模块
提供统一的LLM服务接口
"""

import os
from langchain_openai import ChatOpenAI
from langchain.llms.base import LLM
from typing import Optional

def get_llm(model_name: str = "gpt-3.5-turbo", temperature: float = 0.7) -> LLM:
    """
    获取LLM实例
    
    Args:
        model_name: 模型名称
        temperature: 温度参数
        
    Returns:
        LLM实例
    """
    # 从环境变量获取API密钥
    api_key = os.getenv("OPENAI_API_KEY") or os.getenv("VIDEO_UNDERSTAND_KEY")
    
    if not api_key:
        raise ValueError("请设置OPENAI_API_KEY或VIDEO_UNDERSTAND_KEY环境变量")
    
    return ChatOpenAI(
        model=model_name,
        temperature=temperature,
        api_key=api_key
    )

def get_video_llm() -> LLM:
    """获取视频理解专用的LLM"""
    return get_llm(model_name="gpt-4", temperature=0.3)

def get_text_llm() -> LLM:
    """获取文本处理专用的LLM"""
    return get_llm(model_name="gpt-3.5-turbo", temperature=0.7)
