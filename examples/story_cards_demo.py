"""
故事图文卡片生成完整演示 - 展示从长文本到图文卡片的完整流程
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def demo_story_cards_generation():
    """演示故事图文卡片生成"""
    print("📚 故事图文卡片生成完整演示")
    print("="*80)
    
    # 你提供的测试故事（简化版本用于演示）
    test_story = """
我们小区有个"空姐"，三十多岁，长得五大三粗又白又胖，目测175cm、180斤以上，大嗓门子说起话来二里地外都能听真亮儿地，活脱脱一个肉弹战车。大家都叫她"空姐"因为她年轻时候确实当过空姐，那时候身材苗条、个子高，模样也漂亮，天生吃这碗饭的。

那时候我们这小地方出了这么个大美人，还是个空姐，说媒的天天往她家跑，门槛子都快踩平了，但是人家"空姐"一个都没相中。结果最后谁也没想到，跟一个工地开挖掘机的好上了。爹妈都不知道这俩人是咋搭搁上的。

两个老人坚决反对这俩人在一起，这不自家鲜花插人家牛粪上了吗！但是老人反对根本不好使，没多长时间俩人就租个房子住一块儿了，"空姐"为了能天天跟这男的腻在一起，就背着家里人把工作辞了不干了，再后来就怀孕了。

结婚之后没几个月就生了个儿子，那男的工作不稳定，没活干的时候在家一呆就是几个月，俩人就天天在家干待着，儿子全靠娘家的爹妈养。家里人一瞅，这样过日子不行啊。"空姐"娘家这边条件不错，她爷爷辈、叔叔辈和表兄妹不少都是本地司法系统工作人员，他爷爷就托人给她安排了在市里法院上班，书记员，有编制的。

结果干了不到一个月，说法院工作氛围太压抑，不想干了，跑回家了。又把他爷爷气够呛，直接跟她们家断绝来往了。她爸本来身体就不好，连生气带上火，不到一年人没了。后来"空姐"又怀孕了，又生了个儿子，这下娘家妈得自己带俩外孙子。

她家男的又不知道中了什么邪，不干活了天天在家通宵玩网游，白天睡一天，晚上起来继续通宵。"空姐"就天天在家刷手机，四张嘴全靠娘家妈一个人养活。过了几年两个孩子越来越大，半大小子吃穷老子，娘家妈养不起了，就在我们小区给她们家租了个一楼的毛坯房住，拿笤帚把"空姐"打出门让她找活干。
"""
    
    # 创建预设流程
    preset = StoryCardsPreset()
    
    # 测试场景配置
    test_scenarios = [
        {
            "name": "默认配置",
            "params": {
                "max_segments": 6,
                "image_style": "humor", 
                "html_theme": "story",
                "segment_style": "narrative"
            }
        },
        {
            "name": "紧凑版本",
            "params": {
                "max_segments": 4,
                "image_style": "meme_classic",
                "html_theme": "default", 
                "segment_style": "scene"
            }
        },
        {
            "name": "详细版本",
            "params": {
                "max_segments": 8,
                "image_style": "xiaohongshu",
                "html_theme": "bright",
                "segment_style": "timeline"
            }
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\\n{'='*20} 场景 {i}: {scenario['name']} {'='*20}")
        
        # 创建初始状态
        initial_state = UniversalState(
            user_request=test_story,
            story_text=test_story,
            user_intent=None, execution_plan=None, selected_tools=None,
            raw_content=test_story, processed_content=None, content_segments=None,
            content_metadata=None, html_contents=None, image_paths=None,
            final_outputs=None, current_step=None, execution_history=None,
            error_message=None, tool_outputs=None, temp_data=None
        )
        
        print(f"📝 故事长度: {len(test_story)} 字符")
        print(f"⚙️ 配置参数: {scenario['params']}")
        
        # 演示流程步骤
        print(f"\\n🔄 处理流程:")
        print(f"   1. 📖 故事分段: 目标 {scenario['params']['max_segments']} 段")
        print(f"   2. 🎨 图像生成: {scenario['params']['image_style']} 风格")
        print(f"   3. 📄 HTML卡片: {scenario['params']['html_theme']} 主题")
        print(f"   4. 📸 最终输出: 生成完整图文卡片")
        
        # 演示分段效果
        print(f"\\n📚 预期分段效果 ({scenario['params']['segment_style']} 风格):")
        
        if scenario["params"]["segment_style"] == "narrative":
            expected_segments = [
                "第1段: 空姐的现状介绍",
                "第2段: 年轻时的美貌与相亲",
                "第3段: 与挖掘机司机恋爱",
                "第4段: 结婚生子与工作问题", 
                "第5段: 家庭经济困难",
                "第6段: 被迫外出工作"
            ]
        elif scenario["params"]["segment_style"] == "scene":
            expected_segments = [
                "第1段: 小区里的空姐",
                "第2段: 家中相亲场景",
                "第3段: 租房同居生活",
                "第4段: 法院工作经历"
            ]
        else:  # timeline
            expected_segments = [
                "第1段: 年轻时期 - 空姐生涯",
                "第2段: 相亲阶段 - 选择挖掘机司机",
                "第3段: 结婚初期 - 生子与辞职",
                "第4段: 工作安排 - 法院书记员",
                "第5段: 家庭变故 - 父亲去世",
                "第6段: 经济困难 - 被迫工作",
                "第7段: 现状描述 - 小区生活",
                "第8段: 邻里印象 - 社会评价"
            ]
        
        for segment in expected_segments:
            print(f"     • {segment}")
        
        # 演示每段的处理方式
        print(f"\\n🎨 图像生成策略:")
        if scenario["params"]["image_style"] == "humor":
            print("     • 幽默搞笑风格，手绘插画效果")
            print("     • 夸张表情和幽默元素")
            print("     • 适合轻松阅读的视觉效果")
        elif scenario["params"]["image_style"] == "meme_classic":
            print("     • 经典表情包风格，高对比度")
            print("     • 简单直接的视觉表达")
            print("     • 黑白配色，突出重点")
        else:  # xiaohongshu
            print("     • 现代时尚插画风格")
            print("     • 温暖治愈的色彩搭配")
            print("     • 适合社交媒体分享")
        
        print(f"\\n📱 HTML卡片设计:")
        if scenario["params"]["html_theme"] == "story":
            print("     • 温暖的米色背景 (#F8F5F0)")
            print("     • 深色文字，良好阅读体验")
            print("     • 经典边框装饰")
        elif scenario["params"]["html_theme"] == "default":
            print("     • 标准白色背景")
            print("     • 简洁现代的设计语言")
            print("     • 通用性强的布局")
        else:  # bright
            print("     • 明亮活泼的色彩")
            print("     • 年轻化的视觉风格")
            print("     • 吸引眼球的设计")
        
        print(f"\\n💡 该场景特点:")
        if scenario["name"] == "默认配置":
            print("     • 平衡的段落数量，适合大多数故事")
            print("     • 幽默风格减轻内容的沉重感")
            print("     • 温暖主题增强阅读体验")
        elif scenario["name"] == "紧凑版本":
            print("     • 少量段落，突出关键情节")
            print("     • 经典meme风格，简单直接")
            print("     • 适合快速阅读和分享")
        else:  # 详细版本
            print("     • 详细分段，完整还原故事")
            print("     • 时尚风格，适合年轻读者")
            print("     • 丰富的视觉层次")

def demo_technical_details():
    """演示技术实现详情"""
    print(f"\\n\\n🔧 技术实现详情")
    print("="*80)
    
    print("📋 工具链组合:")
    print("   1. StorySegmenterTool - 智能故事分段")
    print("      • 使用LLM理解故事结构")
    print("      • 支持多种分段策略（narrative, scene, timeline）")
    print("      • 自动生成段落标题和关键元素")
    
    print("\\n   2. AiImageGeneratorTool - AI图像生成")
    print("      • 基于Jmeng API的专业图像生成")
    print("      • LLM提示词优化系统")
    print("      • 自动上传到腾讯云COS")
    
    print("\\n   3. HtmlGeneratorTool - HTML卡片生成")
    print("      • 响应式卡片布局")
    print("      • 多主题支持（story, default, bright, dark）")
    print("      • 图文混排的专业设计")
    
    print("\\n   4. Html2ImageTool - 最终卡片输出")
    print("      • Playwright无头浏览器渲染")
    print("      • 高质量PNG输出")
    print("      • 自定义尺寸和分辨率")
    
    print("\\n⚙️ 参数传递机制:")
    print("   • ParameterManager: 集中式参数管理")
    print("   • UniversalState: 统一的状态管理")
    print("   • 工具间数据流转: state.update() 方式")
    print("   • 自定义参数覆盖: custom_params 优先级")
    
    print("\\n🛡️ 错误处理机制:")
    print("   • 工具级别的错误捕获和回退")
    print("   • LLM调用失败的预设模板")
    print("   • API调用超时和重试机制")
    print("   • 部分失败时的继续处理策略")
    
    print("\\n📊 质量保证:")
    print("   • 分段质量验证（长度、完整性检查）")
    print("   • 提示词后处理和优化")
    print("   • 图像URL有效性验证")
    print("   • HTML渲染质量检查")

def demo_usage_examples():
    """演示使用示例"""
    print(f"\\n\\n🚀 使用示例")
    print("="*80)
    
    print("💻 基础使用:")
    print('''
from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

# 创建预设流程
preset = StoryCardsPreset()

# 准备故事文本
story = "你的长故事文本..."

# 创建初始状态
state = UniversalState(user_request=story, story_text=story)

# 自定义参数
params = {
    "max_segments": 6,        # 分段数量
    "image_style": "humor",   # 图像风格
    "html_theme": "story",    # HTML主题
    "segment_style": "narrative"  # 分段策略
}

# 执行生成
result = preset.execute(state, params)

# 获取结果
segments = result.get("story_segments", [])
cards = result.get("segment_cards", [])
success_count = result.get("successful_cards", 0)
''')
    
    print("\\n🎛️ 高级配置:")
    print('''
# 自定义图像参数
custom_params = {
    "max_segments": 8,
    "image_style": "kawaii",
    "image_width": 1024,
    "image_height": 1024,
    "html_theme": "bright",
    "segment_style": "timeline"
}

# 批量处理多个故事
stories = [story1, story2, story3]
results = []
for story in stories:
    state = UniversalState(story_text=story)
    result = preset.execute(state, custom_params)
    results.append(result)
''')
    
    print("\\n📈 结果处理:")
    print('''
# 检查处理结果
if result.get("processing_complete"):
    total_segments = result["total_segments"]
    successful_cards = result["successful_cards"]
    
    print(f"处理完成: {successful_cards}/{total_segments} 张卡片")
    
    # 获取每张卡片的信息
    for card in result["segment_cards"]:
        if not card.get("error"):
            print(f"段落: {card['segment_title']}")
            print(f"图像: {card['image_url']}")
            print(f"卡片: {card['final_card']}")
''')

if __name__ == "__main__":
    demo_story_cards_generation()
    demo_technical_details()
    demo_usage_examples()
    
    print(f"\\n\\n🎉 演示完成！")
    print("💡 现在你有了一个完整的故事图文卡片生成系统:")
    print("   ✅ 智能故事分段处理")
    print("   ✅ 专业AI图像生成") 
    print("   ✅ 美观HTML卡片设计")
    print("   ✅ 灵活的参数配置")
    print("   ✅ 完善的错误处理")
    
    print(f"\\n🔄 推荐工作流程:")
    print("   1. 准备长文本故事")
    print("   2. 选择合适的配置参数")
    print("   3. 执行 StoryCardsPreset")
    print("   4. 获取生成的图文卡片")
    print("   5. 根据需要进行后期调整")