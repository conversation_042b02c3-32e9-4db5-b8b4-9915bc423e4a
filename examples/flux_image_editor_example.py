"""
图片编辑工具使用示例
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.generators.flux_image_editor import FluxImageEditor
from core.state import UniversalState

def example_basic_usage():
    """基础使用示例"""
    print("🚀 图片编辑工具基础使用示例")
    
    # 创建工具实例
    editor = FluxImageEditor()
    
    # 显示工具信息
    tool_info = editor.get_info()
    print(f"工具名称: {tool_info['name']}")
    print(f"工具描述: {tool_info['description']}")
    print(f"输入字段: {tool_info['input_keys']}")
    print(f"输出字段: {tool_info['output_keys']}")
    print()
    
    # 准备测试数据
    test_data = {
        "input_image": "https://example.com/sample-image.jpg",
        "edit_prompt": "Transform this image into a cyberpunk style with neon lights and futuristic elements",
        "edit_config": {
            "model": "black-forest-labs/flux-kontext-pro",
            "aspect_ratio": "16:9",
            "output_format": "png"
        }
    }
    
    print("📝 测试输入:")
    for key, value in test_data.items():
        print(f"  {key}: {value}")
    print()
    
    # 注意：实际执行需要有效的API配置
    print("⚠️ 注意：实际执行需要设置以下环境变量:")
    print("  - FLUX_API_KEY: Flux API密钥")
    print("  - FLUX_BASE_URL: Flux API基础URL")
    print("  - TENCENT_SECRET_ID: 腾讯云密钥ID (可选，用于云存储)")
    print("  - TENCENT_SECRET_KEY: 腾讯云密钥 (可选，用于云存储)")
    print("  - COS_REGION: COS区域 (可选，用于云存储)")
    print("  - COS_BUCKET: COS存储桶 (可选，用于云存储)")
    print()

def example_with_state():
    """在状态管理中使用示例"""
    print("🔄 在状态管理中使用图片编辑工具")
    
    # 创建初始状态
    state = UniversalState(
        user_request="编辑我的图片，让它更有科幻感",
        raw_content="",
        processed_content="",
        content_segments=[],
        current_step="image_editing",
        tool_outputs={}
    )
    
    # 添加图片编辑相关数据
    state["input_image"] = "https://example.com/user-image.jpg"
    state["edit_prompt"] = "Add futuristic sci-fi elements, holographic displays, and advanced technology"
    state["edit_config"] = {
        "model": "black-forest-labs/flux-kontext-max",  # 使用高质量模型
        "aspect_ratio": "match_input_image"
    }
    
    # 创建工具并执行
    editor = FluxImageEditor()
    result = editor.execute(state)
    
    print("🔍 执行结果结构:")
    for key in result.keys():
        print(f"  {key}: {type(result[key])}")
    
    # 模拟更新状态
    if "edited_image_url" in result:
        state["tool_outputs"]["image_editing"] = result
        print("✅ 图片编辑结果已保存到状态")
    else:
        print(f"❌ 图片编辑失败: {result.get('error_message', '未知错误')}")

def example_advanced_config():
    """高级配置示例"""
    print("⚙️ 高级配置示例")
    
    # 创建不同风格的编辑配置
    configs = {
        "艺术风格": {
            "edit_prompt": "Transform into an impressionist painting style with soft brushstrokes and vibrant colors",
            "edit_config": {
                "model": "black-forest-labs/flux-kontext-max",
                "aspect_ratio": "4:3",
                "output_format": "png",
                "seed": 42  # 固定种子以获得可重复结果
            }
        },
        "复古风格": {
            "edit_prompt": "Apply a vintage 1980s aesthetic with retro colors, film grain, and nostalgic atmosphere",
            "edit_config": {
                "model": "black-forest-labs/flux-kontext-pro",
                "aspect_ratio": "16:9",
                "output_format": "jpeg"
            }
        },
        "未来科技": {
            "edit_prompt": "Transform into a futuristic tech interface with holographic elements, neon accents, and digital overlays",
            "edit_config": {
                "model": "black-forest-labs/flux-kontext-max",
                "aspect_ratio": "match_input_image",
                "output_format": "png"
            }
        }
    }
    
    editor = FluxImageEditor()
    
    for style_name, config in configs.items():
        print(f"\n🎨 {style_name} 配置:")
        print(f"  提示词: {config['edit_prompt'][:50]}...")
        print(f"  模型: {config['edit_config']['model']}")
        print(f"  比例: {config['edit_config']['aspect_ratio']}")
        print(f"  格式: {config['edit_config']['output_format']}")
        
        # 构建完整输入
        test_input = {
            "input_image": "https://example.com/test-image.jpg",
            **config
        }
        
        # 验证输入格式
        if editor.validate_input(test_input):
            print("  ✅ 输入验证通过")
        else:
            print("  ❌ 输入验证失败")

def example_error_handling():
    """错误处理示例"""
    print("🚨 错误处理示例")
    
    editor = FluxImageEditor()
    
    # 测试各种错误情况
    error_cases = [
        {
            "name": "缺少输入图片",
            "input": {
                "edit_prompt": "test prompt",
                "edit_config": {}
            }
        },
        {
            "name": "无效图片URL",
            "input": {
                "input_image": "invalid-url",
                "edit_prompt": "test prompt",
                "edit_config": {}
            }
        },
        {
            "name": "空编辑指令",
            "input": {
                "input_image": "https://example.com/image.jpg",
                "edit_prompt": "",
                "edit_config": {}
            }
        }
    ]
    
    for case in error_cases:
        print(f"\n🔍 测试: {case['name']}")
        result = editor.process(case['input'])
        
        if "error_message" in result:
            print(f"  ❌ 预期错误: {result['error_message']}")
        else:
            print(f"  ⚠️ 意外通过验证")

if __name__ == "__main__":
    print("=" * 60)
    print("📸 Flux图片编辑工具完整使用示例")
    print("=" * 60)
    print()
    
    example_basic_usage()
    print("\n" + "="*60 + "\n")
    
    example_with_state()
    print("\n" + "="*60 + "\n")
    
    example_advanced_config()
    print("\n" + "="*60 + "\n")
    
    example_error_handling()
    print("\n" + "="*60)
    print("✨ 示例演示完成！")