"""
完整Meme生成流程演示 - 展示从用户输入到最终图片的完整过程
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.state import UniversalState
from graphs.preset_graphs.meme_preset_v2 import MemePresetGraphV2

def demo_complete_meme_flow():
    """演示完整的Meme生成流程"""
    print("🎭 完整Meme生成流程演示")
    print("="*80)
    
    # 创建Meme预设流程
    preset = MemePresetGraphV2()
    
    # 测试场景
    scenarios = [
        {
            "name": "基础搞笑Meme",
            "user_request": "程序员看到bug时的表情",
            "custom_params": {
                "image_style": "humor",
                "meme_keywords": ["程序员", "bug", "震惊"],
                "mood": "既无奈又搞笑"
            }
        },
        {
            "name": "可爱治愈Meme",
            "user_request": "小猫咪第一次用电脑",
            "custom_params": {
                "image_style": "kawaii",
                "image_size": "600x600",
                "meme_keywords": ["小猫", "电脑", "好奇"],
                "mood": "可爱好奇",
                "html_theme": "bright"
            }
        },
        {
            "name": "经典表情包",
            "user_request": "当你发现今天是周一",
            "custom_params": {
                "image_style": "meme_classic",
                "image_size": "800x600",
                "meme_keywords": ["周一", "上班", "绝望"],
                "mood": "无奈绝望但带点幽默"
            }
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{'='*20} 场景 {i}: {scenario['name']} {'='*20}")
        
        # 创建初始状态
        initial_state = UniversalState(
            user_request=scenario["user_request"],
            user_intent=None, execution_plan=None, selected_tools=None,
            raw_content=scenario["user_request"], processed_content=None, content_segments=None,
            content_metadata=None, html_contents=None, image_paths=None,
            final_outputs=None, current_step=None, execution_history=None,
            error_message=None, tool_outputs=None, temp_data=None
        )
        
        print(f"📝 用户请求: {scenario['user_request']}")
        print(f"⚙️ 自定义参数: {scenario['custom_params']}")
        
        # 演示参数准备过程
        print(f"\n🔧 工具参数准备过程:")
        for tool_name in preset.tools:
            try:
                params = preset._prepare_tool_params(
                    tool_name, 
                    initial_state, 
                    scenario["custom_params"]
                )
                formatted_params = preset._format_params_for_display(params)
                print(f"   {tool_name}: {formatted_params}")
                
                # 特别展示图像生成的详细参数
                if tool_name == "generator.ai_image" and params:
                    print(f"      → 原始内容: {params.get('prompt', 'N/A')}")
                    print(f"      → 图像风格: {params.get('style', 'N/A')}")
                    print(f"      → 图像尺寸: {params.get('width', 'N/A')}x{params.get('height', 'N/A')}")
                    print(f"      → Meme关键词: {params.get('meme_keywords', 'N/A')}")
                    print(f"      → 情绪氛围: {params.get('mood', 'N/A')}")
                    print(f"      → LLM优化: {'启用' if params.get('enhance_prompt') else '禁用'}")
                
                # 展示HTML生成的风格配置
                elif tool_name == "generator.html" and params.get("style_config"):
                    style_config = params["style_config"]
                    print(f"      → 风格名称: {style_config['style_name']}")
                    print(f"      → 主背景色: {style_config['colors']['primary_bg']}")
                    print(f"      → 主字体: {style_config['typography']['main_font']}")
                    
            except Exception as e:
                print(f"   {tool_name}: ❌ 参数准备失败: {e}")
        
        print(f"\n💡 这个场景的特色:")
        if scenario["name"] == "基础搞笑Meme":
            print("   • 使用极简手绘humor风格")
            print("   • LLM会优化提示词，加入程序员相关的搞笑元素")
            print("   • HTML使用Comic Sans字体和手绘边框")
        elif scenario["name"] == "可爱治愈Meme":
            print("   • 使用kawaii可爱风格")
            print("   • 粉嫩色彩和大眼睛特征")
            print("   • 亮色HTML主题，营造治愈氛围")
        elif scenario["name"] == "经典表情包":
            print("   • 使用传统meme_classic风格")
            print("   • 黑色粗体字，高对比度")
            print("   • 经典的上下文字布局")

def demo_llm_optimization_process():
    """演示LLM优化过程"""
    print(f"\n\n🤖 LLM提示词优化过程演示")
    print("="*80)
    
    from core.prompt_optimizer import get_prompt_optimizer
    
    optimizer = get_prompt_optimizer()
    
    # 模拟用户内容和参数
    user_content = "程序员深夜debug的心路历程"
    style = "humor"
    additional_requirements = {
        "keywords": ["程序员", "代码", "咖啡", "深夜"],
        "mood": "疲惫但专注",
        "elements": ["电脑屏幕", "代码行", "咖啡杯"],
        "avoid": ["复杂背景", "太多文字"]
    }
    
    print(f"📝 用户原始内容: {user_content}")
    print(f"🎨 目标风格: {style}")
    print(f"⚙️ 额外要求: {additional_requirements}")
    
    print(f"\n🔄 LLM优化过程:")
    print("1. 获取风格配置...")
    style_config = optimizer.style_manager.get_style(style)
    print(f"   ✅ 风格: {style_config.name}")
    
    print("2. 构建优化模板...")
    template = optimizer.optimization_templates.get(style, "")
    print(f"   ✅ 模板长度: {len(template)} 字符")
    
    print("3. 格式化额外要求...")
    formatted_req = optimizer._format_additional_requirements(additional_requirements)
    print(f"   ✅ 格式化结果: {formatted_req}")
    
    print("4. 调用LLM优化（模拟）...")
    # 这里使用回退方案演示，实际使用时会调用真实LLM
    optimized_prompt = optimizer._fallback_prompt(user_content, style)
    print(f"   ✅ 优化结果: {optimized_prompt}")
    
    print("5. 后处理...")
    final_prompt = optimizer._post_process_prompt(optimized_prompt, style)
    print(f"   ✅ 最终提示词: {final_prompt}")

def demo_configuration_benefits():
    """演示配置化系统的优势"""
    print(f"\n\n✨ 配置化系统优势演示")
    print("="*80)
    
    print("🎯 1. 风格配置的专业性:")
    from core.style_config import get_style_config_manager
    style_manager = get_style_config_manager()
    
    humor_style = style_manager.get_style("humor")
    print(f"   风格名称: {humor_style.name}")
    print(f"   详细描述: {humor_style.description}")
    print(f"   视觉指导: {humor_style.visual_style[:100]}...")
    
    print(f"\n🔧 2. 参数传递的清晰性:")
    print("   用户只需提供:")
    print("   • image_style: 'humor' | 'kawaii' | 'meme_classic'")
    print("   • image_size: '810x810' | '600x600' | '1024x1024'")
    print("   • meme_keywords: ['程序员', 'bug']")
    print("   • html_theme: 'default' | 'dark' | 'bright'")
    
    print(f"\n⚡ 3. LLM优化的智能性:")
    print("   • 自动根据风格选择合适的优化模板")
    print("   • 将用户要求转化为专业绘画描述")
    print("   • 包含技术参数和质量要求")
    print("   • 有回退机制保证稳定性")
    
    print(f"\n🛡️ 4. 系统的可靠性:")
    print("   • 配置与代码分离，易于维护")
    print("   • 多层回退机制，避免单点失败")
    print("   • 参数验证和后处理，确保输出质量")
    print("   • 详细的日志记录，便于调试")

if __name__ == "__main__":
    demo_complete_meme_flow()
    demo_llm_optimization_process()
    demo_configuration_benefits()
    
    print(f"\n\n🎉 完整演示结束！")
    print("💡 现在你有了一个:")
    print("   ✅ 配置化的风格管理系统")
    print("   ✅ 专业的LLM提示词优化器")
    print("   ✅ 灵活的参数传递机制")
    print("   ✅ 完整的Meme生成流程")
    
    print(f"\n🚀 使用方法:")
    print("   preset = MemePresetGraphV2()")
    print("   result = preset.execute(state, {")
    print("       'image_style': 'humor',")
    print("       'meme_keywords': ['程序员', 'bug'],")
    print("       'html_theme': 'dark'")
    print("   })")