"""
视频编辑Agent使用示例
演示如何使用Agent进行对话式视频编辑
"""
import asyncio
import json
from datetime import datetime
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.video_edit_agent import VideoEditAgent, AgentResponse

class VideoEditAgentDemo:
    """视频编辑Agent演示"""
    
    def __init__(self):
        self.agent = None
        self.project_id = "demo_project_001"
        self.user_id = "demo_user"
    
    async def run_demo(self):
        """运行演示"""
        print("🎬 视频编辑Agent演示开始")
        print("=" * 50)
        
        # 1. 初始化Agent
        await self.initialize_agent()
        
        # 2. 模拟对话场景
        await self.demo_conversation_scenarios()
        
        print("\n🎉 演示完成！")
    
    async def initialize_agent(self):
        """初始化Agent"""
        print("🤖 正在初始化视频编辑Agent...")
        
        self.agent = VideoEditAgent(
            project_id=self.project_id,
            user_id=self.user_id
        )
        
        print(f"✅ Agent初始化完成")
        print(f"   - Agent ID: {self.agent.agent_id}")
        print(f"   - 项目ID: {self.project_id}")
        print(f"   - 用户ID: {self.user_id}")
        print()
    
    async def demo_conversation_scenarios(self):
        """演示对话场景"""
        
        # 场景1: 项目状态查询
        await self.demo_status_query()
        
        # 场景2: 编辑请求
        await self.demo_edit_request()
        
        # 场景3: 确认操作
        await self.demo_confirmation()
        
        # 场景4: 错误处理
        await self.demo_error_handling()
    
    async def demo_status_query(self):
        """演示状态查询"""
        print("📋 场景1: 项目状态查询")
        print("-" * 30)
        
        user_input = "当前项目的状态怎么样？"
        print(f"👤 用户: {user_input}")
        
        response = await self.agent.process_user_input(user_input)
        await self.display_agent_response(response)
        print()
    
    async def demo_edit_request(self):
        """演示编辑请求"""
        print("✂️ 场景2: 编辑请求")
        print("-" * 30)
        
        user_input = "把第一段的背景音乐换成更轻松的钢琴曲"
        print(f"👤 用户: {user_input}")
        
        response = await self.agent.process_user_input(user_input)
        await self.display_agent_response(response)
        
        # 如果需要确认，保存响应以便后续确认
        if response.requires_confirmation:
            self.pending_confirmation_response = response
        
        print()
    
    async def demo_confirmation(self):
        """演示确认操作"""
        print("✅ 场景3: 确认操作")
        print("-" * 30)
        
        if hasattr(self, 'pending_confirmation_response'):
            print("👤 用户: 确认执行")
            
            response = await self.agent.confirm_operation(
                confirmation=True,
                user_feedback="请使用轻快一点的钢琴曲"
            )
            await self.display_agent_response(response)
            
            # 模拟等待执行完成
            if response.response_type == "progress":
                await self.simulate_execution_progress()
        else:
            print("⚠️ 没有待确认的操作")
        
        print()
    
    async def demo_error_handling(self):
        """演示错误处理"""
        print("❌ 场景4: 错误处理")
        print("-" * 30)
        
        # 模拟一个可能导致错误的请求
        user_input = "删除整个视频文件"
        print(f"👤 用户: {user_input}")
        
        response = await self.agent.process_user_input(user_input)
        await self.display_agent_response(response)
        print()
    
    async def simulate_execution_progress(self):
        """模拟执行进度"""
        print("\n📊 执行进度模拟:")
        
        progress_steps = [
            (20, "正在分析音频轨道..."),
            (40, "正在搜索合适的钢琴音乐..."),
            (60, "正在替换背景音乐..."),
            (80, "正在调整音量平衡..."),
            (100, "处理完成！")
        ]
        
        for progress, message in progress_steps:
            print(f"   🔄 {progress}% - {message}")
            await asyncio.sleep(0.5)  # 模拟处理时间
        
        print("   ✅ 编辑操作已完成，您可以预览效果")
    
    async def display_agent_response(self, response: AgentResponse):
        """显示Agent响应"""
        print(f"🤖 Agent ({response.response_type}):")
        print(f"   {response.content}")
        
        if response.suggestions:
            print("   💡 建议:")
            for suggestion in response.suggestions:
                print(f"      - {suggestion}")
        
        if response.requires_confirmation:
            print("   ⏳ 等待用户确认...")
        
        # 显示元数据（调试用）
        if response.metadata:
            print(f"   📋 元数据: {json.dumps(response.metadata, indent=2, ensure_ascii=False)}")

class InteractiveDemo:
    """交互式演示"""
    
    def __init__(self):
        self.agent = VideoEditAgent("interactive_project", "interactive_user")
    
    async def run_interactive(self):
        """运行交互式演示"""
        print("🎬 视频编辑Agent交互式演示")
        print("输入 'quit' 退出，输入 'help' 查看帮助")
        print("=" * 50)
        
        while True:
            try:
                user_input = input("\n👤 您: ").strip()
                
                if user_input.lower() == 'quit':
                    print("👋 再见！")
                    break
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                elif user_input.lower() == 'yes' or user_input.lower() == '确认':
                    response = await self.agent.confirm_operation(True)
                elif user_input.lower() == 'no' or user_input.lower() == '取消':
                    response = await self.agent.confirm_operation(False)
                else:
                    response = await self.agent.process_user_input(user_input)
                
                await self.display_response(response)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
        🆘 帮助信息:
        
        📝 可以尝试的命令:
        - "当前项目状态如何？"
        - "把第一段的背景音乐换成轻松的"
        - "替换语音内容为'欢迎大家'"
        - "添加字幕"
        - "确认" 或 "yes" - 确认操作
        - "取消" 或 "no" - 取消操作
        - "quit" - 退出演示
        
        💡 提示: Agent会理解自然语言，可以用中文描述您的编辑需求
        """
        print(help_text)
    
    async def display_response(self, response: AgentResponse):
        """显示响应"""
        print(f"\n🤖 Agent: {response.content}")
        
        if response.suggestions:
            print("\n💡 建议:")
            for i, suggestion in enumerate(response.suggestions, 1):
                print(f"   {i}. {suggestion}")

async def main():
    """主函数"""
    print("选择演示模式:")
    print("1. 自动演示 (展示预设场景)")
    print("2. 交互式演示 (手动输入)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        demo = VideoEditAgentDemo()
        await demo.run_demo()
    elif choice == "2":
        demo = InteractiveDemo()
        await demo.run_interactive()
    else:
        print("无效选择，运行自动演示...")
        demo = VideoEditAgentDemo()
        await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main())
