"""
WebSocket视频编辑Agent服务器
提供实时的Agent交互服务
"""
import asyncio
import json
import logging
from typing import Dict, Set
import websockets
from websockets.server import WebSocketServerProtocol
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.video_edit_agent import VideoEditAgent, AgentResponse

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSocketAgentServer:
    """WebSocket Agent服务器"""
    
    def __init__(self, host: str = "localhost", port: int = 8765):
        self.host = host
        self.port = port
        self.agents: Dict[str, VideoEditAgent] = {}
        self.connections: Dict[str, WebSocketServerProtocol] = {}
        self.user_sessions: Dict[str, str] = {}  # user_id -> session_id
    
    async def start_server(self):
        """启动WebSocket服务器"""
        logger.info(f"🚀 启动WebSocket Agent服务器: ws://{self.host}:{self.port}")
        
        async with websockets.serve(
            self.handle_connection,
            self.host,
            self.port,
            ping_interval=20,
            ping_timeout=10
        ):
            logger.info("✅ 服务器启动成功，等待连接...")
            await asyncio.Future()  # 保持服务器运行
    
    async def handle_connection(self, websocket: WebSocketServerProtocol, path: str):
        """处理WebSocket连接"""
        session_id = None
        try:
            logger.info(f"📱 新连接: {websocket.remote_address}")
            
            # 等待客户端发送初始化消息
            init_message = await websocket.recv()
            init_data = json.loads(init_message)
            
            # 验证初始化数据
            if not self._validate_init_data(init_data):
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "无效的初始化数据"
                }))
                return
            
            # 创建或获取Agent会话
            session_id = await self._create_or_get_session(init_data, websocket)
            
            # 发送连接成功消息
            await websocket.send(json.dumps({
                "type": "connected",
                "session_id": session_id,
                "message": "连接成功，可以开始对话"
            }))
            
            # 处理消息循环
            await self._message_loop(websocket, session_id)
            
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 连接断开: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"❌ 连接处理错误: {e}")
        finally:
            # 清理连接
            if session_id:
                await self._cleanup_connection(session_id)
    
    async def _create_or_get_session(self, init_data: Dict, websocket: WebSocketServerProtocol) -> str:
        """创建或获取Agent会话"""
        user_id = init_data["user_id"]
        project_id = init_data["project_id"]
        
        # 检查是否已有会话
        if user_id in self.user_sessions:
            session_id = self.user_sessions[user_id]
            logger.info(f"🔄 恢复现有会话: {session_id}")
        else:
            # 创建新的Agent实例
            agent = VideoEditAgent(project_id, user_id)
            session_id = agent.agent_id
            
            self.agents[session_id] = agent
            self.user_sessions[user_id] = session_id
            
            # 将WebSocket连接添加到Agent
            agent.websocket_connections.append(websocket)
            
            logger.info(f"🆕 创建新会话: {session_id}")
        
        # 保存连接映射
        self.connections[session_id] = websocket
        
        return session_id
    
    async def _message_loop(self, websocket: WebSocketServerProtocol, session_id: str):
        """消息处理循环"""
        agent = self.agents[session_id]
        
        async for message in websocket:
            try:
                data = json.loads(message)
                await self._handle_message(agent, data, websocket)
                
            except json.JSONDecodeError:
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": "无效的JSON格式"
                }))
            except Exception as e:
                logger.error(f"❌ 消息处理错误: {e}")
                await websocket.send(json.dumps({
                    "type": "error",
                    "message": f"处理消息时出错: {str(e)}"
                }))
    
    async def _handle_message(self, agent: VideoEditAgent, data: Dict, websocket: WebSocketServerProtocol):
        """处理具体消息"""
        message_type = data.get("type")
        
        if message_type == "user_input":
            await self._handle_user_input(agent, data, websocket)
        elif message_type == "confirmation":
            await self._handle_confirmation(agent, data, websocket)
        elif message_type == "ping":
            await websocket.send(json.dumps({"type": "pong"}))
        else:
            await websocket.send(json.dumps({
                "type": "error",
                "message": f"未知消息类型: {message_type}"
            }))
    
    async def _handle_user_input(self, agent: VideoEditAgent, data: Dict, websocket: WebSocketServerProtocol):
        """处理用户输入"""
        user_input = data.get("content", "")
        input_type = data.get("input_type", "text")
        
        logger.info(f"💬 用户输入: {user_input}")
        
        # 处理用户输入
        response = await agent.process_user_input(user_input, input_type)
        
        # 发送响应
        await self._send_agent_response(response, websocket)
    
    async def _handle_confirmation(self, agent: VideoEditAgent, data: Dict, websocket: WebSocketServerProtocol):
        """处理确认消息"""
        confirmation = data.get("confirmed", False)
        feedback = data.get("feedback", "")
        
        logger.info(f"✅ 用户确认: {confirmation}")
        
        # 处理确认
        response = await agent.confirm_operation(confirmation, feedback)
        
        # 发送响应
        await self._send_agent_response(response, websocket)
    
    async def _send_agent_response(self, response: AgentResponse, websocket: WebSocketServerProtocol):
        """发送Agent响应"""
        response_data = {
            "type": "agent_response",
            "response": response.to_dict()
        }
        
        await websocket.send(json.dumps(response_data, ensure_ascii=False, default=str))
        logger.info(f"🤖 Agent响应: {response.response_type}")
    
    def _validate_init_data(self, data: Dict) -> bool:
        """验证初始化数据"""
        required_fields = ["user_id", "project_id"]
        return all(field in data for field in required_fields)
    
    async def _cleanup_connection(self, session_id: str):
        """清理连接"""
        if session_id in self.connections:
            del self.connections[session_id]
        
        if session_id in self.agents:
            agent = self.agents[session_id]
            # 清理Agent中的WebSocket连接
            agent.websocket_connections.clear()
        
        logger.info(f"🧹 清理会话: {session_id}")

class WebSocketClient:
    """WebSocket客户端示例"""
    
    def __init__(self, uri: str = "ws://localhost:8765"):
        self.uri = uri
        self.websocket = None
    
    async def connect_and_chat(self):
        """连接并开始聊天"""
        try:
            async with websockets.connect(self.uri) as websocket:
                self.websocket = websocket
                
                # 发送初始化消息
                init_message = {
                    "user_id": "demo_user",
                    "project_id": "demo_project"
                }
                await websocket.send(json.dumps(init_message))
                
                # 等待连接确认
                response = await websocket.recv()
                response_data = json.loads(response)
                print(f"🔗 {response_data['message']}")
                
                # 启动消息接收任务
                receive_task = asyncio.create_task(self._receive_messages())
                
                # 开始交互循环
                await self._input_loop()
                
                # 取消接收任务
                receive_task.cancel()
                
        except Exception as e:
            print(f"❌ 连接错误: {e}")
    
    async def _receive_messages(self):
        """接收消息"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self._handle_server_message(data)
        except asyncio.CancelledError:
            pass
        except Exception as e:
            print(f"❌ 接收消息错误: {e}")
    
    async def _handle_server_message(self, data: Dict):
        """处理服务器消息"""
        message_type = data.get("type")
        
        if message_type == "agent_response":
            response = data["response"]
            print(f"\n🤖 Agent: {response['content']}")
            
            if response.get("requires_confirmation"):
                print("   ⏳ 需要确认 (输入 'yes' 确认, 'no' 取消)")
            
        elif message_type == "progress_update":
            print(f"📊 {data['message']} ({data['progress']:.1f}%)")
            
        elif message_type == "error":
            print(f"❌ 错误: {data['message']}")
    
    async def _input_loop(self):
        """输入循环"""
        print("\n💬 开始对话 (输入 'quit' 退出):")
        
        while True:
            try:
                user_input = await asyncio.get_event_loop().run_in_executor(
                    None, input, "\n👤 您: "
                )
                
                if user_input.lower() == 'quit':
                    break
                
                # 处理确认消息
                if user_input.lower() in ['yes', 'y', '确认']:
                    message = {
                        "type": "confirmation",
                        "confirmed": True
                    }
                elif user_input.lower() in ['no', 'n', '取消']:
                    message = {
                        "type": "confirmation",
                        "confirmed": False
                    }
                else:
                    message = {
                        "type": "user_input",
                        "content": user_input,
                        "input_type": "text"
                    }
                
                await self.websocket.send(json.dumps(message, ensure_ascii=False))
                
            except KeyboardInterrupt:
                break

async def run_server():
    """运行服务器"""
    server = WebSocketAgentServer()
    await server.start_server()

async def run_client():
    """运行客户端"""
    client = WebSocketClient()
    await client.connect_and_chat()

async def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "client":
        print("🔌 启动WebSocket客户端...")
        await run_client()
    else:
        print("🖥️ 启动WebSocket服务器...")
        await run_server()

if __name__ == "__main__":
    asyncio.run(main())
