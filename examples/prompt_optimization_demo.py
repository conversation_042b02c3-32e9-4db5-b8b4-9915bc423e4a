"""
LLM提示词优化演示 - 展示专业绘画提示词生成效果
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.prompt_optimizer import get_prompt_optimizer

def demo_prompt_optimization():
    """演示LLM提示词优化"""
    print("🎨 LLM专业绘画提示词优化演示")
    print("="*70)
    
    optimizer = get_prompt_optimizer()
    
    # 测试内容
    test_contents = [
        "程序员看到bug时的表情",
        "小猫咪学编程", 
        "上班族的周一早晨",
        "考试前的学生"
    ]
    
    # 测试不同风格
    styles = ["humor", "kawaii", "meme_classic"]
    
    for content in test_contents:
        print(f"\n📝 原始内容: {content}")
        print("-" * 50)
        
        for style in styles:
            print(f"\n🎭 {style} 风格:")
            try:
                # 模拟LLM优化（实际需要配置LLM）
                optimized = optimizer._fallback_prompt(content, style)
                print(f"   优化结果: {optimized}")
                
                # 如果要测试真实LLM优化，取消注释下面这行
                # optimized = optimizer.optimize_prompt(content, style)
                
            except Exception as e:
                print(f"   ❌ 优化失败: {e}")

def demo_additional_requirements():
    """演示额外要求功能"""
    print("\n\n⚙️ 额外要求功能演示")
    print("="*70)
    
    optimizer = get_prompt_optimizer()
    content = "程序员debug时的心路历程"
    
    # 不同的额外要求
    requirements_examples = [
        {
            "name": "基础要求",
            "requirements": {}
        },
        {
            "name": "关键词要求",
            "requirements": {
                "keywords": ["代码", "电脑", "咖啡"],
                "mood": "焦虑但专注"
            }
        },
        {
            "name": "完整要求",
            "requirements": {
                "keywords": ["程序员", "bug", "深夜"],
                "colors": "蓝色和橙色为主",
                "mood": "紧张但有希望",
                "elements": ["电脑屏幕", "代码", "咖啡杯"],
                "avoid": ["复杂背景", "太多文字"]
            }
        }
    ]
    
    for example in requirements_examples:
        print(f"\n📋 {example['name']}:")
        if example["requirements"]:
            for key, value in example["requirements"].items():
                print(f"   {key}: {value}")
        else:
            print("   无额外要求")
        
        # 格式化额外要求
        if example["requirements"]:
            formatted = optimizer._format_additional_requirements(example["requirements"])
            print(f"   格式化结果: {formatted}")

def demo_style_comparison():
    """演示不同风格的提示词模板"""
    print("\n\n🎨 风格模板对比演示")
    print("="*70)
    
    optimizer = get_prompt_optimizer()
    content = "工作中的小确幸"
    
    styles = ["humor", "meme_classic", "kawaii", "xiaohongshu", "business"]
    
    for style in styles:
        print(f"\n🎭 {style.upper()} 风格模板:")
        template = optimizer.optimization_templates.get(style, "模板未找到")
        
        # 显示模板的前200字符
        template_preview = template.replace("{content}", "[内容]")
        template_preview = template_preview.replace("{style_description}", "[风格描述]")
        template_preview = template_preview.replace("{visual_guide}", "[视觉指导]")
        
        lines = template_preview.split('\n')
        preview_lines = []
        char_count = 0
        
        for line in lines:
            if char_count + len(line) > 300:
                preview_lines.append("   ...")
                break
            preview_lines.append(f"   {line}")
            char_count += len(line)
        
        print('\n'.join(preview_lines))

def demo_fallback_prompts():
    """演示回退提示词"""
    print("\n\n🔄 回退提示词演示")
    print("="*70)
    
    optimizer = get_prompt_optimizer()
    
    test_cases = [
        ("休闲时光", "humor"),
        ("工作压力", "meme_classic"), 
        ("宠物日常", "kawaii"),
        ("生活分享", "xiaohongshu"),
        ("团队合作", "business")
    ]
    
    for content, style in test_cases:
        fallback = optimizer._fallback_prompt(content, style)
        print(f"\n📝 {content} ({style}):")
        print(f"   回退提示词: {fallback}")

if __name__ == "__main__":
    demo_prompt_optimization()
    demo_additional_requirements()
    demo_style_comparison()
    demo_fallback_prompts()
    
    print("\n\n🎉 演示完成！")
    print("💡 LLM提示词优化系统特点:")
    print("  • 专业的绘画提示词工程模板")
    print("  • 支持多种风格的专业优化")
    print("  • 可以添加额外要求和限制")
    print("  • 有完善的回退机制保证稳定性")
    print("  • 后处理确保提示词质量")
    
    print("\n🔧 使用方法:")
    print("  optimizer.optimize_prompt(content, style_name, additional_requirements)")
    print("  # content: 用户原始内容")
    print("  # style_name: humor, kawaii, meme_classic, xiaohongshu, business")
    print("  # additional_requirements: {keywords, colors, mood, elements, avoid}")