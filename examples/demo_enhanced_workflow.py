"""
增强版视频理解工作流演示 - 集成COS云存储
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from video_understanding_workflow_v2 import run_video_understanding_v2

def demo_enhanced_workflow():
    """演示增强版工作流"""
    
    print("🚀 增强版视频理解工作流演示")
    print("=" * 60)
    print("✨ 新增功能:")
    print("  📤 自动COS云存储")
    print("  🔗 分享链接生成")
    print("  💾 大文件智能缓存")
    print("  🌐 URL视频完整支持")
    print("=" * 60)
    
    # 使用测试视频
    video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    print(f"📹 分析视频: {video_url}")
    print("🔄 开始增强版工作流...")
    
    result = run_video_understanding_v2(
        video_url=video_url,
        user_request="请详细分析这个视频，生成完整的脚本，包括多种ability类型选择，并展示COS集成功能"
    )
    
    if result and not result.get("error_message"):
        print("\n🎉 增强版工作流执行成功！")
        
        # 显示处理信息
        processing_info = result.get("processing_info", {})
        print(f"\n📊 处理信息:")
        print(f"  🔧 处理方法: {processing_info.get('method', 'unknown')}")
        print(f"  📁 文件来源: {processing_info.get('source_type', 'unknown')}")
        
        if processing_info.get("cos_cache_url"):
            print(f"  💾 COS缓存: {processing_info['cos_cache_url']}")
        
        # 显示分析结果
        script_summary = result.get("script_summary", {})
        if script_summary:
            video_info = script_summary.get("video_info", {})
            stats = script_summary.get("statistics", {})
            
            print(f"\n🎬 分析结果:")
            print(f"  📺 标题: {video_info.get('title', 'N/A')}")
            print(f"  🎭 主题: {video_info.get('theme', 'N/A')}")
            print(f"  📸 镜头数: {stats.get('total_shots', 0)}")
            print(f"  ⏱️ 时长: {stats.get('total_duration_seconds', 0)}秒")
            print(f"  👥 角色数: {stats.get('character_count', 0)}")
        
        # 显示输出信息
        output_file = result.get("output_file_path")
        cos_url = result.get("cos_url")
        
        print(f"\n📄 输出文件:")
        print(f"  💾 本地文件: {output_file}")
        
        if cos_url:
            print(f"  🔗 云端链接: {cos_url}")
            print(f"  📤 已上传到COS，可直接分享使用")
        else:
            print(f"  💡 COS未配置，仅保存到本地")
        
        # 显示成功消息
        success_message = result.get("success_message", "")
        if "🔗 在线访问链接:" in success_message:
            print(f"\n✨ 增强功能已启用:")
            print(f"  📤 云存储: ✅")
            print(f"  🔗 分享链接: ✅")
        
        return True
    else:
        print(f"\n❌ 增强版工作流执行失败: {result.get('error_message') if result else '未知错误'}")
        return False

def show_enhancement_summary():
    """展示增强功能总结"""
    
    print(f"\n🎯 增强功能总结:")
    print("=" * 60)
    
    enhancements = [
        {
            "feature": "🌐 URL视频完整支持",
            "description": "自动下载、预处理、分析URL视频",
            "benefit": "支持任意大小的在线视频"
        },
        {
            "feature": "📤 自动COS云存储",
            "description": "脚本结果自动上传到腾讯云COS",
            "benefit": "生成分享链接，便于团队协作"
        },
        {
            "feature": "💾 智能视频缓存",
            "description": "大文件URL视频缓存到COS",
            "benefit": "避免重复下载，提升效率"
        },
        {
            "feature": "🔧 智能预处理",
            "description": "压缩+分段策略处理大文件",
            "benefit": "突破20MB限制，支持任意大小"
        },
        {
            "feature": "🎨 多种Ability类型",
            "description": "9种ability类型智能选择",
            "benefit": "适配不同的内容生成需求"
        },
        {
            "feature": "📋 完整JSON输出",
            "description": "严格按照接口规范输出",
            "benefit": "直接对接业务系统"
        }
    ]
    
    for i, enhancement in enumerate(enhancements, 1):
        print(f"{i}. {enhancement['feature']}")
        print(f"   📝 功能: {enhancement['description']}")
        print(f"   💡 优势: {enhancement['benefit']}")
        print()

def show_usage_examples():
    """展示使用示例"""
    
    print(f"📋 使用示例:")
    print("=" * 60)
    
    examples = [
        {
            "scenario": "小文件URL视频",
            "command": "python video_understanding_workflow_v2.py --video_url 'https://small-video.com/video.mp4'",
            "process": "直接分析 → 生成脚本 → 上传COS → 返回分享链接"
        },
        {
            "scenario": "大文件URL视频",
            "command": "python video_understanding_workflow_v2.py --video_url 'https://large-video.com/big_video.mp4'",
            "process": "下载 → 压缩/分段 → 分析 → 合并 → 上传COS → 返回链接"
        },
        {
            "scenario": "本地大文件",
            "command": "python video_understanding_workflow_v2.py --video_path '/path/to/large_video.mp4'",
            "process": "压缩/分段 → 分析 → 合并 → 上传COS → 返回链接"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['scenario']}")
        print(f"   💻 命令: {example['command']}")
        print(f"   🔄 流程: {example['process']}")
        print()

def main():
    """主演示函数"""
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        return
    
    # 检查COS配置
    cos_configured = all([
        os.getenv("TENCENT_SECRET_ID"),
        os.getenv("TENCENT_SECRET_KEY"), 
        os.getenv("COS_REGION"),
        os.getenv("COS_BUCKET")
    ])
    
    if cos_configured:
        print("✅ COS配置已启用，将展示完整增强功能")
    else:
        print("⚠️ COS未配置，将跳过云存储功能")
    
    # 运行演示
    success = demo_enhanced_workflow()
    
    # 显示增强功能总结
    show_enhancement_summary()
    
    # 显示使用示例
    show_usage_examples()
    
    print("=" * 60)
    if success:
        print("🎉 增强版视频理解工作流演示成功！")
        print("💡 现在支持:")
        print("  🌐 任意大小URL视频分析")
        print("  📤 自动云存储和分享")
        print("  🔧 智能预处理策略")
        print("  🎨 多种ability类型")
        if cos_configured:
            print("  ✨ COS云存储功能已启用")
    else:
        print("❌ 演示失败，请检查配置")

if __name__ == "__main__":
    main()
