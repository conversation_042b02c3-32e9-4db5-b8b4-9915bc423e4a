"""
Meme生成演示 - 展示如何使用配置化的参数系统
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.state import UniversalState
from core.style_config import get_style_config_manager
from core.parameter_manager import get_param_manager

def demo_style_configurations():
    """演示不同风格配置"""
    print("🎨 Meme风格配置演示")
    print("="*60)
    
    style_manager = get_style_config_manager()
    
    # 演示不同的meme风格
    meme_styles = ["humor", "meme_classic", "kawaii"]
    content = "程序员发现bug时的心路历程"
    
    for style in meme_styles:
        print(f"\n📋 {style} 风格:")
        style_config = style_manager.get_style(style)
        print(f"  名称: {style_config.name}")
        print(f"  描述: {style_config.description}")
        
        # 展示图像生成提示词
        prompt = style_manager.get_image_generation_prompt(style, content)
        print(f"  图像提示词: {prompt[:150]}...")
        
        # 展示HTML参数
        html_params = style_manager.get_html_template_params(style)
        print(f"  主色调: {html_params['colors']['primary_bg']}")
        print(f"  字体: {html_params['typography']['main_font']}")

def demo_parameter_customization():
    """演示参数自定义"""
    print("\n\n⚙️ 参数自定义演示")
    print("="*60)
    
    param_manager = get_param_manager()
    
    # 基础参数
    print("📋 基础Meme参数:")
    base_params = param_manager.prepare_tool_params("generator.ai_image", "meme")
    print(f"  风格: {base_params['style']}")
    print(f"  尺寸: {base_params['width']}x{base_params['height']}")
    print(f"  提示词相关性: {base_params['scale']}")
    
    # 自定义参数
    print("\n🎯 自定义参数覆盖:")
    custom_params = {
        "image_style": "kawaii",
        "image_size": "512x512",
        "image_scale": 4.0
    }
    
    customized_params = param_manager.prepare_tool_params(
        "generator.ai_image", 
        "meme", 
        custom_params
    )
    print(f"  自定义风格: {customized_params['style']}")
    print(f"  自定义尺寸: {customized_params['width']}x{customized_params['height']}")
    print(f"  自定义相关性: {customized_params['scale']}")

def demo_preset_usage():
    """演示预设使用"""
    print("\n\n🚀 完整预设使用演示")
    print("="*60)
    
    # 模拟不同的使用场景
    scenarios = [
        {
            "name": "经典搞笑Meme",
            "content": "当你发现今天是周一",
            "params": {
                "image_style": "humor"
            }
        },
        {
            "name": "可爱治愈Meme", 
            "content": "小猫咪学编程",
            "params": {
                "image_style": "kawaii",
                "html_theme": "bright"
            }
        },
        {
            "name": "传统表情包",
            "content": "程序员的日常",
            "params": {
                "image_style": "meme_classic",
                "image_size": "800x600"
            }
        }
    ]
    
    param_manager = get_param_manager()
    
    for scenario in scenarios:
        print(f"\n📝 场景: {scenario['name']}")
        print(f"  内容: {scenario['content']}")
        print(f"  自定义参数: {scenario['params']}")
        
        # 图像生成参数
        image_params = param_manager.prepare_tool_params(
            "generator.ai_image", "meme", scenario['params']
        )
        print(f"  → 图像参数: style={image_params['style']}, size={image_params['width']}x{image_params['height']}")
        
        # HTML生成参数
        html_params = param_manager.prepare_tool_params(
            "generator.html", "meme", scenario['params']
        )
        if 'style_config' in html_params:
            style_name = html_params['style_config']['style_name']
            bg_color = html_params['style_config']['colors']['primary_bg']
            print(f"  → HTML参数: style={style_name}, bg={bg_color}")

def demo_advanced_customization():
    """演示高级自定义"""
    print("\n\n🎨 高级自定义演示")
    print("="*60)
    
    # 深度自定义示例
    advanced_params = {
        "image_style": "humor",
        "image_size": "1024x1024", 
        "image_scale": 3.5,
        "meme_keywords": ["程序员", "debug", "咖啡", "深夜"],
        "html_theme": "dark",
        "custom_css": {
            "border-radius": "25px",
            "box-shadow": "0 8px 16px rgba(255,107,107,0.3)",
            "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            "backdrop-filter": "blur(10px)"
        }
    }
    
    print("🔧 高级自定义配置:")
    for key, value in advanced_params.items():
        if isinstance(value, dict):
            print(f"  {key}: {len(value)} 项自定义设置")
        elif isinstance(value, list):
            print(f"  {key}: {value}")
        else:
            print(f"  {key}: {value}")
    
    param_manager = get_param_manager()
    
    # 应用到HTML生成
    html_params = param_manager.prepare_tool_params(
        "generator.html", "meme", advanced_params
    )
    
    print("\n✨ 应用后的HTML配置:")
    if 'style_config' in html_params:
        config = html_params['style_config']
        print(f"  风格名称: {config['style_name']}")
        print(f"  主背景色: {config['colors']['primary_bg']}")
        print(f"  边框半径: {config['elements'].get('border-radius', 'default')}")

if __name__ == "__main__":
    demo_style_configurations()
    demo_parameter_customization() 
    demo_preset_usage()
    demo_advanced_customization()
    
    print("\n\n🎉 演示完成！")
    print("💡 这个配置系统让你可以:")
    print("  • 使用预设的专业风格配置")
    print("  • 通过简单参数自定义效果")
    print("  • 深度定制CSS和视觉效果")
    print("  • 保持代码清晰和可维护性")