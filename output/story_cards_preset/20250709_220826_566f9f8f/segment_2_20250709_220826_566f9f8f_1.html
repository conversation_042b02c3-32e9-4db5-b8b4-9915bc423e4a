<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            opacity: 0.8;
        }
        
        .circle-1 {
            width: 80px;
            height: 80px;
            top: 60px;
            right: 50px;
            animation: bounce 4s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 40px;
            height: 40px;
            bottom: 120px;
            left: 60px;
            background: linear-gradient(45deg, #54a0ff, #5f27cd);
            animation: bounce 3s ease-in-out infinite 0.5s;
        }
        
        .triangle {
            position: absolute;
            width: 0;
            height: 0;
            border-left: 30px solid transparent;
            border-right: 30px solid transparent;
            border-bottom: 52px solid #ff9ff3;
            top: 200px;
            left: 80px;
            opacity: 0.7;
            animation: rotate 8s linear infinite;
        }
        
        .content {
            position: relative;
            z-index: 2;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .emoji {
            font-size: 80px;
            margin-bottom: 30px;
            animation: shake 2s ease-in-out infinite;
        }
        
        .main-title {
            font-size: 52px;
            font-weight: 900;
            color: #2c3e50;
            line-height: 1.2;
            margin-bottom: 25px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .highlight-1 {
            color: #ff6b6b;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-stroke: 2px #ff6b6b;
            font-size: 58px;
            font-weight: 900;
        }
        
        .highlight-2 {
            color: #54a0ff;
            background: linear-gradient(45deg, #54a0ff, #5f27cd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 56px;
            font-weight: 900;
        }
        
        .highlight-3 {
            color: #ff9ff3;
            background: linear-gradient(45deg, #ff9ff3, #ee5a6f);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 54px;
            font-weight: 900;
        }
        
        .summary {
            font-size: 28px;
            color: #576574;
            font-weight: 500;
            line-height: 1.4;
            margin-bottom: 30px;
            max-width: 90%;
        }
        
        .subtitle {
            font-size: 18px;
            color: #a4b0be;
            font-weight: 400;
            margin-top: 20px;
        }
        
        .badge {
            position: absolute;
            top: 30px;
            right: 30px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="triangle"></div>
        </div>
        
        <div class="badge">第2段</div>
        
        <div class="content">
            <div class="emoji">💻😱</div>
            
            <h1 class="main-title">
                打开电脑第一件事<br>
                <span class="highlight-1">测试服务器</span>又<span class="highlight-2">挂了</span><br>
                一堆<span class="highlight-3">bug</span>等着修复
            </h1>
            
            <p class="summary">
                每个程序员都懂的痛苦时刻<br>
                昨天的代码今天的噩梦
            </p>
            
            <p class="subtitle">程序员的日常崩溃现场</p>
        </div>
    </div>
</body>
</html>