<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 107, 129, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .content {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 100%;
        }

        .segment-tag {
            background: linear-gradient(45deg, #ff6b81, #ffa726);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 40px;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(255, 107, 129, 0.3);
        }

        .main-title {
            font-size: 48px;
            font-weight: 900;
            line-height: 1.2;
            margin-bottom: 30px;
            color: #2c3e50;
        }

        .highlight-word {
            background: linear-gradient(45deg, #ff6b81, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(255, 107, 129, 0.3);
            position: relative;
        }

        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(45deg, #ff6b81, #ffa726);
            border-radius: 2px;
        }

        .story-content {
            font-size: 24px;
            font-weight: 400;
            line-height: 1.6;
            color: #34495e;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #ff6b81;
        }

        .emoji-decoration {
            font-size: 36px;
            margin: 0 10px;
            display: inline-block;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 107, 129, 0.1);
        }

        .circle:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation: float 6s ease-in-out infinite;
        }

        .circle:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 20%;
            right: 15%;
            animation: float 8s ease-in-out infinite reverse;
        }

        .circle:nth-child(3) {
            width: 40px;
            height: 40px;
            bottom: 15%;
            left: 20%;
            animation: float 7s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            font-weight: 300;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle"></div>
            <div class="circle"></div>
            <div class="circle"></div>
        </div>
        
        <div class="content">
            <div class="segment-tag">第1段</div>
            
            <h1 class="main-title">
                <span class="highlight-word">程序员</span>的<br>
                <span class="highlight-word">起床</span>困难症
            </h1>
            
            <div class="story-content">
                <span class="emoji-decoration">⏰</span>
                早上8点，闹钟响了三遍才起床，匆忙洗漱后发现又要迟到了
                <span class="emoji-decoration">😴</span>
            </div>
            
            <div class="subtitle">
                每个程序员都有的日常 · 你中了几条？
            </div>
        </div>
    </div>
</body>
</html>