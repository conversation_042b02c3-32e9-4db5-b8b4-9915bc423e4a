<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班修Bug的日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 182, 193, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .emoji-decoration {
            position: absolute;
            font-size: 60px;
            opacity: 0.1;
        }
        
        .emoji-1 {
            top: 80px;
            left: 80px;
            animation: bounce 3s ease-in-out infinite;
        }
        
        .emoji-2 {
            bottom: 100px;
            right: 60px;
            animation: bounce 3s ease-in-out infinite 1.5s;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-15px); }
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            color: #2d3748;
            text-align: center;
            line-height: 1.2;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }
        
        .highlight-word {
            background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
            position: relative;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
            border-radius: 2px;
        }
        
        .highlight-bug {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 52px;
            text-shadow: 0 4px 8px rgba(78, 205, 196, 0.3);
            position: relative;
        }
        
        .highlight-bug::before {
            content: '🐛';
            position: absolute;
            top: -10px;
            right: -20px;
            font-size: 24px;
            animation: wiggle 2s ease-in-out infinite;
        }
        
        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-5deg); }
            75% { transform: rotate(5deg); }
        }
        
        .content-text {
            font-size: 28px;
            font-weight: 500;
            color: #4a5568;
            text-align: center;
            line-height: 1.5;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
            max-width: 480px;
        }
        
        .highlight-tired {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            font-size: 32px;
        }
        
        .subtitle {
            font-size: 18px;
            font-weight: 400;
            color: #718096;
            text-align: center;
            position: relative;
            z-index: 2;
            opacity: 0.8;
        }
        
        .geometric-shape {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.1), rgba(78, 205, 196, 0.1));
        }
        
        .shape-1 {
            width: 120px;
            height: 120px;
            top: 20%;
            right: -60px;
            animation: rotate 8s linear infinite;
        }
        
        .shape-2 {
            width: 80px;
            height: 80px;
            bottom: 30%;
            left: -40px;
            animation: rotate 6s linear infinite reverse;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .card-number {
            position: absolute;
            top: 30px;
            right: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 16px;
            font-weight: 700;
            padding: 8px 16px;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-number">第4段</div>
        
        <div class="emoji-decoration emoji-1">💻</div>
        <div class="emoji-decoration emoji-2">😴</div>
        
        <div class="geometric-shape shape-1"></div>
        <div class="geometric-shape shape-2"></div>
        
        <h1 class="main-title">
            终于把<span class="highlight-bug">bug</span>修完了！
        </h1>
        
        <p class="content-text">
            <span class="highlight-word">加班</span>到深夜，<span class="highlight-tired">疲惫</span>回家<br>
            发誓明天一定要早睡
        </p>
        
        <p class="subtitle">
            程序员的日常心声 · 每天都在重复的誓言
        </p>
    </div>
</body>
</html>