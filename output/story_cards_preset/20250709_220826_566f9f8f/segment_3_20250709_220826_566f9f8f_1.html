<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品经理又改需求了</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 107, 107, 0.05) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .emoji-decoration {
            position: absolute;
            font-size: 80px;
            opacity: 0.1;
            animation: bounce 3s ease-in-out infinite;
        }
        
        .emoji-1 {
            top: 80px;
            left: 80px;
            animation-delay: 0s;
        }
        
        .emoji-2 {
            top: 120px;
            right: 60px;
            animation-delay: 1s;
        }
        
        .emoji-3 {
            bottom: 100px;
            left: 60px;
            animation-delay: 2s;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            color: #2d3748;
            text-align: center;
            line-height: 1.2;
            margin-bottom: 40px;
            z-index: 2;
        }
        
        .highlight-word {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
            position: relative;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 3px;
            opacity: 0.6;
        }
        
        .stress-word {
            color: #e74c3c;
            font-size: 52px;
            text-shadow: 2px 2px 4px rgba(231, 76, 60, 0.3);
            animation: pulse 2s infinite;
        }
        
        .content-text {
            font-size: 24px;
            font-weight: 400;
            color: #4a5568;
            text-align: center;
            line-height: 1.6;
            margin-bottom: 50px;
            max-width: 480px;
            z-index: 2;
        }
        
        .story-tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: 700;
            position: absolute;
            top: 40px;
            right: 40px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            z-index: 3;
        }
        
        .bottom-accent {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 120px;
            background: linear-gradient(to top, rgba(255, 107, 107, 0.1), transparent);
            border-radius: 0 0 24px 24px;
        }
        
        .character-icon {
            position: absolute;
            bottom: 40px;
            right: 60px;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            box-shadow: 0 8px 16px rgba(255, 154, 158, 0.4);
            z-index: 3;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="emoji-decoration emoji-1">😰</div>
        <div class="emoji-decoration emoji-2">💻</div>
        <div class="emoji-decoration emoji-3">⏰</div>
        
        <div class="story-tag">第3段</div>
        
        <h1 class="main-title">
            <span class="highlight-word">产品经理</span>又改<span class="stress-word">需求</span><br>
            今天下午就要完成！
        </h1>
        
        <p class="content-text">
            中午吃饭时突然收到变更通知<br>
            小王内心万分崩溃，这熟悉的剧情<br>
            每个程序员都懂的痛😭
        </p>
        
        <div class="bottom-accent"></div>
        <div class="character-icon">🤯</div>
    </div>
</body>
</html>