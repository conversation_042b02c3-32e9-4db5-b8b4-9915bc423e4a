<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事集锦封面</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            overflow: hidden;
            position: relative;
        }
        
        .card-container {
            width: 100%;
            height: 100%;
            padding: 25px;
            position: relative;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            background: #FFFFFF;
        }
        
        .doodle-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .star {
            position: absolute;
            color: #4ECDC4;
            font-size: 20px;
            opacity: 0.6;
        }
        
        .star:nth-child(1) { top: 60px; right: 80px; transform: rotate(15deg); }
        .star:nth-child(2) { bottom: 150px; left: 50px; transform: rotate(-20deg); }
        .star:nth-child(3) { top: 200px; left: 80px; transform: rotate(30deg); }
        
        .circle {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #FF6B6B;
            border-radius: 50%;
            opacity: 0.4;
        }
        
        .circle:nth-child(4) { top: 120px; right: 120px; }
        .circle:nth-child(5) { bottom: 200px; right: 60px; }
        
        .exclamation {
            position: absolute;
            color: #FF6B6B;
            font-size: 24px;
            font-weight: bold;
            opacity: 0.7;
        }
        
        .exclamation:nth-child(6) { top: 300px; right: 100px; transform: rotate(10deg); }
        .exclamation:nth-child(7) { bottom: 100px; left: 100px; transform: rotate(-15deg); }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .title {
            font-size: 28px;
            color: #333333;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            margin-bottom: 40px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .story-icon {
            font-size: 80px;
            color: #4ECDC4;
            margin: 30px 0;
            opacity: 0.8;
        }
        
        .count-badge {
            background: #FF6B6B;
            color: white;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 18px;
            font-weight: 600;
            margin-top: 20px;
            position: relative;
            transform: rotate(-2deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            transform: rotate(-8deg);
            bottom: 80px;
            right: 80px;
            background: #FAFAFA;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #E0E0E0;
        }
        
        .annotation::before {
            content: '→';
            position: absolute;
            top: -10px;
            left: 10px;
            color: #FF6B6B;
            font-size: 16px;
        }
        
        .smiley {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #4ECDC4;
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: #E0E0E0;
            margin: 20px auto;
            position: relative;
            border-radius: 1px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 8px;
            height: 4px;
            background: #FF6B6B;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="doodle-elements">
            <div class="star">★</div>
            <div class="star">✦</div>
            <div class="star">✧</div>
            <div class="circle"></div>
            <div class="circle"></div>
            <div class="exclamation">!</div>
            <div class="exclamation">?</div>
        </div>
        
        <div class="content">
            <div class="title">故事集锦</div>
            <div class="subtitle">精彩片段合集</div>
            
            <div class="story-icon">📖</div>
            
            <div class="divider"></div>
            
            <div class="count-badge">4个精彩片段</div>
            
            <div class="annotation">
                准备好了吗？
            </div>
            
            <div class="smiley">😊</div>
        </div>
    </div>
</body>
</html>