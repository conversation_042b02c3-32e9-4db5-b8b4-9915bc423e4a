<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天 - 第1段</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .segment-title {
            font-size: 20px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 16px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            margin: 20px 0;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px 0;
        }
        
        .main-content {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            text-align: center;
            letter-spacing: 0.02em;
            position: relative;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            border: 1px solid #E0E0E0;
        }
        
        .highlight {
            color: #FF6B6B;
            position: relative;
            font-weight: 700;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
            opacity: 0.6;
        }
        
        .emotion-bubble {
            position: absolute;
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
        }
        
        .emotion-1 {
            top: 20px;
            left: 20px;
            animation-delay: 0s;
        }
        
        .emotion-2 {
            top: 50px;
            right: 30px;
            animation-delay: 1s;
        }
        
        .emotion-3 {
            bottom: 80px;
            left: 30px;
            animation-delay: 2s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }
        
        .doodle {
            position: absolute;
            stroke: #4ECDC4;
            stroke-width: 2;
            fill: none;
            opacity: 0.3;
        }
        
        .doodle-1 {
            top: 100px;
            left: 50px;
            width: 40px;
            height: 40px;
        }
        
        .doodle-2 {
            bottom: 150px;
            right: 40px;
            width: 30px;
            height: 30px;
        }
        
        .time-stamp {
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 12px;
            color: #666666;
            background: rgba(255, 255, 255, 0.8);
            padding: 5px 10px;
            border-radius: 15px;
            border: 1px dashed #E0E0E0;
        }
        
        .divider {
            width: 100%;
            height: 2px;
            background: linear-gradient(to right, transparent, #4ECDC4, transparent);
            margin: 15px 0;
            position: relative;
        }
        
        .divider::before {
            content: '⭐';
            position: absolute;
            left: 50%;
            top: -8px;
            transform: translateX(-50%);
            background: #FFFFFF;
            padding: 0 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第1段</div>
        
        <div class="image-container">
            <img src="https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710205814B1AE86AC32EB7B13D328-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238697&x-signature=W99l47mSppYkM3RH2BZgWdSGPzg%3D" alt="程序员小王的早晨">
            <div class="image-annotation">早晨时光 ☀️</div>
        </div>
        
        <div class="divider"></div>
        
        <div class="content-area">
            <div class="main-content">
                程序员小王的一天：早上8点，<span class="highlight">闹钟响了三遍</span>才起床，匆忙洗漱后发现又要<span class="highlight">迟到了</span>。
            </div>
        </div>
        
        <!-- 手绘装饰元素 -->
        <div class="emotion-bubble emotion-1">😴</div>
        <div class="emotion-bubble emotion-2">⏰</div>
        <div class="emotion-bubble emotion-3">😅</div>
        
        <!-- 手绘涂鸦 -->
        <svg class="doodle doodle-1">
            <circle cx="20" cy="20" r="15" stroke-dasharray="5,5"/>
        </svg>
        
        <svg class="doodle doodle-2">
            <path d="M5,15 Q15,5 25,15 Q15,25 5,15" stroke-dasharray="3,3"/>
        </svg>
        
        <div class="time-stamp">早晨 8:00 📱</div>
    </div>
</body>
</html>