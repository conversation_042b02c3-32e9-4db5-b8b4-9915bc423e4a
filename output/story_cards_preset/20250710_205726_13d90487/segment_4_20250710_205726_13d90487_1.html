<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 加班的小王</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .segment-badge {
            position: absolute;
            top: -8px;
            left: 30px;
            background: #FF6B6B;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 400;
            transform: rotate(-2deg);
            box-shadow: 0 2px 4px rgba(255,107,107,0.3);
        }
        
        .doodle-corner {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: #4ECDC4;
            transform: rotate(15deg);
        }
        
        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px 20px;
        }
        
        .image-container {
            width: 200px;
            height: 200px;
            margin: 0 auto 30px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #4ECDC4;
            position: relative;
            box-shadow: 0 4px 12px rgba(78,205,196,0.2);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .tired-annotation {
            position: absolute;
            top: -15px;
            right: -20px;
            background: #FAFAFA;
            color: #666666;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            transform: rotate(15deg);
            border: 1px dashed #E0E0E0;
        }
        
        .story-text {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            letter-spacing: 0.02em;
            max-width: 480px;
            margin: 0 auto;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(255,107,107,0.2) 0%, rgba(255,107,107,0.1) 100%);
            padding: 2px 4px;
            border-radius: 4px;
            position: relative;
        }
        
        .highlight::after {
            content: "！";
            position: absolute;
            top: -10px;
            right: -8px;
            color: #FF6B6B;
            font-size: 16px;
            font-weight: bold;
        }
        
        .underline {
            text-decoration: underline;
            text-decoration-color: #4ECDC4;
            text-decoration-thickness: 2px;
            text-underline-offset: 3px;
        }
        
        .emotion-bubble {
            position: absolute;
            bottom: 120px;
            left: 50px;
            background: #FAFAFA;
            color: #666666;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px dashed #E0E0E0;
            transform: rotate(-3deg);
        }
        
        .emotion-bubble::before {
            content: "💤";
            margin-right: 5px;
        }
        
        .doodle-stars {
            position: absolute;
            bottom: 100px;
            right: 60px;
            color: #4ECDC4;
            font-size: 20px;
            transform: rotate(25deg);
        }
        
        .promise-highlight {
            margin-top: 20px;
            padding: 15px;
            background: rgba(78,205,196,0.1);
            border-radius: 10px;
            border-left: 4px solid #4ECDC4;
            position: relative;
        }
        
        .promise-highlight::before {
            content: "✋";
            position: absolute;
            top: -10px;
            left: -10px;
            background: #FFFFFF;
            padding: 5px;
            border-radius: 50%;
            border: 2px solid #4ECDC4;
        }
        
        .squiggly-line {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: repeating-linear-gradient(
                90deg,
                #FF6B6B,
                #FF6B6B 5px,
                transparent 5px,
                transparent 10px
            );
            border-radius: 2px;
        }
        
        .corner-doodle {
            position: absolute;
            bottom: 15px;
            right: 15px;
            font-size: 16px;
            color: #4ECDC4;
            transform: rotate(-15deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-badge">第4段</div>
        <div class="doodle-corner">☆</div>
        
        <div class="content-wrapper">
            <div class="image-container">
                <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025071021003269A692418900ED1465DD-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238834&x-signature=P9M7LpMsKCGAjzZ2H9mT%2FI76Ugc%3D" alt="疲惫的小王">
                <div class="tired-annotation">好累啊~</div>
            </div>
            
            <div class="story-text">
                下午<span class="highlight">加班到很晚</span>，终于把bug修完了，小王拖着<span class="underline">疲惫的身体</span>回到家，发誓明天一定要早睡。
                
                <div class="promise-highlight">
                    明天一定要早睡！
                </div>
            </div>
        </div>
        
        <div class="emotion-bubble">又是熬夜的一天</div>
        <div class="doodle-stars">✨ ✨</div>
        <div class="squiggly-line"></div>
        <div class="corner-doodle">( ˘ω˘ )</div>
    </div>
</body>
</html>