<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, transparent 48%, #FF6B6B 49%, #FF6B6B 51%, transparent 52%);
            z-index: -1;
            border-radius: 20px;
        }
        
        .header {
            text-align: center;
            position: relative;
            margin-bottom: 15px;
        }
        
        .segment-title {
            font-size: 28px;
            color: #333333;
            font-weight: 700;
            position: relative;
            display: inline-block;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, #4ECDC4, transparent);
            border-radius: 2px;
        }
        
        .doodle-stars {
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 20px;
            color: #FF6B6B;
            transform: rotate(15deg);
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .image-container {
            width: 100%;
            height: 300px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 2px solid #E0E0E0;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .text-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            background: rgba(250, 250, 250, 0.5);
            padding: 20px;
            border-radius: 12px;
            border: 1px dashed #E0E0E0;
        }
        
        .main-text {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            letter-spacing: 0.02em;
            text-align: center;
            position: relative;
        }
        
        .highlight-word {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
            opacity: 0.6;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            top: 50%;
            right: -40px;
            transform: translateY(-50%);
            opacity: 0.6;
        }
        
        .emotion-icon {
            position: absolute;
            font-size: 24px;
            color: #FF6B6B;
        }
        
        .emotion-1 {
            top: 20px;
            left: 20px;
            transform: rotate(-15deg);
        }
        
        .emotion-2 {
            bottom: 30px;
            right: 30px;
            transform: rotate(20deg);
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent, #4ECDC4, transparent);
            margin: 15px 0;
            border-radius: 1px;
            position: relative;
        }
        
        .divider::before {
            content: '✦';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #FFFFFF;
            padding: 0 8px;
            color: #4ECDC4;
            font-size: 12px;
        }
        
        .footer-doodle {
            position: absolute;
            bottom: 15px;
            left: 15px;
            font-size: 14px;
            color: #666666;
            opacity: 0.7;
            transform: rotate(-5deg);
        }
        
        .corner-decoration {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .corner-decoration.top-left {
            top: 10px;
            left: 10px;
        }
        
        .corner-decoration.bottom-right {
            bottom: 10px;
            right: 10px;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-5px) rotate(2deg); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration bottom-right"></div>
        
        <div class="header">
            <h1 class="segment-title">第2段</h1>
            <div class="doodle-stars floating">✨</div>
        </div>
        
        <div class="content-section">
            <div class="image-container">
                <img src="https://p9-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710205902373E765C49FD8D14E755-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238745&x-signature=5E5NJi2k%2BWwHkJ60LjKSc7gMUFQ%3D" alt="故事配图">
                <div class="image-annotation">职场日常 📱</div>
            </div>
            
            <div class="divider"></div>
            
            <div class="text-content">
                <div class="emphasis-circle"></div>
                <p class="main-text">
                    到公司后，打开电脑第一件事就是查看昨天的代码，结果发现<span class="highlight-word">测试服务器挂了</span>，一堆<span class="highlight-word">bug</span>等着修复。
                </p>
                
                <div class="emotion-icon emotion-1">😱</div>
                <div class="emotion-icon emotion-2">💻</div>
            </div>
        </div>
        
        <div class="footer-doodle">程序员の日常 ✍️</div>
    </div>
</body>
</html>