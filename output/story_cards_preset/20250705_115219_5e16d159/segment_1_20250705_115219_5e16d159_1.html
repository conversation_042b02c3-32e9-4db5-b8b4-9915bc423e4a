<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content {
            text-align: center;
            z-index: 2;
            padding: 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            color: #2d3748;
            margin-bottom: 30px;
            line-height: 1.1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .highlight-word {
            color: #ff6b6b;
            text-shadow: 0 0 20px rgba(255,107,107,0.5);
            position: relative;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120%;
            height: 8px;
            background: linear-gradient(90deg, #ff6b6b, #ffa726);
            border-radius: 4px;
            opacity: 0.8;
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 40px;
            background: rgba(255,255,255,0.8);
            padding: 15px 30px;
            border-radius: 50px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .description {
            font-size: 1.3rem;
            color: #2d3748;
            line-height: 1.6;
            max-width: 80%;
            background: rgba(255,255,255,0.9);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .time-badge {
            position: absolute;
            top: 30px;
            right: 30px;
            background: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1.1rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(255,107,107,0.3);
        }
        
        .alarm-icon {
            position: absolute;
            top: 30px;
            left: 30px;
            font-size: 2.5rem;
            color: #ffa726;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .decorative-circle {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            bottom: 50px;
            left: 50px;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .stress-keyword {
            color: #e53e3e;
            font-weight: 700;
            font-size: 1.1em;
            text-shadow: 1px 1px 2px rgba(229,62,62,0.3);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="time-badge">早上8点</div>
        <div class="alarm-icon">⏰</div>
        
        <div class="content">
            <h1 class="main-title">
                程序员的<span class="highlight-word">赖床</span>日常
            </h1>
            
            <div class="subtitle">
                三次闹钟才起床的真实写照
            </div>
            
            <div class="description">
                闹钟响了三遍才起床，匆忙洗漱后发现<span class="stress-keyword">又要迟到了</span>。
                这就是程序员小王的真实一天开始！
            </div>
        </div>
        
        <div class="decorative-circle"></div>
    </div>
</body>
</html>