<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天 - 第1段</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .card::after {
            content: '✨';
            position: absolute;
            bottom: 15px;
            left: 15px;
            font-size: 20px;
            opacity: 0.4;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
            opacity: 0.6;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 25px;
            position: relative;
            border: 2px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-container::before {
            content: '📸';
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 8px;
            border-radius: 15px;
            font-size: 14px;
            z-index: 1;
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .main-content {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            text-align: center;
            letter-spacing: 0.02em;
            padding: 20px;
            background: #FAFAFA;
            border-radius: 12px;
            border: 1px solid #E0E0E0;
            position: relative;
            margin-bottom: 20px;
        }
        
        .main-content::before {
            content: '💭';
            position: absolute;
            top: -10px;
            left: 20px;
            background: #FFFFFF;
            padding: 5px;
            border-radius: 50%;
            font-size: 16px;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            background-size: 100% 40%;
            background-repeat: no-repeat;
            background-position: 0 80%;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 700;
        }
        
        .annotation {
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%) rotate(15deg);
            font-size: 14px;
            color: #4ECDC4;
            background: #FFFFFF;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px dashed #4ECDC4;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .annotation::before {
            content: '→';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            color: #4ECDC4;
            font-size: 16px;
        }
        
        .emotion-icons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }
        
        .emotion-icon {
            font-size: 24px;
            opacity: 0.6;
            animation: float 3s ease-in-out infinite;
        }
        
        .emotion-icon:nth-child(2) {
            animation-delay: 0.5s;
        }
        
        .emotion-icon:nth-child(3) {
            animation-delay: 1s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .divider {
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #FF6B6B 20%, #4ECDC4 50%, #FF6B6B 80%, transparent 100%);
            margin: 15px 0;
            border-radius: 2px;
            opacity: 0.3;
        }
        
        .footer-note {
            text-align: center;
            font-size: 12px;
            color: #666666;
            margin-top: 10px;
            font-style: italic;
        }
        
        .doodle-1 {
            position: absolute;
            top: 100px;
            left: 10px;
            width: 20px;
            height: 20px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.2;
        }
        
        .doodle-2 {
            position: absolute;
            bottom: 150px;
            right: 20px;
            font-size: 16px;
            color: #FF6B6B;
            opacity: 0.3;
            transform: rotate(-15deg);
        }
        
        .time-emphasis {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
        }
        
        .time-emphasis::after {
            content: '⏰';
            position: absolute;
            top: -5px;
            right: -20px;
            font-size: 12px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="doodle-1"></div>
        <div class="doodle-2">?!</div>
        
        <h1 class="segment-title">第1段</h1>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025071020461706DD037780166712C316-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752237980&x-signature=87K6kCoSzyg5DdnF0HgBMIwzp04%3D" alt="程序员小王的早晨">
        </div>
        
        <div class="content-section">
            <div class="main-content">
                程序员小王的一天：<span class="time-emphasis">早上8点</span>，<span class="highlight">闹钟响了三遍</span>才起床，匆忙洗漱后发现又要迟到了。
                
                <div class="annotation">
                    熟悉的场景！
                </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="emotion-icons">
                <span class="emotion-icon">😴</span>
                <span class="emotion-icon">😱</span>
                <span class="emotion-icon">🏃‍♂️</span>
            </div>
            
            <div class="footer-note">
                每个程序员的日常写照 ~
            </div>
        </div>
    </div>
</body>
</html>