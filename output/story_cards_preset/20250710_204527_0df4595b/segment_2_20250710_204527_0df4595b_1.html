<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card-container {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 25px;
            position: relative;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .main-content {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            margin-bottom: 20px;
            position: relative;
            padding: 15px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            border-left: 3px solid #4ECDC4;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            background-size: 100% 20px;
            background-repeat: no-repeat;
            background-position: 0 bottom;
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .highlight::after {
            content: '!';
            position: absolute;
            right: -15px;
            top: -8px;
            color: #FF6B6B;
            font-size: 14px;
            font-weight: bold;
        }
        
        .emotion-icon {
            display: inline-block;
            margin: 0 5px;
            font-size: 16px;
            animation: bounce 2s infinite;
        }
        
        .doodle-elements {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .doodle-elements::before {
            content: '';
            position: absolute;
            top: -10px;
            right: -10px;
            width: 15px;
            height: 15px;
            background: #FF6B6B;
            border-radius: 50%;
            opacity: 0.6;
        }
        
        .doodle-star {
            position: absolute;
            bottom: 20px;
            right: 30px;
            color: #4ECDC4;
            font-size: 20px;
            opacity: 0.4;
            animation: twinkle 3s infinite;
        }
        
        .hand-drawn-arrow {
            position: absolute;
            bottom: 120px;
            left: 50px;
            width: 60px;
            height: 30px;
            opacity: 0.5;
        }
        
        .hand-drawn-arrow::before {
            content: '→';
            position: absolute;
            color: #FF6B6B;
            font-size: 24px;
            transform: rotate(15deg);
        }
        
        .annotation-bubble {
            position: absolute;
            bottom: 60px;
            right: 80px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 12px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
            max-width: 120px;
        }
        
        .annotation-bubble::before {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid rgba(255, 255, 255, 0.9);
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .wavy-line {
            position: absolute;
            bottom: 150px;
            left: 20px;
            width: 100px;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                #4ECDC4 0px,
                #4ECDC4 5px,
                transparent 5px,
                transparent 10px
            );
            opacity: 0.4;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="doodle-elements"></div>
        
        <div class="segment-title">第2段</div>
        
        <div class="image-container">
            <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507102047143E801C6B35307912DAAB-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238036&x-signature=atnEPKWMiT9p807RDpu1NeIJrz4%3D" alt="工作场景">
            <div class="image-annotation">上班日常</div>
        </div>
        
        <div class="content-section">
            <div class="main-content">
                到公司后，打开电脑第一件事就是查看昨天的代码，结果发现<span class="highlight">测试服务器挂了</span><span class="emotion-icon">😱</span>，一堆bug等着修复<span class="emotion-icon">🐛</span>。
            </div>
        </div>
        
        <div class="hand-drawn-arrow"></div>
        <div class="wavy-line"></div>
        <div class="doodle-star">★</div>
        
        <div class="annotation-bubble">
            程序员的日常惊喜
        </div>
    </div>
</body>
</html>