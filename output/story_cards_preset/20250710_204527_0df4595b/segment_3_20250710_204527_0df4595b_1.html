<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 故事情节卡片</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .card::after {
            content: '✨';
            position: absolute;
            bottom: 15px;
            left: 15px;
            font-size: 20px;
            opacity: 0.4;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px 0;
        }
        
        .main-content {
            font-size: 20px;
            line-height: 1.6;
            color: #333333;
            text-align: center;
            letter-spacing: 0.02em;
            position: relative;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 12px;
            border: 1px dashed #E0E0E0;
        }
        
        .highlight {
            background: linear-gradient(180deg, transparent 60%, #FF6B6B 60%, #FF6B6B 80%, transparent 80%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 700;
        }
        
        .emotion-icon {
            display: inline-block;
            margin: 0 5px;
            font-size: 24px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .annotation-bubble {
            position: absolute;
            bottom: -15px;
            right: 20px;
            background: #4ECDC4;
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 400;
        }
        
        .annotation-bubble::before {
            content: '';
            position: absolute;
            top: -8px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-bottom: 8px solid #4ECDC4;
        }
        
        .doodle-elements {
            position: absolute;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            font-size: 16px;
            color: #FF6B6B;
            opacity: 0.6;
        }
        
        .divider {
            width: 80%;
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                #4ECDC4,
                #4ECDC4 5px,
                transparent 5px,
                transparent 10px
            );
            margin: 15px auto;
            border-radius: 2px;
        }
        
        .footer-note {
            text-align: center;
            font-size: 12px;
            color: #666666;
            margin-top: 10px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第3段</div>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710204802E07828FEE17041122996-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238085&x-signature=4X7ReKZrSshw3e%2BhneMwYbfJhp0%3D" alt="工作场景">
            <div class="image-annotation">📱 午餐时刻</div>
        </div>
        
        <div class="content-area">
            <div class="main-content">
                中午吃饭时，<span class="highlight">产品经理</span>又提出了新的需求变更，要求<span class="highlight">今天下午就要完成</span>，小王内心万分崩溃<span class="emotion-icon">😱</span>
                
                <div class="annotation-bubble">又来了！</div>
            </div>
            
            <div class="divider"></div>
            
            <div class="footer-note">
                程序员的日常 · 需求变更进行时 📝
            </div>
        </div>
        
        <div class="doodle-elements">
            ！？<br>
            💭<br>
            ⚡
        </div>
    </div>
</body>
</html>