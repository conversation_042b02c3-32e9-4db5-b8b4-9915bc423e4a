<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事集锦封面卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: #FAFAFA;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 28px;
            color: #333333;
            margin-bottom: 15px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            margin-bottom: 20px;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .story-icon {
            width: 120px;
            height: 120px;
            margin: 30px 0;
            position: relative;
        }
        
        .story-icon::before {
            content: '📚';
            font-size: 80px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .story-circle {
            position: absolute;
            border: 3px solid #4ECDC4;
            border-radius: 50%;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            border-style: dashed;
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .story-count {
            font-size: 18px;
            color: #333333;
            margin: 20px 0;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .story-count::before {
            content: '✨';
            margin-right: 8px;
        }
        
        .story-count::after {
            content: '✨';
            margin-left: 8px;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .star {
            position: absolute;
            color: #FF6B6B;
            font-size: 16px;
            animation: twinkle 3s ease-in-out infinite;
        }
        
        .star:nth-child(1) { top: 15%; left: 15%; animation-delay: 0s; }
        .star:nth-child(2) { top: 20%; right: 20%; animation-delay: 1s; }
        .star:nth-child(3) { bottom: 25%; left: 25%; animation-delay: 2s; }
        .star:nth-child(4) { bottom: 30%; right: 15%; animation-delay: 1.5s; }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        .exclamation {
            position: absolute;
            color: #4ECDC4;
            font-size: 20px;
            font-weight: bold;
        }
        
        .exclamation:nth-child(5) { top: 35%; left: 8%; transform: rotate(-15deg); }
        .exclamation:nth-child(6) { top: 40%; right: 10%; transform: rotate(15deg); }
        .exclamation:nth-child(7) { bottom: 45%; left: 12%; transform: rotate(-10deg); }
        
        .doodle-line {
            position: absolute;
            width: 60px;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
            opacity: 0.6;
        }
        
        .doodle-line:nth-child(8) { top: 60%; left: 5%; transform: rotate(25deg); }
        .doodle-line:nth-child(9) { top: 65%; right: 8%; transform: rotate(-20deg); }
        .doodle-line:nth-child(10) { bottom: 15%; left: 10%; transform: rotate(30deg); }
        
        .footer {
            text-align: center;
            margin-top: 40px;
        }
        
        .footer-text {
            font-size: 14px;
            color: #666666;
            margin-bottom: 15px;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .smile-face {
            font-size: 24px;
            color: #FF6B6B;
            margin: 0 10px;
            display: inline-block;
            animation: bounce 2s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .hand-drawn-border {
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid #E0E0E0;
            border-radius: 12px;
            pointer-events: none;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="hand-drawn-border"></div>
        
        <div class="decorative-elements">
            <div class="star">⭐</div>
            <div class="star">✨</div>
            <div class="star">⭐</div>
            <div class="star">✨</div>
            <div class="exclamation">!</div>
            <div class="exclamation">?</div>
            <div class="exclamation">!</div>
            <div class="doodle-line"></div>
            <div class="doodle-line"></div>
            <div class="doodle-line"></div>
        </div>
        
        <div class="header">
            <h1 class="title">故事集锦</h1>
            <p class="subtitle">精彩片段合集</p>
        </div>
        
        <div class="content-area">
            <div class="story-icon">
                <div class="story-circle"></div>
            </div>
            
            <div class="story-count">
                共包含 4 个精彩片段
            </div>
        </div>
        
        <div class="footer">
            <p class="footer-text">准备好开启阅读之旅了吗？</p>
            <div>
                <span class="smile-face">😊</span>
                <span class="smile-face">📖</span>
                <span class="smile-face">🎉</span>
            </div>
        </div>
    </div>
</body>
</html>