<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 加班的夜晚</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            overflow: hidden;
            position: relative;
        }
        
        .card-container {
            width: 100%;
            height: 100%;
            padding: 25px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            font-weight: 700;
            letter-spacing: 0.02em;
            position: relative;
            margin-bottom: 10px;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 1px solid #E0E0E0;
            background: #FAFAFA;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px 15px;
            background: #FAFAFA;
            border-radius: 12px;
            border: 1px dashed #4ECDC4;
            position: relative;
        }
        
        .main-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: center;
            position: relative;
            padding: 0 10px;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.2) 100%);
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .highlight::after {
            content: '💤';
            position: absolute;
            right: -25px;
            top: -2px;
            font-size: 14px;
        }
        
        .tired-emphasis {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
        }
        
        .tired-emphasis::before {
            content: '😴';
            position: absolute;
            left: -25px;
            top: 0;
            font-size: 16px;
        }
        
        .decorative-elements {
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 12px;
            color: #4ECDC4;
        }
        
        .decorative-elements::before {
            content: '◦ ◦ ◦';
            letter-spacing: 5px;
        }
        
        .bottom-decoration {
            position: absolute;
            bottom: 15px;
            right: 15px;
            font-size: 14px;
            color: #FF6B6B;
        }
        
        .bottom-decoration::after {
            content: '⭐';
            margin-left: 5px;
        }
        
        .handwritten-note {
            position: absolute;
            bottom: -10px;
            left: 20px;
            background: #FFFFFF;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            color: #666666;
            border: 1px solid #E0E0E0;
            transform: rotate(-2deg);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .handwritten-note::before {
            content: '📝';
            margin-right: 5px;
        }
        
        .corner-doodle {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .corner-doodle::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #4ECDC4;
            border-radius: 50%;
        }
        
        .divider {
            width: 100%;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                #FF6B6B 0px,
                #FF6B6B 5px,
                transparent 5px,
                transparent 10px
            );
            margin: 10px 0;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="corner-doodle"></div>
        
        <div class="segment-title">第4段</div>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/202507102048487D47F6BF75AF4E12AE18-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752238130&x-signature=tRrmJwMXy1ooYhsZZS4u1VMJKNo%3D" alt="加班的夜晚">
            <div class="image-annotation">深夜时分 🌙</div>
        </div>
        
        <div class="divider"></div>
        
        <div class="content-section">
            <div class="decorative-elements"></div>
            
            <div class="main-text">
                下午加班到很晚，终于把bug修完了，<br>
                小王<span class="tired-emphasis">拖着疲惫的身体</span>回到家，<br>
                <span class="highlight">发誓明天一定要早睡</span>。
            </div>
            
            <div class="bottom-decoration">程序员的日常</div>
            
            <div class="handwritten-note">
                又是熟悉的flag！
            </div>
        </div>
    </div>
</body>
</html>