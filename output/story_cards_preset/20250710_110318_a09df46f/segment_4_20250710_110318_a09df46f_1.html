<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: #FAFAFA;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            font-weight: 700;
            letter-spacing: 0.02em;
            position: relative;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #4ECDC4;
            border-radius: 2px;
        }
        
        .doodle-star {
            position: absolute;
            top: -10px;
            right: -15px;
            font-size: 20px;
            color: #4ECDC4;
            transform: rotate(15deg);
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 20px 0;
        }
        
        .main-image {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            object-fit: cover;
            margin-bottom: 30px;
            border: 2px solid #E0E0E0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .story-content {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            max-width: 480px;
            position: relative;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            border: 1px dashed #E0E0E0;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.2) 100%);
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .highlight::after {
            content: '!';
            position: absolute;
            top: -15px;
            right: -10px;
            font-size: 16px;
            color: #FF6B6B;
            font-weight: bold;
        }
        
        .emotion-icon {
            font-size: 24px;
            color: #4ECDC4;
            margin: 0 5px;
            display: inline-block;
            transform: rotate(-5deg);
        }
        
        .doodle-circle {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            top: 20px;
            left: 20px;
            opacity: 0.6;
        }
        
        .doodle-arrow {
            position: absolute;
            bottom: 30px;
            right: 30px;
            width: 40px;
            height: 2px;
            background: #FF6B6B;
            transform: rotate(45deg);
            opacity: 0.7;
        }
        
        .doodle-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 8px solid #FF6B6B;
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid #E0E0E0;
            transform: rotate(-2deg);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .annotation-1 {
            top: 120px;
            left: 50px;
        }
        
        .annotation-2 {
            bottom: 150px;
            right: 40px;
            transform: rotate(3deg);
        }
        
        .wavy-line {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: repeating-linear-gradient(
                90deg,
                #4ECDC4 0px,
                #4ECDC4 10px,
                transparent 10px,
                transparent 20px
            );
            opacity: 0.6;
        }
        
        .speech-bubble {
            position: absolute;
            top: 180px;
            right: 80px;
            background: #FFFFFF;
            border: 2px solid #FF6B6B;
            border-radius: 15px;
            padding: 8px 12px;
            font-size: 12px;
            color: #333333;
            transform: rotate(-8deg);
        }
        
        .speech-bubble::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid #FF6B6B;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="doodle-circle"></div>
        <div class="doodle-star">✦</div>
        
        <div class="header">
            <h1 class="segment-title">第4段</h1>
        </div>
        
        <div class="content-area">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710110625D5E4E44322094DD54F98-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752203188&x-signature=9OuP7IOiRpaBQK6Gb51m5GtQT9A%3D" alt="故事配图" class="main-image">
            
            <div class="story-content">
                下午加班到很晚，终于把<span class="highlight">bug修完了</span><span class="emotion-icon">😤</span>，小王拖着疲惫的身体回到家，发誓明天一定要<span class="highlight">早睡</span><span class="emotion-icon">💤</span>。
            </div>
        </div>
        
        <div class="annotation annotation-1">又是熬夜的一天</div>
        <div class="annotation annotation-2">明日复明日？</div>
        <div class="speech-bubble">程序员日常</div>
        
        <div class="doodle-arrow"></div>
        <div class="wavy-line"></div>
    </div>
</body>
</html>