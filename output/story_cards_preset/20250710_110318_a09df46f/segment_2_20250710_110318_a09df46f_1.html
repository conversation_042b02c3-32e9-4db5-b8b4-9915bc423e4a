<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            width: 600px;
            height: 800px;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: 0.02em;
            position: relative;
            overflow: hidden;
        }
        
        .card {
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            width: 550px;
            height: 750px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            font-weight: 700;
        }
        
        .segment-title::after {
            content: "✨";
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            color: #4ECDC4;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 25px;
        }
        
        .image-container {
            width: 280px;
            height: 200px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 2px solid #E0E0E0;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: -15px;
            right: -20px;
            background: #FFFFFF;
            color: #666666;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px dashed #4ECDC4;
            transform: rotate(8deg);
        }
        
        .text-content {
            background: #FAFAFA;
            padding: 20px;
            border-radius: 12px;
            border: 1px dashed #E0E0E0;
            position: relative;
            line-height: 1.6;
            font-size: 18px;
            color: #333333;
            text-align: center;
            max-width: 450px;
        }
        
        .text-content::before {
            content: "😅";
            position: absolute;
            top: -10px;
            left: -10px;
            font-size: 24px;
            background: #FFFFFF;
            border-radius: 50%;
            padding: 5px;
        }
        
        .highlight {
            color: #FF6B6B;
            position: relative;
            font-weight: 600;
        }
        
        .highlight::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 80px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            right: -40px;
            top: 50%;
            transform: translateY(-50%) rotate(-15deg);
            opacity: 0.7;
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-star {
            position: absolute;
            color: #4ECDC4;
            font-size: 16px;
            opacity: 0.6;
        }
        
        .star-1 { top: 15%; left: 8%; transform: rotate(-20deg); }
        .star-2 { top: 25%; right: 12%; transform: rotate(30deg); }
        .star-3 { bottom: 20%; left: 15%; transform: rotate(-45deg); }
        
        .exclamation {
            position: absolute;
            color: #FF6B6B;
            font-size: 20px;
            font-weight: bold;
            opacity: 0.8;
            bottom: 15%;
            right: 10%;
            transform: rotate(15deg);
        }
        
        .mood-indicator {
            position: absolute;
            bottom: 10px;
            right: 20px;
            font-size: 14px;
            color: #666666;
            font-style: italic;
        }
        
        .hand-drawn-line {
            position: absolute;
            width: 60px;
            height: 2px;
            background: #4ECDC4;
            border-radius: 1px;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%) rotate(-2deg);
            opacity: 0.5;
        }
        
        .speech-bubble {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #FFFFFF;
            border: 1px solid #E0E0E0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            color: #666666;
            transform: rotate(-5deg);
        }
        
        .speech-bubble::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 15px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #FFFFFF;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第2段</div>
        
        <div class="content-area">
            <div class="image-container">
                <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/2025071011045352840B736C2ADED14442-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752203096&x-signature=%2FZcusUzJEsTxaVW7kkmmAN%2Bo78Y%3D" alt="工作场景">
                <div class="image-annotation">现场直击！</div>
            </div>
            
            <div class="text-content">
                到公司后，打开电脑第一件事就是查看昨天的代码，结果发现<span class="highlight">测试服务器挂了</span>，一堆bug等着修复。
                <div class="emphasis-circle"></div>
            </div>
        </div>
        
        <div class="doodle-elements">
            <div class="doodle-star star-1">★</div>
            <div class="doodle-star star-2">☆</div>
            <div class="doodle-star star-3">✦</div>
            <div class="exclamation">!</div>
        </div>
        
        <div class="speech-bubble">程序员的日常</div>
        <div class="hand-drawn-line"></div>
        <div class="mood-indicator">心情指数: 😰</div>
    </div>
</body>
</html>