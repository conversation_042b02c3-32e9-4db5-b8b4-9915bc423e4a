<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 故事情节卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: #FAFAFA;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .card::after {
            content: '★';
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #FF6B6B;
            font-size: 20px;
            opacity: 0.4;
            transform: rotate(-15deg);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }
        
        .segment-title {
            font-size: 28px;
            color: #333333;
            margin-bottom: 15px;
            position: relative;
            display: inline-block;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
        }
        
        .main-image {
            width: 280px;
            height: 200px;
            border-radius: 12px;
            object-fit: cover;
            margin-bottom: 35px;
            border: 2px solid #E0E0E0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .story-text {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            letter-spacing: 0.02em;
            max-width: 480px;
            position: relative;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            border: 1px dashed #E0E0E0;
        }
        
        .highlight {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #FF6B6B;
            opacity: 0.6;
            border-radius: 1px;
        }
        
        .emotion-bubble {
            position: absolute;
            top: -15px;
            right: -10px;
            background: #4ECDC4;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 700;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .emotion-bubble::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 15px;
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #4ECDC4;
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            font-style: italic;
            transform: rotate(-5deg);
        }
        
        .annotation-1 {
            top: 50px;
            left: 30px;
        }
        
        .annotation-2 {
            bottom: 150px;
            right: 40px;
            transform: rotate(8deg);
        }
        
        .doodle {
            position: absolute;
            opacity: 0.3;
        }
        
        .doodle-1 {
            top: 100px;
            right: 50px;
            width: 40px;
            height: 40px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            border-style: dashed;
        }
        
        .doodle-2 {
            bottom: 80px;
            left: 80px;
            font-size: 24px;
            color: #FF6B6B;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            position: relative;
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: linear-gradient(to right, transparent, #E0E0E0, transparent);
            margin: 0 auto 20px;
            position: relative;
        }
        
        .divider::before {
            content: '~';
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            color: #4ECDC4;
            font-size: 16px;
        }
        
        .card-type {
            font-size: 12px;
            color: #666666;
            letter-spacing: 0.05em;
            opacity: 0.8;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <h1 class="segment-title">第3段</h1>
        </div>
        
        <div class="content-area">
            <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710110541D4E9545458A4CEDC579E-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752203144&x-signature=rBVL%2F1H05dU9PbT7lZPjPmPHS%2BI%3D" alt="故事情节" class="main-image floating">
            
            <div class="story-text">
                中午吃饭时，<span class="highlight">产品经理又提出了新的需求变更</span>，要求今天下午就要完成，<span class="highlight">小王内心万分崩溃</span>。
                <div class="emotion-bubble">崩溃！</div>
            </div>
            
            <div class="annotation annotation-1">又来了... (╯°□°）╯</div>
            <div class="annotation annotation-2">打工人的日常</div>
            
            <div class="doodle doodle-1"></div>
            <div class="doodle doodle-2">？！</div>
        </div>
        
        <div class="footer">
            <div class="divider"></div>
            <p class="card-type">日常生活故事情节展示卡片</p>
        </div>
    </div>
</body>
</html>