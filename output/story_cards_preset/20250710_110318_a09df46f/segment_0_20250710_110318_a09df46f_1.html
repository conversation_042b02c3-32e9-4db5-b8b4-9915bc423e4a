<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事集锦封面卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: #FAFAFA;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            box-sizing: border-box;
        }
        
        .doodle-corner {
            position: absolute;
            font-size: 20px;
            color: #FF6B6B;
            transform: rotate(-15deg);
        }
        
        .doodle-corner.top-left {
            top: 15px;
            left: 15px;
        }
        
        .doodle-corner.top-right {
            top: 15px;
            right: 15px;
            transform: rotate(15deg);
        }
        
        .doodle-corner.bottom-left {
            bottom: 15px;
            left: 15px;
            transform: rotate(15deg);
        }
        
        .doodle-corner.bottom-right {
            bottom: 15px;
            right: 15px;
            transform: rotate(-15deg);
        }
        
        .main-title {
            font-size: 28px;
            color: #333333;
            margin-top: 60px;
            margin-bottom: 30px;
            position: relative;
            font-weight: 700;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: #4ECDC4;
            border-radius: 1px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            margin-bottom: 40px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .story-icon {
            width: 120px;
            height: 120px;
            background: #FAFAFA;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }
        
        .story-icon::before {
            content: '📚';
            font-size: 48px;
        }
        
        .story-icon::after {
            content: '✨';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 24px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
        
        .content-text {
            font-size: 18px;
            color: #333333;
            margin: 30px 0;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .highlight-circle {
            display: inline-block;
            background: #FF6B6B;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 700;
            margin: 0 5px;
            position: relative;
            transform: rotate(-2deg);
        }
        
        .annotation {
            font-size: 14px;
            color: #666666;
            margin-top: 40px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .annotation::before {
            content: '→';
            position: absolute;
            left: -25px;
            top: 50%;
            transform: translateY(-50%) rotate(-10deg);
            color: #4ECDC4;
            font-size: 16px;
        }
        
        .emoji-scatter {
            position: absolute;
            font-size: 16px;
            color: #FF6B6B;
            animation: float 3s ease-in-out infinite;
        }
        
        .emoji-scatter:nth-child(1) {
            top: 150px;
            left: 80px;
            animation-delay: 0s;
        }
        
        .emoji-scatter:nth-child(2) {
            top: 200px;
            right: 60px;
            animation-delay: 1s;
        }
        
        .emoji-scatter:nth-child(3) {
            bottom: 200px;
            left: 60px;
            animation-delay: 2s;
        }
        
        .emoji-scatter:nth-child(4) {
            bottom: 150px;
            right: 80px;
            animation-delay: 0.5s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: none;
            border: none;
            position: relative;
            margin: 30px 0;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: repeating-linear-gradient(
                to right,
                #4ECDC4 0px,
                #4ECDC4 10px,
                transparent 10px,
                transparent 20px
            );
        }
        
        .bottom-note {
            font-size: 12px;
            color: #666666;
            margin-top: auto;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .bottom-note::before {
            content: '♪';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            color: #FF6B6B;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="doodle-corner top-left">✦</div>
        <div class="doodle-corner top-right">♪</div>
        <div class="doodle-corner bottom-left">◇</div>
        <div class="doodle-corner bottom-right">✧</div>
        
        <div class="emoji-scatter">!</div>
        <div class="emoji-scatter">?</div>
        <div class="emoji-scatter">☺</div>
        <div class="emoji-scatter">♡</div>
        
        <h1 class="main-title">故事集锦</h1>
        <p class="subtitle">生活中的精彩瞬间</p>
        
        <div class="story-icon"></div>
        
        <p class="content-text">
            这里收录了<span class="highlight-circle">4个</span>精彩片段
        </p>
        
        <div class="divider"></div>
        
        <p class="annotation">每一个故事都值得细细品味</p>
        
        <p class="bottom-note">让我们一起走进这些温暖的时光</p>
    </div>
</body>
</html>