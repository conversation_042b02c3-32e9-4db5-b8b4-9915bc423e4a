<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天 - 第1段</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-radius: 20px;
            z-index: -1;
            opacity: 0.1;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            margin: 20px 0;
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 20px 0;
        }
        
        .main-content {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: center;
            position: relative;
            background: rgba(250, 250, 250, 0.5);
            padding: 25px;
            border-radius: 12px;
            border: 1px dashed #E0E0E0;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            background-size: 100% 20px;
            background-repeat: no-repeat;
            background-position: 0 bottom;
            padding: 2px 0;
            position: relative;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            top: -20px;
            right: -20px;
            opacity: 0.6;
        }
        
        .doodle-element {
            position: absolute;
            font-size: 24px;
            color: #FF6B6B;
            opacity: 0.7;
        }
        
        .doodle-1 {
            top: 15%;
            left: 8%;
            transform: rotate(-15deg);
        }
        
        .doodle-2 {
            top: 25%;
            right: 10%;
            transform: rotate(20deg);
        }
        
        .doodle-3 {
            bottom: 20%;
            left: 12%;
            transform: rotate(-10deg);
        }
        
        .emotion-note {
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 14px;
            color: #666666;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 15px;
            border: 1px dashed #4ECDC4;
            transform: rotate(-2deg);
        }
        
        .divider {
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #E0E0E0, transparent);
            margin: 15px 0;
            position: relative;
        }
        
        .divider::before {
            content: '◦';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #FFFFFF;
            color: #4ECDC4;
            padding: 0 8px;
            font-size: 16px;
        }
        
        .story-mood {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 32px;
            opacity: 0.6;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第1段</div>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710110407BC01A4E9EFCE69DA08E4-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752203050&x-signature=Y6dilhJBmXn42CKRY1uQCj4jm20%3D" alt="程序员小王的早晨">
            <div class="image-annotation">早晨时光 ⏰</div>
        </div>
        
        <div class="divider"></div>
        
        <div class="content-area">
            <div class="main-content">
                <div class="emphasis-circle"></div>
                程序员小王的一天：早上8点，<span class="highlight">闹钟响了三遍才起床</span>，匆忙洗漱后发现又要迟到了。
            </div>
        </div>
        
        <div class="doodle-element doodle-1 floating">😴</div>
        <div class="doodle-element doodle-2">⏰</div>
        <div class="doodle-element doodle-3">💨</div>
        
        <div class="emotion-note">又是熟悉的早晨 😅</div>
        
        <div class="story-mood">🏃‍♂️</div>
    </div>
</body>
</html>