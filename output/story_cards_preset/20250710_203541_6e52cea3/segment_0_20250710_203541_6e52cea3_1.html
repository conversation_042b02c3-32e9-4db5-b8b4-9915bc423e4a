<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事集锦封面卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: #FAFAFA;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .story-card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            overflow: hidden;
        }
        
        .doodle-corner {
            position: absolute;
            font-size: 24px;
            color: #4ECDC4;
            opacity: 0.6;
        }
        
        .doodle-corner.top-left {
            top: 15px;
            left: 15px;
        }
        
        .doodle-corner.top-right {
            top: 15px;
            right: 15px;
        }
        
        .doodle-corner.bottom-left {
            bottom: 15px;
            left: 15px;
        }
        
        .doodle-corner.bottom-right {
            bottom: 15px;
            right: 15px;
        }
        
        .main-title {
            font-size: 28px;
            color: #333333;
            margin-bottom: 30px;
            position: relative;
            font-weight: 700;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .title-underline {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
            opacity: 0.7;
        }
        
        .story-icon {
            width: 150px;
            height: 150px;
            background: #FAFAFA;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
            position: relative;
        }
        
        .story-icon::before {
            content: "📚";
            font-size: 60px;
        }
        
        .story-icon::after {
            content: "✨";
            position: absolute;
            top: -10px;
            right: 10px;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        .content-text {
            font-size: 20px;
            color: #666666;
            margin-bottom: 40px;
            line-height: 1.6;
            letter-spacing: 0.02em;
            position: relative;
        }
        
        .highlight-circle {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #FF6B6B;
            border-radius: 50%;
            top: -5px;
            left: 50px;
            opacity: 0.6;
        }
        
        .story-count {
            font-size: 18px;
            color: #333333;
            background: #FAFAFA;
            padding: 15px 30px;
            border: 1px solid #E0E0E0;
            border-radius: 25px;
            position: relative;
            margin-bottom: 30px;
        }
        
        .story-count::before {
            content: "→";
            position: absolute;
            left: -25px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: #4ECDC4;
        }
        
        .fun-elements {
            position: absolute;
            font-size: 16px;
            color: #FF6B6B;
            opacity: 0.7;
        }
        
        .element-1 {
            top: 120px;
            left: 80px;
        }
        
        .element-2 {
            top: 180px;
            right: 90px;
        }
        
        .element-3 {
            bottom: 200px;
            left: 60px;
        }
        
        .element-4 {
            bottom: 150px;
            right: 70px;
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: linear-gradient(to right, transparent, #4ECDC4, transparent);
            margin: 20px 0;
            position: relative;
        }
        
        .divider::before {
            content: "✦";
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: #FFFFFF;
            padding: 0 10px;
            color: #4ECDC4;
            font-size: 14px;
        }
        
        .annotation {
            font-size: 14px;
            color: #666666;
            background: #FAFAFA;
            padding: 8px 15px;
            border-left: 3px solid #FF6B6B;
            border-radius: 0 8px 8px 0;
            position: relative;
            margin-top: 20px;
            transform: rotate(-1deg);
        }
        
        .annotation::before {
            content: "！";
            position: absolute;
            left: -15px;
            top: -5px;
            color: #FF6B6B;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="story-card">
        <div class="doodle-corner top-left">◦</div>
        <div class="doodle-corner top-right">✧</div>
        <div class="doodle-corner bottom-left">◈</div>
        <div class="doodle-corner bottom-right">◯</div>
        
        <div class="fun-elements element-1">？</div>
        <div class="fun-elements element-2">！</div>
        <div class="fun-elements element-3">☺</div>
        <div class="fun-elements element-4">◊</div>
        
        <h1 class="main-title">
            故事集锦
            <div class="title-underline"></div>
        </h1>
        
        <div class="story-icon">
            <div class="highlight-circle"></div>
        </div>
        
        <div class="content-text">
            精彩片段 · 温暖回忆
        </div>
        
        <div class="divider"></div>
        
        <div class="story-count">
            共包含 4 个精彩片段
        </div>
        
        <div class="annotation">
            每个故事都值得细细品味 ♡
        </div>
    </div>
</body>
</html>