<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 故事情节卡片</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .story-card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            font-weight: 700;
            letter-spacing: 0.02em;
            position: relative;
            display: inline-block;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #4ECDC4;
            border-radius: 2px;
        }
        
        .doodle-star {
            position: absolute;
            top: -10px;
            right: -15px;
            font-size: 20px;
            color: #4ECDC4;
            transform: rotate(15deg);
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
        }
        
        .main-image {
            width: 280px;
            height: 200px;
            object-fit: cover;
            border-radius: 12px;
            border: 2px solid #E0E0E0;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .story-text {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            letter-spacing: 0.02em;
            max-width: 480px;
            position: relative;
            background: #FAFAFA;
            padding: 20px;
            border-radius: 12px;
            border: 1px dashed #E0E0E0;
        }
        
        .highlight {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
            display: inline-block;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #FF6B6B;
            border-radius: 1px;
            opacity: 0.6;
        }
        
        .emotion-icon {
            position: absolute;
            font-size: 24px;
            color: #FF6B6B;
        }
        
        .emotion-1 {
            top: -15px;
            right: -20px;
            transform: rotate(-15deg);
        }
        
        .emotion-2 {
            bottom: -15px;
            left: -20px;
            transform: rotate(10deg);
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: #FFFFFF;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid #E0E0E0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transform: rotate(-3deg);
        }
        
        .annotation-1 {
            top: 50px;
            left: -30px;
        }
        
        .annotation-2 {
            bottom: 80px;
            right: -40px;
            transform: rotate(5deg);
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-circle {
            position: absolute;
            width: 40px;
            height: 40px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .circle-1 {
            top: 100px;
            left: 50px;
            transform: rotate(15deg);
        }
        
        .circle-2 {
            bottom: 150px;
            right: 60px;
            transform: rotate(-20deg);
        }
        
        .hand-drawn-arrow {
            position: absolute;
            bottom: 200px;
            left: 80px;
            width: 60px;
            height: 2px;
            background: #4ECDC4;
            transform: rotate(25deg);
            opacity: 0.6;
        }
        
        .hand-drawn-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #4ECDC4;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        
        .footer-doodle {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #666666;
            position: relative;
        }
        
        .footer-doodle::before {
            content: '~ ~ ~ ~ ~';
            display: block;
            margin-bottom: 10px;
            color: #4ECDC4;
            letter-spacing: 8px;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
        
        .speech-bubble {
            position: absolute;
            top: 120px;
            right: 20px;
            background: #FFFFFF;
            border: 2px solid #FF6B6B;
            border-radius: 15px;
            padding: 8px 12px;
            font-size: 14px;
            color: #333333;
            transform: rotate(-5deg);
        }
        
        .speech-bubble::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #FF6B6B;
        }
    </style>
</head>
<body>
    <div class="story-card">
        <div class="card-header">
            <h1 class="segment-title">第3段</h1>
            <div class="doodle-star">★</div>
        </div>
        
        <div class="content-section">
            <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710203810A7DA3EC0D642B2104E5A-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752237492&x-signature=JI74fz8R0wPo81AmwMJgQaacl%2FI%3D" alt="故事场景" class="main-image">
            
            <div class="story-text">
                <span class="highlight">中午吃饭时</span>，产品经理又提出了新的需求变更，要求<span class="highlight">今天下午就要完成</span>，小王内心万分崩溃。
                
                <div class="emotion-icon emotion-1">😱</div>
                <div class="emotion-icon emotion-2">💔</div>
            </div>
            
            <div class="annotation annotation-1">又来了！</div>
            <div class="annotation annotation-2 floating">心态崩了</div>
            <div class="speech-bubble">程序员的日常</div>
        </div>
        
        <div class="doodle-elements">
            <div class="doodle-circle circle-1"></div>
            <div class="doodle-circle circle-2"></div>
            <div class="hand-drawn-arrow"></div>
        </div>
        
        <div class="footer-doodle">
            生活就是这样充满意外 ✨
        </div>
    </div>
</body>
</html>