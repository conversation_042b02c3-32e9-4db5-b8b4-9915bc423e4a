<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 工作日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: "✨";
            position: absolute;
            right: -40px;
            top: -5px;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
        }
        
        .main-content {
            font-size: 20px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            margin-bottom: 40px;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.3em;
            background-position: 0 88%;
            padding: 2px 4px;
            border-radius: 3px;
            color: #333333;
            font-weight: 700;
        }
        
        .image-container {
            width: 300px;
            height: 200px;
            margin: 20px auto;
            border: 2px solid #4ECDC4;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .doodle-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        
        .doodle-circle {
            position: absolute;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            top: 100px;
            left: 50px;
            transform: rotate(-15deg);
        }
        
        .doodle-star {
            position: absolute;
            color: #FF6B6B;
            font-size: 24px;
            top: 200px;
            right: 80px;
            transform: rotate(20deg);
        }
        
        .doodle-arrow {
            position: absolute;
            bottom: 150px;
            left: 100px;
            width: 60px;
            height: 2px;
            background: #4ECDC4;
            transform: rotate(-30deg);
        }
        
        .doodle-arrow::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -4px;
            width: 0;
            height: 0;
            border-left: 10px solid #4ECDC4;
            border-top: 5px solid transparent;
            border-bottom: 5px solid transparent;
        }
        
        .emotion-icon {
            position: absolute;
            font-size: 24px;
            color: #FF6B6B;
            animation: bounce 2s ease-in-out infinite;
        }
        
        .emotion-1 {
            top: 120px;
            right: 100px;
        }
        
        .emotion-2 {
            bottom: 200px;
            left: 80px;
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: #FAFAFA;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px dashed #E0E0E0;
            transform: rotate(-5deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .annotation-1 {
            top: 80px;
            right: 20px;
        }
        
        .annotation-2 {
            bottom: 120px;
            left: 20px;
            transform: rotate(8deg);
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: #E0E0E0;
            margin: 20px auto;
            position: relative;
            border-radius: 1px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 8px;
            height: 8px;
            background: #FF6B6B;
            border-radius: 50%;
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        .footer-note {
            font-size: 12px;
            color: #666666;
            text-align: center;
            margin-top: 20px;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第2段</div>
        
        <div class="content-area">
            <div class="main-content">
                到公司后，打开电脑第一件事就是<span class="highlight">查看昨天的代码</span>，结果发现<span class="highlight">测试服务器挂了</span>，一堆bug等着修复。
            </div>
            
            <div class="divider"></div>
            
            <div class="image-container">
                <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710203721BD07B3D945D42D112D1F-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752237443&x-signature=ch0JzS3BQoILux%2BK8bYVQu%2B8LTg%3D" alt="工作场景">
            </div>
            
            <div class="footer-note">
                程序员的日常：永远有意外在等着你 😅
            </div>
        </div>
        
        <div class="doodle-elements">
            <div class="doodle-circle"></div>
            <div class="doodle-star">★</div>
            <div class="doodle-arrow"></div>
            <div class="emotion-icon emotion-1">😱</div>
            <div class="emotion-icon emotion-2">💻</div>
            <div class="annotation annotation-1">又是新的一天！</div>
            <div class="annotation annotation-2">bug修复中...</div>
        </div>
    </div>
</body>
</html>