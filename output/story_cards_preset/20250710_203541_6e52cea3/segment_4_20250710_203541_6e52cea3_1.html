<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
            letter-spacing: 0.02em;
        }
        
        .story-card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
            display: inline-block;
            margin-bottom: 15px;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #4ECDC4;
            border-radius: 2px;
        }
        
        .doodle-stars {
            position: absolute;
            top: -10px;
            right: 20px;
            font-size: 20px;
            color: #4ECDC4;
            transform: rotate(15deg);
        }
        
        .content-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            border: 2px solid #E0E0E0;
        }
        
        .story-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #FF6B6B;
        }
        
        .story-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .main-text {
            font-size: 18px;
            color: #333333;
            text-align: center;
            line-height: 1.8;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 12px;
            border: 1px solid #E0E0E0;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, #4ECDC4 0%, #4ECDC4 100%, transparent 100%);
            background-size: 100% 3px;
            background-repeat: no-repeat;
            background-position: 0 100%;
            padding: 2px 4px;
            border-radius: 3px;
            color: #333333;
            font-weight: 600;
        }
        
        .emotion-bubble {
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            background: #FF6B6B;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }
        
        .doodle-elements {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .doodle-circle {
            width: 25px;
            height: 25px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            position: relative;
        }
        
        .doodle-arrow {
            font-size: 16px;
            color: #FF6B6B;
            transform: rotate(-15deg);
        }
        
        .corner-doodle {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .corner-doodle::after {
            content: '';
            position: absolute;
            top: -5px;
            right: -5px;
            width: 8px;
            height: 8px;
            background: #FF6B6B;
            border-radius: 50%;
        }
        
        .bottom-decoration {
            position: absolute;
            bottom: 15px;
            right: 25px;
            display: flex;
            gap: 8px;
        }
        
        .mini-star {
            width: 12px;
            height: 12px;
            background: #4ECDC4;
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
            opacity: 0.6;
        }
        
        .thought-bubble {
            position: absolute;
            top: 60px;
            right: 30px;
            font-size: 14px;
            color: #666666;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 15px;
            border: 1px dashed #E0E0E0;
            transform: rotate(-3deg);
        }
        
        .zigzag-line {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                #4ECDC4,
                #4ECDC4 8px,
                transparent 8px,
                transparent 16px
            );
            opacity: 0.4;
        }
    </style>
</head>
<body>
    <div class="story-card">
        <div class="corner-doodle"></div>
        
        <div class="card-header">
            <h1 class="segment-title">第4段</h1>
            <div class="doodle-stars">✨</div>
        </div>
        
        <div class="content-section">
            <div class="image-container">
                <img src="https://p26-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710203858BED661CADE9FBA11582F-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752237540&x-signature=nyarUo8pTMjjhMFbY%2BhmFKumUPc%3D" alt="故事配图" class="story-image">
                <div class="image-annotation">深夜加班 📱</div>
            </div>
            
            <div class="story-content">
                <div class="main-text">
                    下午<span class="highlight">加班到很晚</span>，终于把bug修完了，小王拖着疲惫的身体回到家，发誓明天一定要<span class="highlight">早睡</span>。
                    <div class="emotion-bubble">😴</div>
                </div>
                
                <div class="thought-bubble">又是熟悉的flag...</div>
            </div>
        </div>
        
        <div class="zigzag-line"></div>
        
        <div class="doodle-elements">
            <div class="doodle-circle"></div>
            <div class="doodle-arrow">→</div>
            <span style="font-size: 14px; color: #666666;">程序员的日常</span>
        </div>
        
        <div class="bottom-decoration">
            <div class="mini-star"></div>
            <div class="mini-star"></div>
            <div class="mini-star"></div>
        </div>
    </div>
</body>
</html>