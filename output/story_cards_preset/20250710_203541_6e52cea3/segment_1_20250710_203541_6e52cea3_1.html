<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天 - 第1段</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .segment-badge {
            position: absolute;
            top: -5px;
            right: 20px;
            background: #FF6B6B;
            color: white;
            padding: 5px 15px;
            border-radius: 0 0 10px 10px;
            font-size: 12px;
            font-weight: 400;
            transform: rotate(2deg);
        }
        
        .doodle-corner {
            position: absolute;
            top: 15px;
            left: 15px;
            font-size: 20px;
            color: #4ECDC4;
            transform: rotate(-15deg);
        }
        
        .title-area {
            text-align: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .main-title {
            font-size: 28px;
            color: #333333;
            margin-bottom: 10px;
            position: relative;
            display: inline-block;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            margin-bottom: 20px;
        }
        
        .image-container {
            text-align: center;
            margin: 25px 0;
            position: relative;
        }
        
        .main-image {
            width: 280px;
            height: 200px;
            object-fit: cover;
            border-radius: 12px;
            border: 2px solid #E0E0E0;
            transform: rotate(-1deg);
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
        }
        
        .image-annotation {
            position: absolute;
            bottom: -15px;
            right: 10px;
            background: #FAFAFA;
            color: #666666;
            padding: 3px 8px;
            border-radius: 5px;
            font-size: 12px;
            border: 1px dashed #4ECDC4;
            transform: rotate(3deg);
        }
        
        .content-area {
            margin: 30px 0;
            line-height: 1.6;
            letter-spacing: 0.02em;
        }
        
        .story-text {
            font-size: 18px;
            color: #333333;
            background: #FAFAFA;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4ECDC4;
            position: relative;
            margin-bottom: 20px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.2em;
            background-position: 0 88%;
            padding: 2px 0;
        }
        
        .emotion-bubble {
            position: absolute;
            right: -10px;
            top: 10px;
            background: #FF6B6B;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transform: rotate(15deg);
        }
        
        .doodle-elements {
            position: absolute;
            bottom: 20px;
            left: 20px;
            font-size: 14px;
            color: #4ECDC4;
        }
        
        .star-doodle {
            position: absolute;
            top: 120px;
            right: 30px;
            color: #FF6B6B;
            font-size: 18px;
            transform: rotate(25deg);
        }
        
        .circle-doodle {
            position: absolute;
            bottom: 100px;
            left: 30px;
            width: 25px;
            height: 25px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            transform: rotate(-10deg);
        }
        
        .squiggle-line {
            position: absolute;
            top: 200px;
            left: 50px;
            width: 50px;
            height: 2px;
            background: #FF6B6B;
            border-radius: 2px;
            transform: rotate(10deg);
        }
        
        .mood-indicator {
            position: absolute;
            bottom: 50px;
            right: 50px;
            font-size: 24px;
            color: #FF6B6B;
            transform: rotate(-5deg);
        }
        
        .time-stamp {
            position: absolute;
            bottom: 15px;
            right: 20px;
            font-size: 12px;
            color: #666666;
            background: #FAFAFA;
            padding: 3px 8px;
            border-radius: 5px;
            border: 1px dashed #E0E0E0;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-badge">第1段</div>
        <div class="doodle-corner">☆</div>
        
        <div class="title-area">
            <h1 class="main-title">程序员小王的一天</h1>
            <div class="subtitle">早晨的"战斗"开始了</div>
        </div>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250710203633CF174407C2F2F1110240-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752237396&x-signature=MdJqXxgq4WBT%2Fop4ledKe9VJelo%3D" alt="程序员的早晨" class="main-image">
            <div class="image-annotation">又是熟悉的一天</div>
        </div>
        
        <div class="content-area">
            <div class="story-text">
                早上8点，<span class="highlight">闹钟响了三遍</span>才起床，匆忙洗漱后发现又要迟到了。
                <div class="emotion-bubble">😅</div>
            </div>
        </div>
        
        <div class="star-doodle">★</div>
        <div class="circle-doodle"></div>
        <div class="squiggle-line"></div>
        <div class="mood-indicator">😴</div>
        <div class="doodle-elements">~ 每天都是新的开始 ~</div>
        <div class="time-stamp">08:00 AM</div>
    </div>
</body>
</html>