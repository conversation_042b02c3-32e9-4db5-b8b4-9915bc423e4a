<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #f5f5f5;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .bg-decoration {
            position: absolute;
            top: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
        }
        
        .bg-decoration::after {
            content: '';
            position: absolute;
            bottom: -100px;
            left: -100px;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.08);
            border-radius: 50%;
        }
        
        .content {
            position: relative;
            z-index: 2;
            padding: 60px 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            text-align: center;
        }
        
        .emoji {
            font-size: 80px;
            margin-bottom: 30px;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            color: #ffffff;
            line-height: 1.2;
            margin-bottom: 20px;
            text-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }
        
        .highlight-word {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            border-radius: 2px;
        }
        
        .sub-title {
            font-size: 24px;
            font-weight: 500;
            color: rgba(255,255,255,0.9);
            line-height: 1.4;
            margin-bottom: 40px;
        }
        
        .description {
            font-size: 18px;
            font-weight: 400;
            color: rgba(255,255,255,0.8);
            line-height: 1.6;
            background: rgba(255,255,255,0.1);