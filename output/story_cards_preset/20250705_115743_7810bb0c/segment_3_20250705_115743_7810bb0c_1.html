<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品经理的需求变更</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #f5f5f5;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffd93d 50%, #6bcf7f 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
        }
        
        .content {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 100%;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 40px;
            color: #fff;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .highlight-1 {
            color: #ff3366;
            background: rgba(255,255,255,0.9);
            padding: 8px 16px;
            border-radius: 16px;
            display: inline-block;
            margin: 0 8px;
            transform: rotate(-2deg);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }
        
        .highlight-2 {
            color: #2d3436;
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-stroke: 2px #fff;
            -webkit-text-stroke: 2px #fff;
            font-size: 5rem;
            display: block;
            margin: 20px 0;
            transform: scale(1.1);
        }
        
        .highlight-3 {
            background: rgba(116, 185, 255, 0.9);
            color: #fff;
            padding: 12px 20px;
            border-radius: 20px;
            display: inline-block;
            transform: rotate(1deg);
            box-shadow: 0 8px 16px rgba(0,0,0,0.25);
            font-weight: 700;
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 400;
            color: rgba(255,255,255,0.9);
            margin-top: 50px;