<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            z-index: 1;
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
            text-align: center;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            color: #ffffff;
            line-height: 1.1;
            margin-bottom: 30px;
            text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.02em;
        }
        
        .highlight {
            color: #2d3436;
            background: linear-gradient(135deg, #fdcb6e, #e17055);
            padding: 8px 16px;
            border-radius: 12px;
            display: inline-block;
            margin: 0 4px;
            text-shadow: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 500;
            color: #2d3436;
            background: rgba(255, 255, 255, 0.9);
            padding: 20px 30px;
            border-radius: 16px;
            margin-bottom: 40px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            line-height: 1.4;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            pointer-events: none;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .circle-1 {
            width: 120px;
            height: 120px;
            top: 80px;
            right: 60px;
            animation: float 6s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 80px;
            height: 80px;
            bottom: 120px;
            left: 40px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        .circle-3 {
            width: 60px;
            height: 60px;
            top: 200px;
            left: 80px;
            animation: float 7s ease-in-out infinite;
        }
        
        .tag {
            position: absolute;
            bottom: 40px;
            right: 40px;
            background: rgba(45, 52, 54, 0.8);
            color: #ffffff;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .emoji {
            font-size: 3rem;
            margin-bottom: 20px;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
        
        <div class="content">
            <div class="emoji">💻</div>
            
            <h1 class="main-title">
                打开电脑第一件事<br>
                <span class="highlight">测试服务器</span>又挂了！
            </h1>
            
            <p class="subtitle">
                昨天的代码还在，一堆bug等着修复<br>
                程序员的日常就是这么刺激
            </p>
            
            <div class="tag">#程序员日常</div>
        </div>
    </div>
</body>
</html>