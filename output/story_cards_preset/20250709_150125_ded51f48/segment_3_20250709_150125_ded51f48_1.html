<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的崩溃瞬间</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.1) 100%);
            z-index: 1;
        }
        
        .content {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            color: #ffffff;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 30px;
            line-height: 1.1;
            letter-spacing: -0.02em;
        }
        
        .highlight-word {
            background: linear-gradient(45deg, #ff3838, #ff8f00);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            position: relative;
            display: inline-block;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
            z-index: -1;
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
            margin-bottom: 40px;
            opacity: 0.9;
            letter-spacing: 0.05em;
        }
        
        .description {
            font-size: 1.3rem;
            font-weight: 400;
            color: #ffffff;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            line-height: 1.6;
            max-width: 90%;
            opacity: 0.8;
            margin-bottom: 50px;
        }
        
        .emoji {
            font-size: 6rem;
            margin-bottom: 30px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .decorative-element {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            top: 50px;
            right: 50px;
            animation: float 3s ease-in-out infinite;
        }
        
        .decorative-element:nth-child(2) {
            width: 60px;
            height: 60px;
            bottom: 80px;
            left: 60px;
            top: auto;
            right: auto;
            animation-delay: 1s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        
        .tag {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-element"></div>
        <div class="decorative-element"></div>
        <div class="tag">第3段</div>
        
        <div class="content">
            <div class="emoji">😱</div>
            
            <h1 class="main-title">
                <span class="highlight-word">需求变更</span><br>
                又来了！
            </h1>
            
            <p class="subtitle">
                <span class="highlight-word">今天下午</span>就要完成？
            </p>
            
            <p class="description">
                中午吃饭时，产品经理又提出了新的需求变更，<br>
                要求今天下午就要完成，<span class="highlight-word">小王</span>内心万分崩溃
            </p>
        </div>
    </div>
</body>
</html>