<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 60px 50px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .header {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .segment-tag {
            display: inline-block;
            background: rgba(255, 255, 255, 0.9);
            color: #ff6b6b;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .title {
            font-size: 48px;
            font-weight: 900;
            color: #2c3e50;
            line-height: 1.2;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .keyword {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
        }
        
        .keyword::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 2px;
        }
        
        .description {
            font-size: 24px;
            color: #34495e;
            font-weight: 400;
            line-height: 1.5;
            max-width: 400px;
            margin-bottom: 30px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .highlight-text {
            background: rgba(255, 255, 255, 0.8);
            padding: 2px 8px;
            border-radius: 8px;
            font-weight: 500;
            color: #e74c3c;
        }
        
        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .circle-1 {
            width: 120px;
            height: 120px;
            top: 10%;
            left: 10%;
            animation: pulse 4s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 80px;
            height: 80px;
            top: 70%;
            right: 15%;
            animation: pulse 4s ease-in-out infinite 2s;
        }
        
        .circle-3 {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation: pulse 4s ease-in-out infinite 1s;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }
        
        .footer {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .theme-tag {
            background: rgba(255, 255, 255, 0.9);
            color: #7f8c8d;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 400;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
        </div>
        
        <div class="header">
            <div class="segment-tag">第1段</div>
        </div>
        
        <div class="main-content">
            <h1 class="title">
                <span class="keyword">程序员</span>的<span class="keyword">真实</span>日常
            </h1>
            <p class="description">
                早上8点<span class="highlight-text">三次闹钟</span>才起床<br>
                匆忙洗漱后又要迟到了
            </p>
        </div>
        
        <div class="footer">
            <div class="theme-tag">生活故事</div>
        </div>
    </div>
</body>
</html>