<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的深夜修Bug之路</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            z-index: 1;
        }

        .content {
            position: relative;
            z-index: 2;
            padding: 60px 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            color: #ffffff;
            line-height: 1.1;
            margin-bottom: 30px;
            text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
        }

        .highlight {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
        }

        .highlight::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #ff6b6b, #feca57);
            border-radius: 4px;
            opacity: 0.3;
        }

        .keyword {
            color: #feca57;
            font-weight: 900;
            text-shadow: 3px 3px 0px #ff6b6b, 6px 6px 12px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.8rem;
            font-weight: 500;
            color: #e8f4f8;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .description {
            font-size: 1.3rem;
            font-weight: 400;
            color: #b8d4e3;
            line-height: 1.6;
            max-width: 480px;
            margin-bottom: 50px;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }

        .circle1 {
            width: 120px;
            height: 120px;
            top: 80px;
            right: -60px;
            animation: float 6s ease-in-out infinite;
        }

        .circle2 {
            width: 80px;
            height: 80px;
            bottom: 120px;
            left: -40px;
            animation: float 4s ease-in-out infinite reverse;
        }

        .circle3 {
            width: 60px;
            height: 60px;
            top: 200px;
            left: 50px;
            animation: float 5s ease-in-out infinite;
        }

        .tag {
            position: absolute;
            bottom: 40px;
            right: 40px;
            background: rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .code-symbol {
            position: absolute;
            font-family: 'Courier New', monospace;
            font-size: 2rem;
            color: rgba(255, 255, 255, 0.3);
            animation: pulse 3s ease-in-out infinite;
        }

        .code-symbol1 {
            top: 150px;
            left: 80px;
        }

        .code-symbol2 {
            bottom: 200px;
            right: 100px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle1"></div>
            <div class="circle circle2"></div>
            <div class="circle circle3"></div>
            <div class="code-symbol code-symbol1">&lt;/&gt;</div>
            <div class="code-symbol code-symbol2">{ }</div>
        </div>
        
        <div class="content">
            <h1 class="main-title">
                深夜<span class="keyword">修Bug</span><br>
                <span class="highlight">终于完成</span>
            </h1>
            
            <p class="subtitle">
                拖着疲惫身体回家的程序员小王
            </p>
            
            <p class="description">
                下午加班到很晚，终于把bug修完了<br>
                发誓明天一定要早睡的日常
            </p>
        </div>
        
        <div class="tag">
            第4段 · 程序员日常
        </div>
    </div>
</body>
</html>