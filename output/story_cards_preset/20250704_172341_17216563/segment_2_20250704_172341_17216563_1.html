<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .decoration {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            top: 80px;
            right: 80px;
            opacity: 0.3;
        }
        
        .decoration2 {
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #48cae4, #023e8a);
            bottom: 100px;
            left: 60px;
            opacity: 0.4;
        }
        
        .content {
            text-align: center;
            z-index: 10;
            position: relative;
        }
        
        .main-title {
            font-size: 4.2rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 30px;
            color: #2d3436;
        }
        
        .highlight1 {
            color: #ff6b6b;
            text-shadow: 3px 3px 0px rgba(255,107,107,0.3);
            position: relative;
        }
        
        .highlight2 {
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            position: relative;
        }
        
        .highlight3 {
            color: #00b894;
            text-shadow: 2px 2px 8px rgba(0,184,148,0.4);
            position: relative;
        }
        
        .subtitle {
            font-size: 1.4rem;
            font-weight: 400;
            color: #636e72;
            margin-bottom: 40px;
            line-height: