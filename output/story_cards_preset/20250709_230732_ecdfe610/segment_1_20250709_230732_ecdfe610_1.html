<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .segment-badge {
            display: inline-block;
            background: #4ECDC4;
            color: #FFFFFF;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 20px;
            position: relative;
        }
        
        .segment-badge::after {
            content: "✨";
            position: absolute;
            right: -15px;
            top: -5px;
            font-size: 12px;
        }
        
        .main-title {
            font-size: 28px;
            color: #333333;
            font-weight: 700;
            line-height: 1.6;
            margin-bottom: 15px;
            position: relative;
        }
        
        .main-title::after {
            content: "";
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }
        
        .image-container {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            overflow: hidden;
            margin: 20px 0;
            border: 3px solid #FF6B6B;
            position: relative;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.2);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .story-preview {
            background: #FAFAFA;
            border: 1px dashed #E0E0E0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            max-width: 400px;
        }
        
        .story-text {
            font-size: 18px;
            color: #666666;
            line-height: 1.6;
            letter-spacing: 0.02em;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.2em;
            background-position: 0 88%;
            color: #333333;
            font-weight: 400;
        }
        
        .doodle-element {
            position: absolute;
            color: #FF6B6B;
            font-size: 16px;
            opacity: 0.7;
        }
        
        .doodle-1 {
            top: 10%;
            left: 10%;
            transform: rotate(-15deg);
        }
        
        .doodle-2 {
            top: 15%;
            right: 15%;
            transform: rotate(20deg);
        }
        
        .doodle-3 {
            bottom: 20%;
            left: 8%;
            transform: rotate(-10deg);
        }
        
        .doodle-4 {
            bottom: 15%;
            right: 10%;
            transform: rotate(15deg);
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
        }
        
        .footer-text {
            font-size: 14px;
            color: #666666;
            font-style: italic;
        }
        
        .emotion-icon {
            display: inline-block;
            margin: 0 5px;
            font-size: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .hand-drawn-circle {
            position: absolute;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.6;
        }
        
        .circle-1 {
            width: 30px;
            height: 30px;
            top: 12%;
            right: 20%;
            transform: rotate(45deg);
        }
        
        .circle-2 {
            width: 25px;
            height: 25px;
            bottom: 25%;
            left: 15%;
            transform: rotate(-30deg);
        }
        
        .annotation {
            position: absolute;
            font-size: 12px;
            color: #FF6B6B;
            background: #FFFFFF;
            padding: 3px 8px;
            border-radius: 8px;
            border: 1px solid #FF6B6B;
            transform: rotate(-5deg);
        }
        
        .annotation-1 {
            top: 45%;
            right: 5%;
        }
        
        .annotation-2 {
            bottom: 35%;
            left: 5%;
            transform: rotate(8deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <div class="segment-badge">第1段</div>
            <h1 class="main-title">程序员小王的一天</h1>
        </div>
        
        <div class="content-area">
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/f9ed35b6-2b1e-45e8-9503-68dbb7358d30.jpg" alt="程序员小王">
            </div>
            
            <div class="story-preview">
                <div class="story-text">
                    早上8点，<span class="highlight">闹钟响了三遍</span>才起床<span class="emotion-icon">😴</span><br>
                    匆忙洗漱后发现<span class="highlight">又要迟到了</span><span class="emotion-icon">😱</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="footer-text">~ 每个程序员都有过的清晨 ~</div>
        </div>
        
        <!-- 手绘装饰元素 -->
        <div class="doodle-element doodle-1">☆</div>
        <div class="doodle-element doodle-2">!</div>
        <div class="doodle-element doodle-3">?</div>
        <div class="doodle-element doodle-4">♪</div>
        
        <div class="hand-drawn-circle circle-1"></div>
        <div class="hand-drawn-circle circle-2"></div>
        
        <div class="annotation annotation-1">熟悉吗？</div>
        <div class="annotation annotation-2">日常</div>
    </div>
</body>
</html>