<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, transparent 49%, #FF6B6B 49%, #FF6B6B 51%, transparent 51%);
            background-size: 10px 10px;
            opacity: 0.1;
            z-index: -1;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -40px;
            top: -5px;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        
        .image-container {
            width: 100%;
            height: 200px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: -15px;
            right: -10px;
            background: #4ECDC4;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            transform: rotate(15deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .content-area {
            flex: 1;
            padding: 20px 0;
        }
        
        .main-content {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            margin-bottom: 20px;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, #FF6B6B 0%, #FF6B6B 100%, transparent 100%);
            background-size: 100% 3px;
            background-repeat: no-repeat;
            background-position: 0 100%;
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            top: 50%;
            right: -80px;
            transform: translateY(-50%);
            opacity: 0.7;
        }
        
        .emotion-icon {
            display: inline-block;
            font-size: 20px;
            margin: 0 5px;
            animation: bounce 1.5s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .annotation {
            position: absolute;
            background: #FAFAFA;
            border: 1px dashed #666666;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            color: #666666;
            transform: rotate(-2deg);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .annotation-1 {
            top: 40px;
            left: 20px;
        }
        
        .annotation-2 {
            bottom: 80px;
            right: 30px;
            transform: rotate(3deg);
        }
        
        .doodle-line {
            position: absolute;
            height: 2px;
            background: #4ECDC4;
            opacity: 0.6;
            border-radius: 1px;
        }
        
        .doodle-1 {
            width: 80px;
            top: 120px;
            left: 50px;
            transform: rotate(-5deg);
        }
        
        .doodle-2 {
            width: 60px;
            bottom: 150px;
            right: 40px;
            transform: rotate(8deg);
        }
        
        .story-flow {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666666;
            opacity: 0.8;
        }
        
        .story-flow::before {
            content: '→';
            margin-right: 5px;
            color: #FF6B6B;
        }
        
        .decorative-stars {
            position: absolute;
            font-size: 16px;
            color: #4ECDC4;
            opacity: 0.6;
        }
        
        .star-1 { top: 80px; right: 60px; }
        .star-2 { bottom: 200px; left: 80px; }
        .star-3 { top: 300px; right: 100px; }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第2段</div>
        
        <div class="image-container">
            <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/0ccb11f8-0adb-4150-8f1f-5c389337e1ae.jpg" alt="故事配图">
            <div class="image-annotation">现实很骨感</div>
        </div>
        
        <div class="content-area">
            <div class="main-content">
                到公司后，打开电脑第一件事就是查看昨天的代码，结果发现<span class="highlight">测试服务器挂了</span><span class="emotion-icon">😱</span>，一堆bug等着修复<span class="emotion-icon">💻</span>。
            </div>
        </div>
        
        <div class="annotation annotation-1">程序员的日常</div>
        <div class="annotation annotation-2">又是忙碌的一天！</div>
        
        <div class="doodle-line doodle-1"></div>
        <div class="doodle-line doodle-2"></div>
        
        <div class="decorative-stars star-1">⭐</div>
        <div class="decorative-stars star-2">✨</div>
        <div class="decorative-stars star-3">💫</div>
        
        <div class="story-flow">故事继续...</div>
    </div>
</body>
</html>