<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班修Bug的日常</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .doodle-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.05;
            background-image: 
                radial-gradient(circle at 20% 20%, #FF6B6B 2px, transparent 2px),
                radial-gradient(circle at 80% 80%, #4ECDC4 2px, transparent 2px),
                radial-gradient(circle at 40% 60%, #FF6B6B 1px, transparent 1px);
            background-size: 100px 100px, 150px 150px, 80px 80px;
        }
        
        .segment-tag {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #FF6B6B;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            transform: rotate(5deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .main-title {
            font-size: 32px;
            color: #333333;
            text-align: center;
            margin-top: 80px;
            margin-bottom: 20px;
            line-height: 1.4;
            position: relative;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: #4ECDC4;
            border-radius: 2px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            text-align: center;
            margin-bottom: 40px;
            font-weight: 300;
        }
        
        .content-area {
            background: #FAFAFA;
            border: 1px solid #E0E0E0;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            position: relative;
            min-height: 200px;
        }
        
        .story-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.3em;
            background-position: 0 88%;
            color: #333333;
            padding: 2px 0;
        }
        
        .emotion-icon {
            font-size: 24px;
            margin: 0 10px;
            display: inline-block;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }
        
        .doodle-elements {
            position: absolute;
            top: 150px;
            left: 50px;
            width: 40px;
            height: 40px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            transform: rotate(-15deg);
        }
        
        .doodle-elements::before {
            content: '';
            position: absolute;
            top: -20px;
            right: -30px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 15px solid #FF6B6B;
            transform: rotate(45deg);
        }
        
        .doodle-elements::after {
            content: '!';
            position: absolute;
            top: -40px;
            right: 60px;
            font-size: 20px;
            color: #FF6B6B;
            font-weight: bold;
        }
        
        .doodle-star {
            position: absolute;
            bottom: 100px;
            right: 80px;
            font-size: 18px;
            color: #4ECDC4;
            transform: rotate(15deg);
        }
        
        .annotation {
            position: absolute;
            bottom: 50px;
            left: 50px;
            font-size: 14px;
            color: #666666;
            background: #FFFFFF;
            padding: 8px 12px;
            border: 1px dashed #E0E0E0;
            border-radius: 5px;
            transform: rotate(-2deg);
        }
        
        .wavy-line {
            position: absolute;
            bottom: 150px;
            left: 100px;
            width: 200px;
            height: 20px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 20'%3E%3Cpath d='M0,10 Q50,0 100,10 T200,10' stroke='%23FF6B6B' stroke-width='2' fill='none'/%3E%3C/svg%3E") no-repeat;
            background-size: contain;
            opacity: 0.6;
        }
        
        .character-image {
            position: absolute;
            bottom: 200px;
            right: 100px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 3px solid #4ECDC4;
            object-fit: cover;
            transform: rotate(-5deg);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .thought-bubble {
            position: absolute;
            bottom: 320px;
            right: 60px;
            background: #FFFFFF;
            border: 2px solid #FF6B6B;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 12px;
            color: #333333;
            max-width: 100px;
            text-align: center;
        }
        
        .thought-bubble::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 15px solid #FF6B6B;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="doodle-bg"></div>
        <div class="segment-tag">第4段</div>
        
        <div class="main-title">
            加班修Bug的<br>
            <span class="highlight">深夜战斗</span>
        </div>
        
        <div class="subtitle">
            程序员小王的日常奋斗记
        </div>
        
        <div class="content-area">
            <div class="story-text">
                下午加班到很晚，终于把<span class="highlight">bug修完了</span>，<br>
                小王拖着疲惫的身体回到家，<br>
                发誓明天一定要早睡
                <span class="emotion-icon">😴</span>
            </div>
        </div>
        
        <div class="doodle-elements"></div>
        <div class="doodle-star">✨</div>
        <div class="wavy-line"></div>
        
        <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250709231145EA6CAE94ADC461BB1EE7-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752160308&x-signature=ojCrR5Qi%2BAfdFclwGWH1DlRFe%2FU%3D" alt="小王" class="character-image">
        
        <div class="thought-bubble">
            明天一定<br>早睡！
        </div>
        
        <div class="annotation">
            又是一个熬夜的夜晚... 🌙
        </div>
    </div>
</body>
</html>