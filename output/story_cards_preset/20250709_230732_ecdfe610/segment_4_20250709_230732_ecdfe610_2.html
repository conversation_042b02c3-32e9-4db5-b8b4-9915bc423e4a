<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 加班夜归</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            overflow: hidden;
            position: relative;
        }
        
        .card-container {
            width: 100%;
            height: 100%;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            top: -10px;
            right: -25px;
            font-size: 20px;
            color: #4ECDC4;
        }
        
        .image-container {
            width: 280px;
            height: 200px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: -15px;
            right: -20px;
            background: #FAFAFA;
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 14px;
            color: #666666;
            border: 1px dashed #4ECDC4;
            transform: rotate(5deg);
        }
        
        .content-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: center;
            margin: 25px 0;
            position: relative;
            background: #FAFAFA;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #E0E0E0;
        }
        
        .highlight {
            background: linear-gradient(transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 400;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 80px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            top: 45px;
            left: 20px;
            transform: rotate(-5deg);
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-star {
            position: absolute;
            color: #FF6B6B;
            font-size: 16px;
            animation: twinkle 2s infinite;
        }
        
        .star-1 { top: 80px; left: 480px; }
        .star-2 { top: 320px; left: 50px; }
        .star-3 { bottom: 150px; right: 80px; }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
        
        .emotion-face {
            position: absolute;
            bottom: 100px;
            right: 40px;
            font-size: 24px;
            color: #4ECDC4;
            transform: rotate(-10deg);
        }
        
        .divider-line {
            width: 200px;
            height: 2px;
            background: #4ECDC4;
            margin: 15px auto;
            border-radius: 2px;
            position: relative;
            transform: rotate(1deg);
        }
        
        .divider-line::before {
            content: '';
            position: absolute;
            left: -10px;
            top: -2px;
            width: 6px;
            height: 6px;
            background: #FF6B6B;
            border-radius: 50%;
        }
        
        .divider-line::after {
            content: '';
            position: absolute;
            right: -10px;
            top: -2px;
            width: 6px;
            height: 6px;
            background: #FF6B6B;
            border-radius: 50%;
        }
        
        .annotation-arrow {
            position: absolute;
            bottom: 180px;
            left: 320px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 15px solid #4ECDC4;
            transform: rotate(20deg);
        }
        
        .annotation-text {
            position: absolute;
            bottom: 200px;
            left: 280px;
            font-size: 14px;
            color: #666666;
            background: #FAFAFA;
            padding: 5px 8px;
            border-radius: 5px;
            border: 1px dashed #4ECDC4;
            transform: rotate(-3deg);
        }
        
        .corner-doodle {
            position: absolute;
            top: 10px;
            left: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #FF6B6B;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .corner-doodle::after {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            width: 10px;
            height: 10px;
            background: #4ECDC4;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="segment-title">第4段</div>
        
        <div class="image-container">
            <img src="https://p3-aiop-sign.byteimg.com/tos-cn-i-vuqhorh59i/20250709231145EA6CAE94ADC461BB1EE7-0~tplv-vuqhorh59i-image.image?rk3s=7f9e702d&x-expires=1752160308&x-signature=ojCrR5Qi%2BAfdFclwGWH1DlRFe%2FU%3D" alt="加班夜归">
            <div class="image-annotation">疲惫的夜晚</div>
        </div>
        
        <div class="divider-line"></div>
        
        <div class="content-text">
            下午加班到很晚，终于把<span class="highlight">bug修完了</span>，小王拖着疲惫的身体回到家，发誓明天一定要早睡。
            <div class="emphasis-circle"></div>
        </div>
        
        <div class="doodle-elements">
            <div class="doodle-star star-1">★</div>
            <div class="doodle-star star-2">✦</div>
            <div class="doodle-star star-3">✧</div>
            <div class="emotion-face">😴</div>
            <div class="annotation-arrow"></div>
            <div class="annotation-text">又是熬夜的一天</div>
            <div class="corner-doodle"></div>
        </div>
    </div>
</body>
</html>