<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 故事情节卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            letter-spacing: 0.02em;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .card::after {
            content: '✨';
            position: absolute;
            top: 60px;
            left: 20px;
            font-size: 16px;
            color: #FF6B6B;
            opacity: 0.6;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 30px;
            position: relative;
            font-weight: 700;
        }
        
        .segment-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 2px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .image-container {
            width: 100%;
            height: 200px;
            margin-bottom: 25px;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: -15px;
            right: -10px;
            background: #FFFFFF;
            color: #666666;
            padding: 5px 10px;
            border-radius: 8px;
            font-size: 14px;
            border: 1px dashed #4ECDC4;
            transform: rotate(8deg);
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }
        
        .main-content {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            text-align: center;
            background: #FAFAFA;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #E0E0E0;
            position: relative;
            margin-bottom: 20px;
        }
        
        .highlight {
            background: linear-gradient(120deg, rgba(255, 107, 107, 0.2) 0%, rgba(255, 107, 107, 0.2) 100%);
            padding: 2px 6px;
            border-radius: 4px;
            position: relative;
        }
        
        .highlight::after {
            content: '!';
            position: absolute;
            top: -10px;
            right: -8px;
            color: #FF6B6B;
            font-size: 16px;
            font-weight: bold;
        }
        
        .emotion-icon {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 24px;
            opacity: 0.7;
        }
        
        .doodle-elements {
            position: absolute;
            bottom: 30px;
            left: 30px;
            width: 40px;
            height: 40px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .doodle-elements::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50px;
            width: 15px;
            height: 15px;
            background: #FF6B6B;
            border-radius: 50%;
            opacity: 0.5;
        }
        
        .annotation-bubble {
            position: absolute;
            bottom: 80px;
            right: 50px;
            background: #FFFFFF;
            color: #666666;
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 14px;
            border: 1px dashed #FF6B6B;
            transform: rotate(-3deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .annotation-bubble::before {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            border-top: 6px solid #FFFFFF;
        }
        
        .squiggly-line {
            position: absolute;
            bottom: 150px;
            left: 80px;
            width: 60px;
            height: 2px;
            background: #4ECDC4;
            border-radius: 2px;
            transform: rotate(-5deg);
            opacity: 0.6;
        }
        
        .squiggly-line::before {
            content: '';
            position: absolute;
            top: -4px;
            left: 20px;
            width: 20px;
            height: 2px;
            background: #4ECDC4;
            border-radius: 2px;
            transform: rotate(10deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第3段</div>
        
        <div class="image-container">
            <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/588f60d6-998e-4ba0-982f-c098eee3af55.jpg" alt="故事情节图片">
            <div class="image-annotation">现场直击！</div>
        </div>
        
        <div class="content-area">
            <div class="main-content">
                中午吃饭时，<span class="highlight">产品经理又提出了新的需求变更</span>，要求今天下午就要完成，小王内心万分崩溃。
                <div class="emotion-icon">😱</div>
            </div>
            
            <div class="annotation-bubble">
                又是熟悉的配方~
            </div>
            
            <div class="squiggly-line"></div>
        </div>
        
        <div class="doodle-elements"></div>
    </div>
</body>
</html>