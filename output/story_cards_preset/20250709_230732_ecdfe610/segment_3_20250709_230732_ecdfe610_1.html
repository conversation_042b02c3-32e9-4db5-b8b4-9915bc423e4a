<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的日常崩溃</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .cover-card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            box-sizing: border-box;
        }
        
        .doodle-corner {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            color: #FF6B6B;
            transform: rotate(15deg);
        }
        
        .segment-badge {
            position: absolute;
            top: 30px;
            left: 30px;
            background: #4ECDC4;
            color: #FFFFFF;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 400;
            transform: rotate(-5deg);
        }
        
        .main-title {
            font-size: 36px;
            color: #333333;
            text-align: center;
            margin-top: 120px;
            margin-bottom: 30px;
            line-height: 1.3;
            font-weight: 700;
            position: relative;
        }
        
        .title-underline {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
            transform: translateX(-50%) rotate(-1deg);
        }
        
        .story-content {
            background: #FAFAFA;
            padding: 30px;
            border-radius: 12px;
            margin: 40px 0;
            font-size: 20px;
            color: #333333;
            line-height: 1.6;
            text-align: center;
            position: relative;
            border: 1px solid #E0E0E0;
        }
        
        .stress-emoji {
            font-size: 32px;
            margin: 0 8px;
            display: inline-block;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .highlight-word {
            color: #FF6B6B;
            font-weight: 700;
            position: relative;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: #FF6B6B;
            transform: rotate(-1deg);
        }
        
        .doodle-elements {
            position: absolute;
            bottom: 100px;
            left: 50px;
            font-size: 18px;
            color: #4ECDC4;
            transform: rotate(-15deg);
        }
        
        .exclamation-marks {
            position: absolute;
            top: 50%;
            right: 30px;
            font-size: 28px;
            color: #FF6B6B;
            transform: rotate(20deg);
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { transform: rotate(20deg) scale(1); }
            50% { transform: rotate(20deg) scale(1.1); }
            100% { transform: rotate(20deg) scale(1); }
        }
        
        .mood-indicator {
            position: absolute;
            bottom: 50px;
            right: 50px;
            font-size: 24px;
            color: #666666;
        }
        
        .hand-drawn-circle {
            position: absolute;
            width: 80px;
            height: 80px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            top: 200px;
            left: 30px;
            transform: rotate(15deg);
            opacity: 0.6;
        }
        
        .hand-drawn-star {
            position: absolute;
            bottom: 150px;
            left: 100px;
            color: #FF6B6B;
            font-size: 20px;
            transform: rotate(-30deg);
        }
        
        .subtitle {
            font-size: 18px;
            color: #666666;
            text-align: center;
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .emotion-bubble {
            position: absolute;
            top: 300px;
            right: 80px;
            background: #FFFFFF;
            border: 1px dashed #FF6B6B;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 14px;
            color: #FF6B6B;
            transform: rotate(10deg);
        }
    </style>
</head>
<body>
    <div class="cover-card">
        <div class="doodle-corner">✨</div>
        <div class="segment-badge">第3段</div>
        
        <div class="main-title">
            需求变更的<span class="highlight-word">噩梦</span>
            <div class="title-underline"></div>
        </div>
        
        <div class="subtitle">程序员小王的日常崩溃时刻</div>
        
        <div class="story-content">
            中午吃饭时，产品经理又提出了新的<span class="highlight-word">需求变更</span>，要求今天下午就要完成
            <span class="stress-emoji">😱</span>
            <br><br>
            小王内心万分崩溃...
        </div>
        
        <div class="exclamation-marks">！！！</div>
        <div class="emotion-bubble">又来了...</div>
        
        <div class="hand-drawn-circle"></div>
        <div class="hand-drawn-star">★</div>
        
        <div class="doodle-elements">～(>_<)～</div>
        <div class="mood-indicator">💔</div>
    </div>
</body>
</html>