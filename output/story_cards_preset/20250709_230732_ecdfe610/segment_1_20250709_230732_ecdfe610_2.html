<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            overflow: hidden;
            position: relative;
        }
        
        .card-container {
            width: 100%;
            height: 100%;
            padding: 25px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .segment-title {
            font-size: 20px;
            color: #666666;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .segment-title::after {
            content: "✨";
            position: absolute;
            right: -25px;
            top: -2px;
            color: #4ECDC4;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px 20px;
        }
        
        .story-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            margin-bottom: 30px;
            position: relative;
            background: #FAFAFA;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #E0E0E0;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.3em;
            background-position: 0 88%;
            color: #333333;
            font-weight: 700;
        }
        
        .emphasis-circle {
            position: absolute;
            border: 2px solid #FF6B6B;
            border-radius: 50%;
            background: transparent;
        }
        
        .circle-1 {
            width: 60px;
            height: 60px;
            top: -10px;
            left: -15px;
            transform: rotate(-15deg);
        }
        
        .image-container {
            width: 200px;
            height: 150px;
            margin: 20px auto;
            border: 2px dashed #4ECDC4;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            background: #FAFAFA;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: #FFFFFF;
            padding: 5px 10px;
            border: 1px solid #E0E0E0;
            border-radius: 15px;
            transform: rotate(-5deg);
        }
        
        .annotation-1 {
            top: 20px;
            right: -40px;
        }
        
        .annotation-2 {
            bottom: 20px;
            left: -50px;
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-star {
            position: absolute;
            color: #4ECDC4;
            font-size: 16px;
            transform: rotate(15deg);
        }
        
        .star-1 { top: 100px; left: 80px; }
        .star-2 { top: 300px; right: 60px; }
        .star-3 { bottom: 200px; left: 50px; }
        
        .doodle-line {
            position: absolute;
            width: 40px;
            height: 2px;
            background: #FF6B6B;
            transform: rotate(-10deg);
            border-radius: 1px;
        }
        
        .line-1 { top: 150px; right: 100px; }
        .line-2 { bottom: 250px; left: 100px; }
        
        .emotion-icon {
            position: absolute;
            font-size: 24px;
            color: #FF6B6B;
        }
        
        .sleepy-face {
            top: 50px;
            right: 50px;
            transform: rotate(10deg);
        }
        
        .rush-face {
            bottom: 100px;
            right: 80px;
            transform: rotate(-10deg);
        }
        
        .footer-note {
            font-size: 12px;
            color: #666666;
            text-align: center;
            font-style: italic;
            position: relative;
        }
        
        .footer-note::before {
            content: "~";
            position: absolute;
            left: -20px;
            color: #4ECDC4;
        }
        
        .footer-note::after {
            content: "~";
            position: absolute;
            right: -20px;
            color: #4ECDC4;
        }
        
        .thought-bubble {
            position: absolute;
            top: 200px;
            left: 80px;
            width: 80px;
            height: 50px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            background: rgba(78, 205, 196, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transform: rotate(-8deg);
        }
        
        .alarm-icon {
            position: absolute;
            top: 400px;
            right: 100px;
            font-size: 20px;
            color: #FF6B6B;
            transform: rotate(15deg);
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="segment-title">第1段</div>
        
        <div class="main-content">
            <div class="story-text">
                <div class="emphasis-circle circle-1"></div>
                程序员小王的一天：早上8点，<span class="highlight">闹钟响了三遍</span>才起床，匆忙洗漱后发现又要迟到了。
            </div>
            
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/f9ed35b6-2b1e-45e8-9503-68dbb7358d30.jpg" alt="程序员小王的早晨">
                <div class="annotation annotation-1">又迟到了！</div>
                <div class="annotation annotation-2">熟悉的场景</div>
            </div>
        </div>
        
        <div class="footer-note">每个程序员的日常</div>
        
        <div class="doodle-elements">
            <div class="doodle-star star-1">★</div>
            <div class="doodle-star star-2">☆</div>
            <div class="doodle-star star-3">✦</div>
            
            <div class="doodle-line line-1"></div>
            <div class="doodle-line line-2"></div>
            
            <div class="emotion-icon sleepy-face">😴</div>
            <div class="emotion-icon rush-face">😰</div>
            
            <div class="thought-bubble">💭</div>
            <div class="alarm-icon">⏰</div>
        </div>
    </div>
</body>
</html>