<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 程序员的灾难早晨</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            letter-spacing: 0.02em;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 20 20'%3E%3Cg fill='%23FF6B6B' fill-opacity='0.03'%3E%3Cpath d='M10 0l10 10-10 10L0 10z'/%3E%3C/g%3E%3C/svg%3E") repeat;
            z-index: -1;
        }
        
        .segment-tag {
            position: absolute;
            top: 30px;
            left: 30px;
            background: #4ECDC4;
            color: #FFFFFF;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            transform: rotate(-3deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .main-title {
            font-size: 42px;
            font-weight: 700;
            color: #333333;
            margin: 40px 0 20px 0;
            line-height: 1.2;
            position: relative;
            text-shadow: 2px 2px 0px rgba(255,107,107,0.1);
        }
        
        .main-title::after {
            content: '!!!';
            position: absolute;
            top: -10px;
            right: -40px;
            font-size: 24px;
            color: #FF6B6B;
            animation: bounce 2s infinite;
        }
        
        .subtitle {
            font-size: 24px;
            color: #666666;
            margin: 20px 0 40px 0;
            line-height: 1.4;
            position: relative;
        }
        
        .subtitle::before {
            content: '→';
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            color: #4ECDC4;
            font-size: 20px;
        }
        
        .content-preview {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            margin: 30px 0;
            padding: 20px;
            background: #FAFAFA;
            border-radius: 10px;
            border-left: 4px solid #FF6B6B;
            position: relative;
            max-width: 80%;
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-star {
            position: absolute;
            color: #4ECDC4;
            font-size: 16px;
            animation: twinkle 3s infinite;
        }
        
        .star-1 { top: 15%; left: 85%; animation-delay: 0s; }
        .star-2 { top: 70%; left: 15%; animation-delay: 1s; }
        .star-3 { top: 25%; left: 20%; animation-delay: 2s; }
        
        .doodle-circle {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid #FF6B6B;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .circle-1 { top: 60%; right: 20%; transform: rotate(15deg); }
        .circle-2 { bottom: 20%; left: 25%; transform: rotate(-10deg); }
        
        .bug-emoji {
            font-size: 60px;
            margin: 20px 0;
            animation: shake 1s infinite;
        }
        
        .emphasis-underline {
            position: relative;
            display: inline-block;
        }
        
        .emphasis-underline::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
            opacity: 0.6;
        }
        
        .hand-drawn-arrow {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #4ECDC4;
            animation: float 2s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }
        
        @keyframes float {
            0%, 100% { transform: translateX(-50%) translateY(0); }
            50% { transform: translateX(-50%) translateY(-10px); }
        }
        
        .mood-indicator {
            position: absolute;
            bottom: 40px;
            right: 40px;
            font-size: 32px;
            opacity: 0.7;
            transform: rotate(15deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-tag">第2段</div>
        
        <div class="doodle-elements">
            <div class="doodle-star star-1">★</div>
            <div class="doodle-star star-2">✦</div>
            <div class="doodle-star star-3">✧</div>
            <div class="doodle-circle circle-1"></div>
            <div class="doodle-circle circle-2"></div>
        </div>
        
        <div class="main-title">
            <span class="emphasis-underline">程序员的</span><br>
            灾难早晨
        </div>
        
        <div class="bug-emoji">🐛💥</div>
        
        <div class="subtitle">
            当你满怀期待打开电脑时...
        </div>
        
        <div class="content-preview">
            测试服务器挂了<br>
            一堆bug等着修复<br>
            美好的一天从崩溃开始 😅
        </div>
        
        <div class="hand-drawn-arrow">↗</div>
        
        <div class="mood-indicator">😱</div>
    </div>
</body>
</html>