<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求变更的崩溃时刻</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fff8f0 0%, #ffeaa7 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #fff 0%, #fef5e7 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
        }
        
        .segment-badge {
            position: absolute;
            top: 25px;
            right: 25px;
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 60px 0;
        }
        
        .title {
            font-size: 48px;
            font-weight: 900;
            line-height: 1.2;
            margin-bottom: 30px;
            color: #2d3436;
        }
        
        .highlight-1 {
            color: #ff6b6b;
            text-shadow: 2px 2px 4px rgba(255, 107, 107, 0.3);
            position: relative;
        }
        
        .highlight-1::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #feca57);
            border-radius: 2px;
        }
        
        .highlight-2 {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 900;
            position: relative;
        }
        
        .highlight-3 {
            color: #48dbfb;
            text-shadow: 0 0 10px rgba(72, 219, 251, 0.5);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .subtitle {
            font-size: 24px;
            font-weight: 500;
            color: #636e72;
            margin-bottom: 40px;
            line-height: 1.4;
        }
        
        .description {
            font-size: 18px;
            color: #74b9ff;
            font-weight: 400;
            line-height: 1.6;
            max-width: 450px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(116, 185, 255, 0.1);
            border-radius: 15px;
            border-left: 4px solid #74b9ff;
        }
        
        .emoji-decoration {
            font-size: 60px;
            margin: 20px 0;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .circle-1 {
            position: absolute;
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            border-radius: 50%;
            top: 100px;
            left: -50px;
            opacity: 0.1;
        }
        
        .circle-2 {
            position: absolute;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #48dbfb, #ff9ff3);
            border-radius: 50%;
            bottom: 150px;
            right: -40px;
            opacity: 0.15;
        }
        
        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 60px;
            background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            opacity: 0.1;
            clip-path: polygon(0 20px, 100% 0, 100% 100%, 0 100%);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-badge">第3段</div>
        
        <div class="decorative-elements">
            <div class="circle-1"></div>
            <div class="circle-2"></div>
            <div class="wave"></div>
        </div>
        
        <div class="main-content">
            <div class="title">
                <span class="highlight-1">需求变更</span>的<br>
                <span class="highlight-2">崩溃</span>时刻<br>
                <span class="highlight-3">😱</span>
            </div>
            
            <div class="emoji-decoration">💻⚡️</div>
            
            <div class="subtitle">
                午餐时间的突然袭击<br>
                下午就要完成的魔鬼要求
            </div>
            
            <div class="description">
                产品经理的神操作让小王内心万分崩溃，这种职场日常你是否也深有体会？
            </div>
        </div>
    </div>
</body>
</html>