<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #fff5f5 0%, #fffbf0 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 60px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4ecdc4, #45b7d1);
        }
        
        .decorative-element {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255, 107, 107, 0.1);
            top: 50px;
            right: -60px;
            animation: float 6s ease-in-out infinite;
        }
        
        .decorative-element::after {
            content: '';
            position: absolute;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 217, 61, 0.15);
            bottom: -40px;
            left: -40px;
            animation: float 8s ease-in-out infinite reverse;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .segment-badge {
            background: #ff6b6b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 30px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            color: #2c3e50;
            text-align: center;
            line-height: 1.2;
            margin-bottom: 30px;
            position: relative;
        }
        
        .highlight-word {
            color: #ff6b6b;
            position: relative;
            text-shadow: 2px 2px 4px rgba(255, 107, 107, 0.3);
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #ff6b6b, #ffd93d);
            border-radius: 3px;
            opacity: 0.7;
        }
        
        .secondary-highlight {
            color: #4ecdc4;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(78, 205, 196, 0.4);
        }
        
        .content-text {
            font-size: 24px;
            color: #555;
            text-align: center;
            line-height: 1.6;
            margin-bottom: 40px;
            font-weight: 400;
            max-width: 80%;
        }
        
        .emoji-decoration {
            font-size: 60px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .story-elements {
            display: flex;
            justify-content: space-around;
            width: 100%;
            margin-top: 40px;
            position: relative;
        }
        
        .story-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 14px;
            color: #666;
            opacity: 0.8;
        }
        
        .story-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 20px;
        }
        
        .alarm-icon {
            background: linear-gradient(135deg, #ff6b6b, #ff8a80);
            color: white;
        }
        
        .rush-icon {
            background: linear-gradient(135deg, #ffd93d, #ffeb3b);
            color: #333;
        }
        
        .late-icon {
            background: linear-gradient(135deg, #4ecdc4, #26c6da);
            color: white;
        }
        
        .bottom-accent {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-element"></div>
        
        <div class="segment-badge">第1段</div>
        
        <div class="emoji-decoration">😴</div>
        
        <h1 class="main-title">
            <span class="highlight-word">闹钟</span>三响才起床<br>
            又是<span class="secondary-highlight">迟到</span>的一天
        </h1>
        
        <p class="content-text">
            早上8点，熟悉的闹钟声响起<br>
            匆忙洗漱后发现时间不够了
        </p>
        
        <div class="story-elements">
            <div class="story-item">
                <div class="story-icon alarm-icon">⏰</div>
                <span>闹钟三响</span>
            </div>
            <div class="story-item">
                <div class="story-icon rush-icon">🏃</div>
                <span>匆忙洗漱</span>
            </div>
            <div class="story-item">
                <div class="story-icon late-icon">⏱️</div>
                <span>又要迟到</span>
            </div>
        </div>
        
        <div class="bottom-accent"></div>
    </div>
</body>
</html>