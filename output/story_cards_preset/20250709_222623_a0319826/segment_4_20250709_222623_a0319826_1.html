<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加班修Bug的日常</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
            50% { transform: translate(-50%, -50%) rotate(180deg); }
        }
        
        .segment-badge {
            position: absolute;
            top: 30px;
            right: 30px;
            background: #2d3436;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            z-index: 10;
        }
        
        .content-wrapper {
            z-index: 5;
            text-align: center;
            max-width: 100%;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            color: #2d3436;
            line-height: 1.2;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .highlight-bug {
            color: #e17055;
            background: rgba(255,255,255,0.3);
            padding: 5px 15px;
            border-radius: 15px;
            display: inline-block;
            transform: rotate(-2deg);
            margin: 0 5px;
            border: 3px solid #e17055;
        }
        
        .highlight-tired {
            color: #6c5ce7;
            text-shadow: 3px 3px 0px rgba(108,92,231,0.3);
            font-size: 1.1em;
        }
        
        .highlight-promise {
            color: #00b894;
            background: rgba(0,184,148,0.2);
            padding: 8px 20px;
            border-radius: 25px;
            display: inline-block;
            border: 2px dashed #00b894;
        }
        
        .story-text {
            font-size: 20px;
            color: #636e72;
            line-height: 1.6;
            margin-bottom: 40px;
            font-weight: 400;
            max-width: 480px;
        }
        
        .character-illustration {
            position: absolute;
            bottom: 50px;
            right: 40px;
            width: 120px;
            height: 120px;
            background: #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .code-symbol {
            position: absolute;
            color: rgba(45,52,54,0.1);
            font-family: 'Courier New', monospace;
            font-weight: bold;
            animation: drift 8s linear infinite;
        }
        
        .code-symbol:nth-child(1) {
            top: 15%;
            left: 10%;
            font-size: 24px;
            animation-delay: 0s;
        }
        
        .code-symbol:nth-child(2) {
            top: 25%;
            right: 15%;
            font-size: 18px;
            animation-delay: 2s;
        }
        
        .code-symbol:nth-child(3) {
            bottom: 30%;
            left: 20%;
            font-size: 20px;
            animation-delay: 4s;
        }
        
        @keyframes drift {
            0% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.1; }
            100% { transform: translateY(0px) rotate(360deg); opacity: 0.3; }
        }
        
        .emotion-indicator {
            position: absolute;
            bottom: 120px;
            left: 50px;
            font-size: 40px;
            animation: pulse 1.5s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-badge">第4段</div>
        
        <div class="decorative-elements">
            <div class="code-symbol">&lt;/&gt;</div>
            <div class="code-symbol">{}</div>
            <div class="code-symbol">bug</div>
        </div>
        
        <div class="content-wrapper">
            <h1 class="main-title">
                终于把<span class="highlight-bug">BUG</span>修完了！<br>
                <span class="highlight-tired">拖着疲惫身体</span>回家<br>
                <span class="highlight-promise">发誓明天早睡</span>
            </h1>
            
            <p class="story-text">
                下午加班到很晚，小王终于解决了那个困扰已久的bug，虽然身心俱疲，但内心还是有种成就感...
            </p>
        </div>
        
        <div class="character-illustration">
            😴
        </div>
        
        <div class="emotion-indicator">
            💤
        </div>
    </div>
</body>
</html>