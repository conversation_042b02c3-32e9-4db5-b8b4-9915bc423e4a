<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员日常</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #fff9e6 0%, #fff 50%, #f8f9ff 100%);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 50px 40px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 193, 7, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(0, 0) rotate(0deg); }
            50% { transform: translate(-20px, -20px) rotate(180deg); }
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
            z-index: 2;
        }
        
        .segment-badge {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
            transform: rotate(-2deg);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            line-height: 1.2;
            margin-bottom: 30px;
            color: #2c3e50;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .highlight-1 {
            color: #e74c3c;
            background: linear-gradient(45deg, #ffeb3b, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
            position: relative;
        }
        
        .highlight-1::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            right: 0;
            height: 6px;
            background: #ffeb3b;
            border-radius: 3px;
            opacity: 0.7;
            z-index: -1;
        }
        
        .highlight-2 {
            color: #8e44ad;
            text-decoration: underline;
            text-decoration-color: #e74c3c;
            text-decoration-thickness: 4px;
            text-underline-offset: 8px;
        }
        
        .highlight-3 {
            color: #27ae60;
            background: rgba(46, 204, 113, 0.2);
            padding: 0 10px;
            border-radius: 8px;
            border: 2px dashed #2ecc71;
        }
        
        .description {
            font-size: 20px;
            color: #7f8c8d;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: 40px;
            max-width: 480px;
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
        }
        
        .image-container {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto 30px;
            border: 6px solid #fff;
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
            position: relative;
            transform: rotate(-5deg);
        }
        
        .image-container::before {
            content: '💻';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 60px;
            z-index: 1;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0.8;
        }
        
        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1;
        }
        
        .bug-icon {
            position: absolute;
            top: 15%;
            right: 10%;
            font-size: 24px;
            color: #e74c3c;
            animation: bounce 2s infinite;
        }
        
        .code-icon {
            position: absolute;
            bottom: 20%;
            left: 8%;
            font-size: 20px;
            color: #3498db;
            animation: pulse 3s infinite;
        }
        
        .server-icon {
            position: absolute;
            top: 25%;
            left: 12%;
            font-size: 18px;
            color: #95a5a6;
            animation: shake 4s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }
        
        .footer {
            text-align: center;
            position: relative;
            z-index: 2;
        }
        
        .mood-indicator {
            display: inline-block;
            font-size: 32px;
            margin-bottom: 10px;
            animation: rotate 4s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .status-text {
            font-size: 16px;
            color: #95a5a6;
            font-weight: 300;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="bug-icon">🐛</div>
            <div class="code-icon">💻</div>
            <div class="server-icon">🔧</div>
        </div>
        
        <div class="header">
            <div class="segment-badge">第2段</div>
        </div>
        
        <div class="main-content">
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/2661b6f6-85a6-4afd-9889-4f03f7379068.jpg" alt="程序员日常">
            </div>
            
            <h1 class="main-title">
                早上到公司<br>
                <span class="highlight-1">服务器</span>又<span class="highlight-2">挂了</span><br>
                <span class="highlight-3">Bug满天飞</span>
            </h1>
            
            <p class="description">
                每个程序员都懂的痛：满怀期待打开电脑，却发现昨天的代码变成了今天的噩梦
            </p>
        </div>
        
        <div class="footer">
            <div class="mood-indicator">😵‍💫</div>
            <div class="status-text">又是充满挑战的一天...</div>
        </div>
    </div>
</body>
</html>