<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员深夜加班记</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 20px;
            padding: 60px 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .emoji {
            font-size: 4em;
            margin-bottom: 20px;
            display: block;
        }
        
        .main-title {
            font-size: 3.5em;
            font-weight: 900;
            color: #ffffff;
            line-height: 1.1;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .highlight-1 {
            color: #ff6b6b;
            text-shadow: 0 0 10px rgba(255,107,107,0.5);
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .highlight-2 {
            color: #4ecdc4;
            text-shadow: 0 0 10px rgba(78,205,196,0.5);
            font-size: 1.2em;
            position: relative;
        }
        
        .highlight-2::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #4ecdc4, #44a08d);
            border-radius: 2px;
        }
        
        .middle-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .description {
            font-size: 1.4em;
            color: #e8f4f8;
            line-height: 1.6;
            margin-bottom: 40px;
            font-weight: 300;
            max-width: 450px;
        }
        
        .key-point {
            background: rgba(255,255,255,0.15);
            padding: 25px 35px;
            border-radius: 15px;
            border: 2px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            margin: 20px 0;
        }
        
        .key-point-title {
            font-size: 1.8em;
            font-weight: 700;
            color: #ffd93d;
            margin-bottom: 15px;
        }
        
        .key-point-text {
            font-size: 1.1em;
            color: #ffffff;
            line-height: 1.5;
        }
        
        .bottom-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }
        
        .tag {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            margin-right: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .author {
            font-size: 0.9em;
            color: #b8e6e6;
            font-weight: 300;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
        }
        
        .circle-1 {
            width: 100px;
            height: 100px;
            top: 10%;
            right: 10%;
            animation: pulse 4s ease-in-out infinite;
        }
        
        .circle-2 {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 15%;
            animation: pulse 3s ease-in-out infinite reverse;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.1); opacity: 0.6; }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
        </div>
        
        <div class="content">
            <div class="header">
                <span class="emoji">💻</span>
                <h1 class="main-title">
                    深夜<span class="highlight-1">加班</span><br>
                    <span class="highlight-2">修Bug</span>日记
                </h1>
            </div>
            
            <div class="middle-content">
                <p class="description">
                    疲惫的身体拖着回家，心中默念着明天一定要早睡的誓言
                </p>
                
                <div class="key-point">
                    <div class="key-point-title">程序员的夜晚</div>
                    <div class="key-point-text">
                        Bug修完了，但健康和作息呢？
                    </div>
                </div>
            </div>
            
            <div class="bottom-section">
                <div>
                    <span class="tag">#程序员</span>
                    <span class="tag">#加班</span>
                    <span class="tag">#工作日常</span>
                </div>
                <div class="author">职场生活分享</div>
            </div>
        </div>
    </div>
</body>
</html>