<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 24px;
            padding: 50px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            box-shadow: 0 30px 60px rgba(0,0,0,0.2);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content {
            position: relative;
            z-index: 2;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 30px;
            color: #2d3436;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.1);
        }
        
        .highlight {
            color: #ff6b6b;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-stroke: 2px #fff;
            -webkit-text-stroke: 2px #fff;
        }
        
        .emphasis {
            font-size: 5.2rem;
            color: #00b894;
            text-shadow: 4px 4px 8px rgba(0,0,0,0.2);
            display: block;
            margin: 10px 0;
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 400;
            color: #636e72;
            margin-bottom: 40px;
            opacity: 0.9;
            letter-spacing: 0.5px;
        }
        
        .description {
            font-size: 1.4rem;
            font-weight: 300;
            color: #2d3436;
            line-height: 1.6;
            max-width: 90%;
            background: rgba(255,255,255,0.3);
            padding: 20px 30px;
            border-radius: 16px;
            border: 2px solid rgba(255,255,255,0.4);
            backdrop-filter: blur(10px);
        }
        
        .decorative-circle {
            position: absolute;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            top: 60px;
            right: 60px;
        }
        
        .decorative-square {
            position: absolute;
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.15);
            bottom: 80px;
            left: 50px;
            transform: rotate(45deg);
            border-radius: 12px;
        }
        
        .alarm-icon {
            position: absolute;
            top: 40px;
            left: 50px;
            font-size: 2.5rem;
            color: #fdcb6e;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-circle"></div>
        <div class="decorative-square"></div>
        <div class="alarm-icon">⏰</div>
        
        <div class="content">
            <h1 class="main-title">
                程序员的<span class="highlight">真实</span>
                <span class="emphasis">日常</span>
            </h1>
            
            <p class="subtitle">每个打工人的痛点时刻</p>
            
            <div class="description">
                闹钟响三遍才起床，匆忙洗漱又要迟到<br>
                这就是我们的日常生活写照
            </div>
        </div>
    </div>
</body>
</html>