<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>职场需求变更崩溃瞬间</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 30%, #ff9ff3 70%, #54a0ff 100%);
            border-radius: 24px;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content {
            text-align: center;
            z-index: 2;
            position: relative;
        }
        
        .main-title {
            font-size: 4.5rem;
            font-weight: 900;
            color: #ffffff;
            line-height: 1.1;
            margin-bottom: 30px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.02em;
        }
        
        .highlight-word {
            background: linear-gradient(45deg, #feca57, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-stroke: 2px #ffffff;
            -webkit-text-stroke: 2px #ffffff;
            position: relative;
            display: inline-block;
        }
        
        .highlight-word::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 100%;
            height: 6px;
            background: linear-gradient(90deg, #feca57, #ff6b6b);
            border-radius: 3px;
        }
        
        .stress-word {
            color: #feca57;
            font-size: 5.2rem;
            text-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
            transform: scale(1.1);
            display: inline-block;
        }
        
        .subtitle {
            font-size: 1.8rem;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            line-height: 1.4;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .description {
            font-size: 1.3rem;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            max-width: 480px;
            margin-bottom: 50px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .accent-element {
            position: absolute;
            top: 50px;
            right: 50px;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            animation: pulse 3s ease-in-out infinite;
        }
        
        .accent-element::before {
            content: '💻';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2.5rem;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        .bottom-accent {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 4px;
            background: linear-gradient(90deg, #feca57, #ff6b6b, #54a0ff);
            border-radius: 2px;
            opacity: 0.8;
        }
        
        .corner-decoration {
            position: absolute;
            top: 30px;
            left: 30px;
            width: 60px;
            height: 60px;
            border-left: 6px solid rgba(255, 255, 255, 0.4);
            border-top: 6px solid rgba(255, 255, 255, 0.4);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="corner-decoration"></div>
        <div class="accent-element"></div>
        
        <div class="content">
            <h1 class="main-title">
                午餐时间<span class="highlight-word">需求变更</span><br>
                <span class="stress-word">崩溃</span>现场
            </h1>
            
            <p class="subtitle">
                产品经理的神操作：下午要完成！
            </p>
            
            <p class="description">
                中午吃饭时突然接到通知，新需求要求今天下午完成，小王内心万分崩溃的真实写照
            </p>
        </div>
        
        <div class="bottom-accent"></div>
    </div>
</body>
</html>