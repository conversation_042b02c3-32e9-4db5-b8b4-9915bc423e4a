<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员的日常</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 50%, #42a5f5 100%);
            border-radius: 20px;
            padding: 40px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .emoji {
            font-size: 120px;
            margin-bottom: 30px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .main-title {
            font-size: 72px;
            font-weight: 900;
            color: white;
            text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
            line-height: 1.1;
            letter-spacing: -2px;
        }
        
        .highlight {
            background: linear-gradient(45deg, #ff4081, #e91e63);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-stroke: 2px white;
            -webkit-text-stroke: 2px white;
            position: relative;
        }
        
        .highlight::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 8px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 4px;
        }
        
        .subtitle {
            font-size: 32px;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .description {
            font-size: 24px;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.85);
            line-height: 1.4;
            margin-bottom: 40px;
            max-width: 90%;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .tag {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }
        
        .tag:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }
        
        .accent-shape {
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            top: -100px;
            right: -100px;
            z-index: 1;
        }
        
        .accent-shape2 {
            position: absolute;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            bottom: -75px;
            left: -75px;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="accent-shape"></div>
        <div class="accent-shape2"></div>
        <div class="content">
            <div class="emoji">💻</div>
            <h1 class="main-title">
                程序员的<br>
                <span class="highlight">日常崩溃</span>
            </h1>
            <p class="subtitle">当代打工人的真实写照</p>
            <p class="description">
                每天上班第一件事：检查昨天的代码<br>
                结果发现服务器挂了，一堆bug等着修复
            </p>
            <div class="tags">
                <span class="tag"># 程序员</span>
                <span class="tag"># 职场生活</span>
                <span class="tag"># 代码人生</span>
            </div>
        </div>
    </div>
</body>
</html>