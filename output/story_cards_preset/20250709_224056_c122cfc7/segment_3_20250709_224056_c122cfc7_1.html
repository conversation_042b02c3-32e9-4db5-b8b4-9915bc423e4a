<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 产品经理的突然袭击</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .card {
            width: 600px;
            height: 800px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            border: 3px solid #333;
        }

        .header {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .segment-badge {
            background: #333;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
            transform: rotate(-2deg);
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-shadow: 2px 2px 0px #fff;
            transform: rotate(1deg);
        }

        .subtitle {
            font-size: 16px;
            color: #555;
            font-style: italic;
        }

        .content-area {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .story-text {
            background: #fff9e6;
            border: 3px dashed #ffb74d;
            border-radius: 15px;
            padding: 25px;
            font-size: 18px;
            line-height: 1.6;
            color: #333;
            position: relative;
            margin-bottom: 20px;
        }

        .story-text::before {
            content: "💭";
            position: absolute;
            top: -10px;
            left: 20px;
            background: #fff9e6;
            padding: 0 10px;
            font-size: 24px;
        }

        .highlight {
            background: #ffeb3b;
            padding: 2px 6px;
            border-radius: 5px;
            font-weight: bold;
            color: #d32f2f;
        }

        .image-container {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }

        .main-image {
            width: 200px;
            height: 150px;
            object-fit: cover;
            border-radius: 15px;
            border: 3px solid #333;
            transform: rotate(-1deg);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .emotion-icons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }

        .emotion-icon {
            font-size: 40px;
            animation: bounce 2s infinite;
        }

        .emotion-icon:nth-child(2) {
            animation-delay: 0.3s;
        }

        .emotion-icon:nth-child(3) {
            animation-delay: 0.6s;
        }

        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 3px solid #333;
        }

        .mood-meter {
            background: #e0e0e0;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            border: 2px solid #333;
        }

        .mood-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff5722, #f44336);
            width: 95%;
            border-radius: 8px;
            animation: pulse 2s infinite;
        }

        .mood-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
            100% {
                opacity: 1;
            }
        }

        .doodle {
            position: absolute;
            font-size: 20px;
            color: #666;
            opacity: 0.3;
        }

        .doodle1 {
            top: 10px;
            right: 20px;
            transform: rotate(15deg);
        }

        .doodle2 {
            bottom: 10px;
            left: 20px;
            transform: rotate(-10deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <div class="segment-badge">第3段</div>
            <h1 class="title">产品经理的突然袭击！</h1>
            <p class="subtitle">当代打工人的日常崩溃现场</p>
        </div>

        <div class="content-area">
            <div class="story-text">
                中午吃饭时，<span class="highlight">产品经理又提出了新的需求变更</span>，要求今天下午就要完成，小王内心万分崩溃。
            </div>

            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/1cb1c073-e871-4af6-893a-c2d8c4bfa187.jpg" alt="小王崩溃现场" class="main-image">
            </div>

            <div class="emotion-icons">
                <span class="emotion-icon">😱</span>
                <span class="emotion-icon">💔</span>
                <span class="emotion-icon">😵</span>
            </div>
        </div>

        <div class="footer">
            <div class="mood-meter">
                <div class="mood-fill"></div>
            </div>
            <div class="mood-label">小王的崩溃指数：95%</div>
        </div>

        <div class="doodle doodle1">⚡</div>
        <div class="doodle doodle2">💥</div>
    </div>
</body>
</html>