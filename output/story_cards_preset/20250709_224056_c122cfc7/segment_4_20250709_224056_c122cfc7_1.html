<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 加班狗的深夜回家路</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ffa726);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: float 20s linear infinite;
        }
        
        @keyframes float {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }
        
        .segment-badge {
            background: rgba(255,255,255,0.9);
            color: #ff6b6b;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .title {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 16px;
            font-weight: 400;
            position: relative;
            z-index: 2;
        }
        
        .content-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .story-content {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            padding: 30px;
            border-radius: 15px;
            border: 3px solid #ff6b6b;
            border-style: dashed;
            position: relative;
            margin-bottom: 30px;
        }
        
        .story-content::before {
            content: '💻';
            position: absolute;
            top: -15px;
            left: 20px;
            background: white;
            padding: 5px 10px;
            font-size: 20px;
            border-radius: 50%;
            border: 3px solid #ff6b6b;
        }
        
        .story-text {
            font-size: 18px;
            line-height: 1.8;
            color: #2c3e50;
            font-weight: 500;
            text-align: center;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #2d3436;
        }
        
        .image-container {
            width: 100%;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            border: 4px solid #ff6b6b;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
        }
        
        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-emoji {
            position: absolute;
            font-size: 24px;
            animation: bounce 3s infinite;
        }
        
        .emoji-1 { top: 20%; left: 10%; animation-delay: 0s; }
        .emoji-2 { top: 60%; right: 15%; animation-delay: 1s; }
        .emoji-3 { bottom: 30%; left: 20%; animation-delay: 2s; }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .mood-indicator {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: #ff6b6b;
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(255,107,107,0.4);
        }
        
        .mood-indicator::before {
            content: '😴';
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <div class="segment-badge">第4段</div>
            <h1 class="title">加班狗的深夜回家路</h1>
            <p class="subtitle">程序员小王的日常修仙记</p>
        </div>
        
        <div class="content-area">
            <div class="story-content">
                <p class="story-text">
                    下午加班到很晚，终于把<span class="highlight">bug修完了</span>，小王拖着疲惫的身体回到家，发誓明天一定要<span class="highlight">早睡</span>。
                </p>
            </div>
            
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/9008aa3d-2b97-431a-b7d2-121a64fa8869.jpg" alt="加班回家的小王">
                <div class="image-overlay">
                    又是一个熬夜修bug的夜晚...
                </div>
            </div>
        </div>
        
        <div class="decorative-elements">
            <div class="floating-emoji emoji-1">🐛</div>
            <div class="floating-emoji emoji-2">💤</div>
            <div class="floating-emoji emoji-3">☕</div>
        </div>
        
        <div class="mood-indicator">
            困到怀疑人生
        </div>
    </div>
</body>
</html>