<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 代码崩溃的早晨</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            border: 3px solid #333;
        }
        
        .card-header {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            padding: 30px;
            text-align: center;
            position: relative;
            flex-shrink: 0;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: repeating-linear-gradient(
                45deg,
                #333,
                #333 2px,
                transparent 2px,
                transparent 8px
            );
            z-index: -1;
            border-radius: 25px;
        }
        
        .segment-badge {
            display: inline-block;
            background: #333;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 15px;
            transform: rotate(-2deg);
        }
        
        .card-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            transform: rotate(1deg);
        }
        
        .card-subtitle {
            font-size: 16px;
            color: #666;
            font-style: italic;
            transform: rotate(-0.5deg);
        }
        
        .card-content {
            flex: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .story-text {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border: 2px dashed #333;
            font-size: 18px;
            line-height: 1.6;
            color: #333;
            position: relative;
            margin-bottom: 20px;
            transform: rotate(-0.5deg);
        }
        
        .story-text::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 10px;
            font-size: 60px;
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .story-text::after {
            content: '"';
            position: absolute;
            bottom: -30px;
            right: 10px;
            font-size: 60px;
            color: #ff6b6b;
            font-weight: bold;
        }
        
        .image-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            margin: 20px 0;
        }
        
        .main-image {
            max-width: 100%;
            max-height: 250px;
            border-radius: 15px;
            border: 3px solid #333;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transform: rotate(1deg);
        }
        
        .decorative-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .bug-icon {
            position: absolute;
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
        }
        
        .bug-icon:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .bug-icon:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 1s;
        }
        
        .bug-icon:nth-child(3) {
            bottom: 15%;
            left: 20%;
            animation-delay: 2s;
        }
        
        .emotion-indicator {
            position: absolute;
            bottom: 20px;
            right: 30px;
            font-size: 40px;
            transform: rotate(-10deg);
            animation: bounce 2s infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(180deg); }
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(-10deg); }
            40% { transform: translateY(-10px) rotate(-10deg); }
            60% { transform: translateY(-5px) rotate(-10deg); }
        }
        
        .footer-doodle {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: repeating-linear-gradient(
                90deg,
                #ff6b6b,
                #ff6b6b 10px,
                #feca57 10px,
                #feca57 20px
            );
            border-top: 2px solid #333;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <div class="segment-badge">第2段</div>
            <h1 class="card-title">代码崩溃的早晨</h1>
            <p class="card-subtitle">程序员的日常惊魂时刻</p>
        </div>
        
        <div class="card-content">
            <div class="story-text">
                到公司后，打开电脑第一件事就是查看昨天的代码，结果发现测试服务器挂了，一堆bug等着修复。
            </div>
            
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/682edf07-8e2e-4521-b45e-ab24ebdf5134.jpg" alt="代码崩溃场景" class="main-image">
                
                <div class="decorative-elements">
                    <div class="bug-icon">🐛</div>
                    <div class="bug-icon">🐛</div>
                    <div class="bug-icon">🐛</div>
                </div>
            </div>
            
            <div class="emotion-indicator">😱</div>
        </div>
        
        <div class="footer-doodle"></div>
    </div>
</body>
</html>