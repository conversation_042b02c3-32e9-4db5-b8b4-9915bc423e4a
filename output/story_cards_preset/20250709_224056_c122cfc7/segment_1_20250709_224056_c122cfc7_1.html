<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #fff9e6 0%, #f0f8ff 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            border: 3px solid #2c3e50;
        }
        
        .header {
            background: linear-gradient(45deg, #3498db, #2980b9);
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="70" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translateX(-50%) translateY(-50%) rotate(0deg); }
            100% { transform: translateX(-50%) translateY(-50%) rotate(360deg); }
        }
        
        .segment-badge {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
            transform: rotate(-2deg);
        }
        
        .title {
            font-size: 28px;
            font-weight: 700;
            color: white;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 16px;
            color: rgba(255,255,255,0.9);
            position: relative;
            z-index: 2;
            font-weight: 300;
        }
        
        .content-area {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .story-content {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            border: 2px dashed #3498db;
            position: relative;
            margin-bottom: 30px;
        }
        
        .story-content::before {
            content: '💤';
            position: absolute;
            top: -15px;
            left: 20px;
            background: #fff;
            padding: 5px 10px;
            font-size: 20px;
            border-radius: 10px;
            border: 2px solid #3498db;
        }
        
        .story-text {
            font-size: 18px;
            line-height: 1.6;
            color: #2c3e50;
            font-weight: 400;
            text-align: justify;
        }
        
        .highlight {
            background: linear-gradient(120deg, #f39c12 0%, #f39c12 100%);
            background-size: 100% 3px;
            background-repeat: no-repeat;
            background-position: 0 85%;
            font-weight: 600;
            color: #e67e22;
        }
        
        .image-container {
            width: 100%;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            border: 3px solid #34495e;
            position: relative;
            transform: rotate(-1deg);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            filter: brightness(1.1) contrast(1.1);
        }
        
        .image-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(52, 152, 219, 0.1) 100%);
        }
        
        .decorative-elements {
            position: absolute;
            top: 50%;
            right: -20px;
            transform: translateY(-50%);
            font-size: 60px;
            opacity: 0.1;
            color: #3498db;
            z-index: 1;
        }
        
        .time-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(231, 76, 60, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            transform: rotate(3deg);
        }
        
        .mood-emoji {
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 40px;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        
        .doodle {
            position: absolute;
            bottom: 30px;
            left: 30px;
            width: 50px;
            height: 50px;
            opacity: 0.3;
        }
        
        .doodle::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            background: #3498db;
            top: 20px;
            border-radius: 2px;
            transform: rotate(-15deg);
        }
        
        .doodle::after {
            content: '';
            position: absolute;
            width: 2px;
            height: 30px;
            background: #3498db;
            left: 20px;
            top: 10px;
            border-radius: 2px;
            transform: rotate(20deg);
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="time-indicator">⏰ 早上8点</div>
        <div class="header">
            <div class="segment-badge">第1段</div>
            <h1 class="title">程序员小王的日常</h1>
            <p class="subtitle">又是一个"充满挑战"的早晨</p>
        </div>
        
        <div class="content-area">
            <div class="story-content">
                <p class="story-text">
                    早上8点，<span class="highlight">闹钟响了三遍才起床</span>，匆忙洗漱后发现又要迟到了。这就是我们熟悉的程序员小王，每天都在与时间赛跑的路上！
                </p>
            </div>
            
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/576b1a98-2d69-490d-afa2-aeab2540355c.jpg" alt="程序员小王的早晨">
            </div>
        </div>
        
        <div class="decorative-elements">⚡</div>
        <div class="mood-emoji">😴</div>
        <div class="doodle"></div>
    </div>
</body>
</html>