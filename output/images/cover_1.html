<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故事集锦封面</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: #FFFFFF;
            width: 600px;
            height: 800px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        
        .cover-container {
            width: 550px;
            height: 750px;
            background: #FAFAFA;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .doodle-star {
            position: absolute;
            color: #4ECDC4;
            font-size: 20px;
            opacity: 0.6;
        }
        
        .star1 { top: 50px; left: 80px; transform: rotate(15deg); }
        .star2 { top: 120px; right: 60px; transform: rotate(-20deg); }
        .star3 { bottom: 80px; left: 100px; transform: rotate(25deg); }
        .star4 { bottom: 150px; right: 90px; transform: rotate(-15deg); }
        
        .main-title {
            font-size: 48px;
            color: #333333;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.2;
        }
        
        .title-underline {
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
            opacity: 0.8;
        }
        
        .subtitle {
            font-size: 28px;
            color: #666666;
            margin-bottom: 40px;
            position: relative;
            letter-spacing: 0.02em;
            line-height: 1.6;
        }
        
        .content-highlight {
            font-size: 36px;
            color: #FF6B6B;
            font-weight: 700;
            margin-bottom: 30px;
            position: relative;
        }
        
        .circle-doodle {
            position: absolute;
            top: -15px;
            right: -20px;
            width: 25px;
            height: 25px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.7;
        }
        
        .description {
            font-size: 20px;
            color: #666666;
            margin-bottom: 50px;
            max-width: 400px;
            line-height: 1.6;
            letter-spacing: 0.02em;
        }
        
        .emoji-decoration {
            font-size: 24px;
            color: #FF6B6B;
            margin: 0 10px;
        }
        
        .hand-drawn-arrow {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 40px;
            opacity: 0.6;
        }
        
        .arrow-path {
            stroke: #4ECDC4;
            stroke-width: 2;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px solid #E0E0E0;
            transform: rotate(-2deg);
        }
        
        .note1 {
            top: 80px;
            left: 50px;
            transform: rotate(3deg);
        }
        
        .note2 {
            bottom: 50px;
            right: 50px;
            transform: rotate(-3deg);
        }
        
        .divider {
            width: 200px;
            height: 2px;
            background: linear-gradient(to right, transparent, #FF6B6B, transparent);
            margin: 20px 0;
            opacity: 0.6;
        }
        
        .exclamation {
            position: absolute;
            font-size: 18px;
            color: #FF6B6B;
            font-weight: bold;
        }
        
        .exc1 { top: 200px; left: 120px; transform: rotate(10deg); }
        .exc2 { top: 400px; right: 100px; transform: rotate(-15deg); }
        
        .question {
            position: absolute;
            font-size: 16px;
            color: #4ECDC4;
            font-weight: bold;
        }
        
        .q1 { top: 300px; left: 80px; transform: rotate(-8deg); }
        .q2 { bottom: 200px; right: 120px; transform: rotate(12deg); }
    </style>
</head>
<body>
    <div class="cover-container">
        <div class="doodle-star star1">★</div>
        <div class="doodle-star star2">✦</div>
        <div class="doodle-star star3">✧</div>
        <div class="doodle-star star4">⭐</div>
        
        <div class="annotation note1">精彩！</div>
        <div class="annotation note2">必看～</div>
        
        <div class="exclamation exc1">!</div>
        <div class="exclamation exc2">!!</div>
        <div class="question q1">?</div>
        <div class="question q2">?</div>
        
        <div class="main-title">
            故事集锦
            <div class="title-underline"></div>
        </div>
        
        <div class="divider"></div>
        
        <div class="subtitle">
            <span class="emoji-decoration">📖</span>
            生活中的精彩瞬间
            <span class="emoji-decoration">✨</span>
        </div>
        
        <div class="content-highlight">
            4个精彩片段
            <div class="circle-doodle"></div>
        </div>
        
        <div class="description">
            每一个故事都是生活的小确幸<br>
            每一个片段都值得细细品味
        </div>
        
        <svg class="hand-drawn-arrow" viewBox="0 0 60 40">
            <path class="arrow-path" d="M10,20 Q30,5 50,20 M45,15 L50,20 L45,25" />
        </svg>
    </div>
</body>
</html>