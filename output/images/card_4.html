<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技圈八卦 - OpenAI vs 马斯克</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 2rem;
            box-shadow: 
                0 2rem 4rem rgba(102, 126, 234, 0.15),
                0 0.5rem 1rem rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 0.25rem;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
        }
        
        .header {
            padding: 2.5rem 2.5rem 1.5rem;
            text-align: center;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 255, 0.9));
        }
        
        .emoji-icon {
            font-size: 3rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 0.5rem;
            line-height: 1.3;
        }
        
        .subtitle {
            font-size: 0.9rem;
            color: #718096;
            font-weight: 400;
        }
        
        .content {
            flex: 1;
            padding: 0 2.5rem;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            overflow-y: auto;
        }
        
        .news-section {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 1rem;
            padding: 1.5rem;
            border-left: 0.25rem solid #4299e1;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .news-text {
            font-size: 0.95rem;
            line-height: 1.6;
            color: #4a5568;
            margin-bottom: 1rem;
        }
        
        .quote {
            background: rgba(255, 243, 224, 0.8);
            border-radius: 0.75rem;
            padding: 1rem;
            border-left: 0.25rem solid #ed8936;
            font-style: italic;
            color: #744210;
            margin: 1rem 0;
        }
        
        .divider {
            height: 0.125rem;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 1rem 0;
        }
        
        .opinion-section {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 1rem;
            padding: 1.5rem;
            text-align: center;
        }
        
        .highlight-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .question {
            font-size: 0.95rem;
            color: #4a5568;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .tags {
            padding: 1.5rem 2.5rem 2rem;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .emoji {
            font-size: 1.1em;
            margin: 0 0.1rem;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <span class="emoji-icon">⚡</span>
            <h1 class="title">科技圈八卦</h1>
            <p class="subtitle">OpenAI vs 马斯克最新进展</p>
        </div>
        
        <div class="content">
            <div class="news-section">
                <h2 class="section-title">
                    <span class="emoji">📅</span>
                    最新进展：
                </h2>
                <p class="news-text">
                    虽然马斯克申请的禁令被法官拒了，但案子还是会在今年秋天开庭！法官说这事儿关乎公共利益，必须好好审一审 <span class="emoji">⚖️</span>
                </p>
                <div class="quote">
                    OpenAI官方回应也很有意思，说什么"结构变了但使命不变"...emmm，这话听着怎么这么熟悉呢 <span class="emoji">🤷‍♀️</span>
                </div>
            </div>
            
            <div class="divider"></div>
            
            <div class="opinion-section">
                <p class="highlight-text">
                    理想很丰满，现实很骨感啊 <span class="emoji">✨</span>
                </p>
                <p class="question">
                    说实话，看着这些科技巨头们的宫斗大戏，我只想说一句：
                </p>
                <p class="question">
                    你们觉得这次OpenAI会怎么收场呢？评论区聊聊吧～
                </p>
            </div>
        </div>
        
        <div class="tags">
            <span class="tag">#科技圈八卦</span>
            <span class="tag">#OpenAI</span>
            <span class="tag">#人工智能</span>
            <span class="tag">#马斯克</span>
            <span class="tag">#职场那些事</span>
        </div>
    </div>
</body>
</html>