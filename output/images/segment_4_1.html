<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第4段 - 故事卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Comic Sans MS', 'Kalam', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            width: 600px;
            height: 800px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .card {
            width: 550px;
            height: 750px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            font-weight: 700;
            position: relative;
            margin-bottom: 15px;
        }
        
        .segment-title::after {
            content: "✨";
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 25px;
            align-items: center;
        }
        
        .image-container {
            width: 300px;
            height: 200px;
            border-radius: 12px;
            overflow: hidden;
            border: 2px solid #4ECDC4;
            position: relative;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: -15px;
            right: -10px;
            background: #FFFFFF;
            color: #666666;
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px dashed #4ECDC4;
            transform: rotate(5deg);
        }
        
        .story-content {
            background: #FAFAFA;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #E0E0E0;
            position: relative;
            max-width: 450px;
            text-align: center;
        }
        
        .story-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.6;
            letter-spacing: 0.02em;
            position: relative;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
            top: -10px;
            right: -20px;
            animation: float 3s ease-in-out infinite;
        }
        
        .doodle-elements {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .doodle-star {
            position: absolute;
            color: #FF6B6B;
            font-size: 16px;
            animation: twinkle 2s ease-in-out infinite;
        }
        
        .doodle-star:nth-child(1) {
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .doodle-star:nth-child(2) {
            top: 20%;
            right: 15%;
            animation-delay: 0.5s;
        }
        
        .doodle-star:nth-child(3) {
            bottom: 15%;
            left: 20%;
            animation-delay: 1s;
        }
        
        .emotion-bubble {
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #FFFFFF;
            color: #FF6B6B;
            font-size: 24px;
            padding: 8px 12px;
            border-radius: 50%;
            border: 2px solid #FF6B6B;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            animation: bounce 2s ease-in-out infinite;
        }
        
        .tired-annotation {
            position: absolute;
            bottom: 10px;
            right: 10px;
            font-size: 14px;
            color: #666666;
            transform: rotate(-3deg);
            background: #FFFFFF;
            padding: 5px 8px;
            border-radius: 6px;
            border: 1px dashed #E0E0E0;
        }
        
        .sleep-promise {
            position: absolute;
            top: 10px;
            left: 10px;
            font-size: 12px;
            color: #4ECDC4;
            background: #FFFFFF;
            padding: 4px 8px;
            border-radius: 6px;
            border: 1px solid #4ECDC4;
            transform: rotate(2deg);
        }
        
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateX(-50%) translateY(0px); }
            50% { transform: translateX(-50%) translateY(-5px); }
        }
        
        .hand-drawn-line {
            position: absolute;
            bottom: 60px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: repeating-linear-gradient(
                90deg,
                #4ECDC4 0px,
                #4ECDC4 5px,
                transparent 5px,
                transparent 10px
            );
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="doodle-elements">
            <div class="doodle-star">★</div>
            <div class="doodle-star">✦</div>
            <div class="doodle-star">✧</div>
        </div>
        
        <div class="segment-title">第4段</div>
        
        <div class="content-area">
            <div class="image-container">
                <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/64e6c1a7-802d-4c10-8b27-33eecbcc8ede.jpg" alt="加班场景">
                <div class="image-annotation">深夜加班ing</div>
            </div>
            
            <div class="story-content">
                <div class="story-text">
                    下午加班到很晚，终于把<span class="highlight">bug修完了</span>，小王拖着疲惫的身体回到家，发誓明天一定要<span class="highlight">早睡</span>。
                </div>
                <div class="emphasis-circle"></div>
                <div class="emotion-bubble">😴</div>
                <div class="tired-annotation">又是熬夜的一天</div>
                <div class="sleep-promise">明天早睡！</div>
            </div>
        </div>
        
        <div class="hand-drawn-line"></div>
    </div>
</body>
</html>