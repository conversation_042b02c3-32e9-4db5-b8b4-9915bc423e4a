<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>马斯克与OpenAI的恩怨情仇</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #ffffff;
            border-radius: 2rem;
            box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .card-header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 3rem 2.5rem 2rem;
            color: white;
            position: relative;
        }
        
        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="70" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .card-title {
            font-size: 2.2rem;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }
        
        .emoji-large {
            font-size: 3rem;
            margin-right: 1rem;
            vertical-align: middle;
        }
        
        .card-content {
            flex: 1;
            padding: 3rem 2.5rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .content-section {
            margin-bottom: 2.5rem;
        }
        
        .content-section:last-child {
            margin-bottom: 0;
        }
        
        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2c3e50;
            margin-bottom: 1.5rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.3rem 0.6rem;
            border-radius: 0.5rem;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .emoji {
            font-size: 1.3rem;
            margin: 0 0.3rem;
        }
        
        .stats-container {
            background: #f8f9fa;
            border-radius: 1.5rem;
            padding: 2rem;
            margin-top: 1.5rem;
            border-left: 0.4rem solid #ff6b6b;
        }
        
        .stats-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1rem;
            color: #495057;
        }
        
        .stats-item:last-child {
            margin-bottom: 0;
        }
        
        .stats-number {
            background: #ff6b6b;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 2rem;
            font-weight: 600;
            margin-right: 1rem;
            min-width: 3rem;
            text-align: center;
        }
        
        .timeline {
            background: #e9ecef;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.8rem;
            font-size: 0.95rem;
            color: #495057;
        }
        
        .timeline-item:last-child {
            margin-bottom: 0;
        }
        
        .timeline-dot {
            width: 0.8rem;
            height: 0.8rem;
            background: #ff6b6b;
            border-radius: 50%;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .card-footer {
            background: #f8f9fa;
            padding: 1.5rem 2.5rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer-text {
            color: #6c757d;
            font-size: 0.9rem;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="card-header">
            <h1 class="card-title">
                <span class="emoji-large">🤔</span>马斯克为什么这么生气？
            </h1>
        </div>
        
        <div class="card-content">
            <div class="content-section">
                <p class="content-text">
                    老马可是<span class="highlight">OpenAI的联合创始人之一</span>！2018年离开后就一直在diss现任CEO奥尔特曼。这次更是直接上法庭，说自己投了<span class="highlight">4500万美元</span>结果被"背叛"了 <span class="emoji">😤</span>
                </p>
                
                <div class="stats-container">
                    <div class="stats-item">
                        <span class="stats-number">4500万</span>
                        <span>美元投资金额</span>
                    </div>
                    <div class="stats-item">
                        <span class="stats-number">2018</span>
                        <span>离开OpenAI董事会</span>
                    </div>
                </div>
            </div>
            
            <div class="content-section">
                <p class="content-text">
                    而且这已经不是第一次了！去年2月告过一次，6月撤诉，8月又重新告...这执着程度也是没谁了 <span class="emoji">🙄</span>
                </p>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <span>2023年2月：首次起诉</span>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <span>2023年6月：撤回诉讼</span>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-dot"></div>
                        <span>2023年8月：重新起诉</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card-footer">
            <p class="footer-text">科技圈的恩怨情仇，永远比电视剧还精彩</p>
        </div>
    </div>
</body>
</html>