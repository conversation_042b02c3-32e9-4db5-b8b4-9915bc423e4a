<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第2段 - 代码人生</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 600px;
            height: 800px;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .card {
            width: 550px;
            height: 750px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 10px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: "✨";
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            color: #4ECDC4;
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #E0E0E0;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .content-text {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            text-align: center;
            padding: 20px;
            background: rgba(250, 250, 250, 0.5);
            border-radius: 10px;
            border: 1px solid #E0E0E0;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .highlight {
            background: linear-gradient(transparent 60%, #FF6B6B 60%, #FF6B6B 80%, transparent 80%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            top: -10px;
            left: 50px;
            transform: rotate(-15deg);
            opacity: 0.7;
        }
        
        .doodle-elements {
            position: absolute;
            top: 100px;
            right: 30px;
            font-size: 24px;
            color: #FF6B6B;
            transform: rotate(15deg);
        }
        
        .bug-emoji {
            position: absolute;
            bottom: 20px;
            left: 30px;
            font-size: 20px;
            color: #FF6B6B;
        }
        
        .computer-doodle {
            position: absolute;
            bottom: 30px;
            right: 40px;
            width: 40px;
            height: 30px;
            border: 2px solid #4ECDC4;
            border-radius: 5px;
            background: transparent;
        }
        
        .computer-doodle::before {
            content: "";
            position: absolute;
            width: 20px;
            height: 2px;
            background: #4ECDC4;
            bottom: -8px;
            left: 10px;
        }
        
        .hand-drawn-arrow {
            position: absolute;
            top: 200px;
            left: 20px;
            width: 80px;
            height: 40px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 40'%3E%3Cpath d='M10 20 Q40 10 60 20 L55 15 M60 20 L55 25' stroke='%23FF6B6B' stroke-width='2' fill='none' stroke-linecap='round'/%3E%3C/svg%3E") no-repeat;
            background-size: contain;
        }
        
        .thought-bubble {
            position: absolute;
            top: 50px;
            right: 80px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            color: #666666;
            border: 1px dashed #4ECDC4;
        }
        
        .thought-bubble::before {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 20px;
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 10px solid rgba(255, 255, 255, 0.9);
        }
        
        .footer-note {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666666;
            font-style: italic;
        }
        
        .stress-marks {
            position: absolute;
            top: 380px;
            right: 60px;
            font-size: 16px;
            color: #FF6B6B;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="segment-title">第2段</div>
        
        <div class="thought-bubble">又是忙碌的一天</div>
        
        <div class="image-container">
            <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/092d142e-465c-4d87-ab01-b60ca375f5b9.jpg" alt="工作场景">
            <div class="image-annotation">现实很骨感</div>
        </div>
        
        <div class="content-text">
            <div class="emphasis-circle"></div>
            到公司后，打开电脑第一件事就是查看昨天的代码，结果发现<span class="highlight">测试服务器挂了</span>，一堆bug等着修复。
        </div>
        
        <div class="doodle-elements">！？</div>
        <div class="hand-drawn-arrow"></div>
        <div class="bug-emoji">🐛🐛🐛</div>
        <div class="computer-doodle"></div>
        <div class="stress-marks">💥💥</div>
        
        <div class="footer-note">程序员的日常 · 永远有修不完的bug</div>
    </div>
</body>
</html>