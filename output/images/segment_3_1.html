<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第3段 - 故事情节卡片</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FAFAFA 0%, #FFFFFF 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .card-container {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .segment-title {
            font-size: 28px;
            color: #FF6B6B;
            text-align: center;
            margin-bottom: 20px;
            position: relative;
            letter-spacing: 0.02em;
        }
        
        .segment-title::after {
            content: '✨';
            position: absolute;
            right: -30px;
            top: -5px;
            font-size: 20px;
            animation: twinkle 2s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
        
        .image-container {
            width: 100%;
            height: 280px;
            margin: 20px 0;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .image-annotation {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            color: #666666;
            border: 1px dashed #4ECDC4;
            transform: rotate(-2deg);
        }
        
        .content-area {
            flex: 1;
            padding: 20px 0;
        }
        
        .story-content {
            font-size: 18px;
            line-height: 1.6;
            color: #333333;
            letter-spacing: 0.02em;
            position: relative;
            background: rgba(250, 250, 250, 0.3);
            padding: 20px;
            border-radius: 10px;
            border-left: 3px solid #4ECDC4;
        }
        
        .highlight {
            background: linear-gradient(120deg, transparent 0%, transparent 40%, #FF6B6B 40%, #FF6B6B 60%, transparent 60%);
            padding: 2px 4px;
            border-radius: 3px;
            position: relative;
        }
        
        .emphasis-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px dashed #FF6B6B;
            border-radius: 50%;
            right: -20px;
            top: 50%;
            transform: translateY(-50%) rotate(15deg);
            opacity: 0.7;
        }
        
        .emotion-icon {
            display: inline-block;
            font-size: 20px;
            margin: 0 5px;
            animation: bounce 1.5s infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-3px); }
        }
        
        .doodle-element {
            position: absolute;
            color: #4ECDC4;
            font-size: 16px;
            opacity: 0.6;
        }
        
        .doodle-1 {
            top: 150px;
            left: 20px;
            transform: rotate(-15deg);
        }
        
        .doodle-2 {
            bottom: 100px;
            right: 30px;
            transform: rotate(20deg);
        }
        
        .doodle-3 {
            top: 400px;
            left: 50px;
            transform: rotate(-10deg);
        }
        
        .annotation-note {
            position: absolute;
            bottom: 20px;
            right: 30px;
            background: #FAFAFA;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            color: #666666;
            border: 1px solid #E0E0E0;
            transform: rotate(2deg);
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .annotation-note::before {
            content: '📝';
            margin-right: 5px;
        }
        
        .divider-line {
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, #4ECDC4 20%, #4ECDC4 80%, transparent 100%);
            margin: 15px 0;
            position: relative;
            transform: rotate(-0.5deg);
        }
        
        .divider-line::after {
            content: '★';
            position: absolute;
            right: 10px;
            top: -8px;
            color: #FF6B6B;
            font-size: 14px;
        }
        
        .corner-decoration {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px dashed #4ECDC4;
            border-radius: 50%;
        }
        
        .corner-decoration.top-left {
            top: 10px;
            left: 10px;
            opacity: 0.3;
        }
        
        .corner-decoration.bottom-right {
            bottom: 10px;
            right: 10px;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="card-container">
        <div class="corner-decoration top-left"></div>
        <div class="corner-decoration bottom-right"></div>
        
        <div class="doodle-element doodle-1">◯</div>
        <div class="doodle-element doodle-2">✦</div>
        <div class="doodle-element doodle-3">◇</div>
        
        <h1 class="segment-title">第3段</h1>
        
        <div class="image-container">
            <img src="https://ai-creator-1317512395.cos.ap-guangzhou.myqcloud.com/intelligent_assistant/38dda4cd-b906-4152-8645-ee48c9637aef.jpg" alt="故事情节配图">
            <div class="image-annotation">故事现场</div>
        </div>
        
        <div class="divider-line"></div>
        
        <div class="content-area">
            <div class="story-content">
                中午吃饭时，<span class="highlight">产品经理</span>又提出了新的需求变更，要求<span class="highlight">今天下午就要完成</span><span class="emotion-icon">😱</span>，小王内心万分崩溃<span class="emotion-icon">💔</span>。
                <div class="emphasis-circle"></div>
            </div>
        </div>
        
        <div class="annotation-note">
            职场日常，感同身受！
        </div>
    </div>
</body>
</html>