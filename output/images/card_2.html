<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI内部风波解析</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #ffffff;
            border-radius: 2rem;
            box-shadow: 0 2rem 4rem rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            padding: 2.5rem 2rem 2rem;
            color: white;
            position: relative;
        }
        
        .header::after {
            content: '';
            position: absolute;
            bottom: -1rem;
            left: 0;
            right: 0;
            height: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            clip-path: polygon(0 0, 100% 0, 85% 100%, 15% 100%);
        }
        
        .title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2);
        }
        
        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content {
            flex: 1;
            padding: 3rem 2rem 2rem;
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 2.5rem;
        }
        
        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 1.2rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .emoji {
            font-size: 1.6rem;
        }
        
        .section-content {
            font-size: 1rem;
            line-height: 1.7;
            color: #636e72;
            margin-bottom: 1.5rem;
        }
        
        .highlight {
            background: linear-gradient(120deg, #ffeaa7 0%, #fdcb6e 100%);
            padding: 0.3rem 0.6rem;
            border-radius: 0.5rem;
            font-weight: 500;
            color: #2d3436;
            display: inline-block;
            margin: 0.2rem 0;
        }
        
        .points-list {
            list-style: none;
            padding: 0;
        }
        
        .point-item {
            background: #f8f9fa;
            margin-bottom: 1rem;
            padding: 1.2rem;
            border-radius: 1rem;
            border-left: 0.4rem solid #74b9ff;
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .point-item:hover {
            transform: translateX(0.3rem);
        }
        
        .point-number {
            font-size: 1.1rem;
            font-weight: 600;
            color: #0984e3;
            margin-bottom: 0.5rem;
        }
        
        .point-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 0.5rem;
        }
        
        .point-desc {
            font-size: 0.95rem;
            color: #636e72;
            line-height: 1.6;
        }
        
        .decorative-element {
            position: absolute;
            top: 1rem;
            right: 1rem;
            width: 4rem;
            height: 4rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <div class="decorative-element"></div>
            <h1 class="title">OpenAI内部风波解析</h1>
            <p class="subtitle">前员工集体发声，揭露公司转型背后的真相</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2 class="section-title">
                    <span class="emoji">💥</span>
                    事情经过是这样的：
                </h2>
                <div class="section-content">
                    这些前员工都不是小角色哦，人家可都是技术大牛和管理层！他们在法庭文件里直接开撕，说OpenAI现在的重组计划完全背离了当初的非营利初心。
                </div>
                <div class="highlight">
                    当初说好做慈善，现在却想着赚大钱 💰
                </div>
            </div>
            
            <div class="section">
                <h2 class="section-title">
                    <span class="emoji">🎯</span>
                    核心矛盾在哪里？
                </h2>
                <ul class="points-list">
                    <li class="point-item">
                        <div class="point-number">1️⃣</div>
                        <div class="point-title">投资者施压</div>
                        <div class="point-desc">现在投资方疯狂push公司转型营利，钱味儿太重了</div>
                    </li>
                    <li class="point-item">
                        <div class="point-number">2️⃣</div>
                        <div class="point-title">人才流失</div>
                        <div class="point-desc">很多技术大神当初就是冲着"改变世界"的理想来的，现在感觉被骗了</div>
                    </li>
                    <li class="point-item">
                        <div class="point-number">3️⃣</div>
                        <div class="point-title">使命变质</div>
                        <div class="point-desc">从"让AI造福全人类"变成"让AI给股东赚钱"</div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>