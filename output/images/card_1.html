<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI内部大地震</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 50%, #ffa8a8 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: linear-gradient(145deg, #ffffff 0%, #fafafa 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 40px 35px;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #ff6b6b, #ff8e8e, #4ecdc4, #45b7d1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .shock-badge {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }
        
        .main-title {
            font-size: 48px;
            font-weight: 900;
            line-height: 1.2;
            color: #2c3e50;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .highlight-1 {
            color: #ff6b6b;
            text-shadow: 2px 2px 0px #ffffff, -1px -1px 0px #ff8e8e;
            font-size: 52px;
        }
        
        .highlight-2 {
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 52px;
            text-shadow: 0 2px 10px rgba(78, 205, 196, 0.3);
        }
        
        .highlight-3 {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 52px;
        }
        
        .summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 16px;
            margin: 25px 0;
            border-left: 5px solid #ff6b6b;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }
        
        .summary-text {
            font-size: 18px;
            font-weight: 500;
            color: #495057;
            line-height: 1.6;
            text-align: center;
        }
        
        .key-points {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .point {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #4ecdc4;
        }
        
        .point-icon {
            font-size: 24px;
            margin-right: 15px;
            width: 40px;
            text-align: center;
        }
        
        .point-text {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
            flex: 1;
        }
        
        .footer {
            text-align: center;
            margin-top: 20px;
        }
        
        .cta {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 700;
            display: inline-block;
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
            text-decoration: none;
        }
        
        .tags {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .tag {
            background: rgba(78, 205, 196, 0.1);
            color: #4ecdc4;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <div class="shock-badge">🔥 科技圈爆料</div>
            <h1 class="main-title">
                <span class="highlight-1">12名</span>前员工集体<span class="highlight-2">倒戈</span><br>
                <span class="highlight-3">OpenAI</span>内部大地震！
            </h1>
        </div>
        
        <div class="summary">
            <p class="summary-text">
                前员工力挺马斯克告老东家，非营利初心变质成赚钱工具
            </p>
        </div>
        
        <div class="key-points">
            <div class="point">
                <div class="point-icon">💥</div>
                <div class="point-text">技术大牛和管理层集体开撕，直指重组计划背离初心</div>
            </div>
            
            <div class="point">
                <div class="point-icon">💰</div>
                <div class="point-text">投资者疯狂施压转型营利，理想与现实激烈碰撞</div>
            </div>
            
            <div class="point">
                <div class="point-icon">⚖️</div>
                <div class="point-text">马斯克4500万投资"被背叛"，秋季法庭见分晓</div>
            </div>
            
            <div class="point">
                <div class="point-icon">🎯</div>
                <div class="point-text">从"造福全人类"到"给股东赚钱"的使命变质</div>
            </div>
        </div>
        
        <div class="footer">
            <div class="cta">点击查看完整内幕 →</div>
            <div class="tags">
                <span class="tag">#OpenAI</span>
                <span class="tag">#马斯克</span>
                <span class="tag">#科技八卦</span>
                <span class="tag">#AI风云</span>
            </div>
        </div>
    </div>
</body>
</html>