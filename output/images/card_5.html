<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个性化定制时代</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #FF6B9D 0%, #FFD93D 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            padding: 24px;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(135deg, #FF6B9D 0%, #FFD93D 100%);
        }

        .header {
            text-align: center;
            margin-top: 40px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 40px 20px;
        }

        .title {
            font-size: 32px;
            font-weight: 700;
            color: #2C2C2C;
            line-height: 1.6;
            margin-bottom: 30px;
            position: relative;
        }

        .title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(135deg, #FF6B9D 0%, #FFD93D 100%);
            border-radius: 2px;
        }

        .emoji {
            font-size: 48px;
            margin: 20px 0;
            display: block;
        }

        .highlight-text {
            font-size: 20px;
            font-weight: 600;
            color: #FF6B9D;
            margin: 20px 0;
            text-shadow: 0 2px 4px rgba(255, 107, 157, 0.2);
        }

        .tags {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
            margin-top: 40px;
        }

        .tag {
            background: linear-gradient(135deg, #FF6B9D 0%, #FFD93D 100%);
            color: #FFFFFF;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3);
            transition: transform 0.3s ease;
        }

        .tag:hover {
            transform: translateY(-2px);
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            opacity: 0.1;
        }

        .circle-1 {
            width: 120px;
            height: 120px;
            background: #FF6B9D;
            top: 10%;
            right: -60px;
        }

        .circle-2 {
            width: 80px;
            height: 80px;
            background: #FFD93D;
            bottom: 20%;
            left: -40px;
        }

        .circle-3 {
            width: 60px;
            height: 60px;
            background: #6BCF7F;
            top: 30%;
            left: 10%;
        }

        .question-mark {
            position: absolute;
            top: 50%;
            right: 20px;
            font-size: 80px;
            color: #FFD93D;
            opacity: 0.1;
            font-weight: 700;
            transform: translateY(-50%) rotate(15deg);
        }

        .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #F0F0F0;
            margin-top: 20px;
        }

        .footer-text {
            font-size: 12px;
            color: #666666;
            line-height: 1.6;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .emoji {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="decorative-elements">
            <div class="circle circle-1"></div>
            <div class="circle circle-2"></div>
            <div class="circle circle-3"></div>
            <div class="question-mark">?</div>
        </div>
        
        <div class="header">
            <div class="highlight-text">个性化定制时代</div>
        </div>
        
        <div class="main-content">
            <div class="title">
                个性化定制的时代<br>真的来了
            </div>
            
            <span class="emoji">💫</span>
            
            <div class="title" style="font-size: 24px; margin-bottom: 0;">
                你们准备好拥抱这个<br>充满可能性的未来了吗？
            </div>
        </div>
        
        <div class="tags">
            <span class="tag">#AI科技</span>
            <span class="tag">#个性化定制</span>
            <span class="tag">#商业趋势</span>
            <span class="tag">#科技生活</span>
            <span class="tag">#未来已来</span>
        </div>
        
        <div class="footer">
            <div class="footer-text">
                拥抱变化 · 创造未来
            </div>
        </div>
    </div>
</body>
</html>