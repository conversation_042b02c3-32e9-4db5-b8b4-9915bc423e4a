<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>程序员小王的一天</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&display=swap');
        
        body {
            margin: 0;
            padding: 0;
            font-family: 'Kalam', 'Comic Sans MS', cursive;
            background: linear-gradient(135deg, #FFFFFF 0%, #FAFAFA 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        
        .card {
            width: 600px;
            height: 800px;
            background: #FFFFFF;
            border: 2px dashed #FF6B6B;
            border-radius: 15px;
            padding: 25px;
            margin: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        
        .header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }
        
        .main-title {
            font-size: 32px;
            color: #333333;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.4;
            letter-spacing: 0.02em;
            position: relative;
            display: inline-block;
        }
        
        .main-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 3px;
            background: #FF6B6B;
            border-radius: 2px;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666666;
            margin-bottom: 15px;
            font-weight: 400;
            line-height: 1.6;
        }
        
        .time-badge {
            background: #4ECDC4;
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 16px;
            display: inline-block;
            margin-bottom: 30px;
            transform: rotate(-2deg);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .story-content {
            background: #FAFAFA;
            padding: 30px;
            border-radius: 15px;
            border: 1px solid #E0E0E0;
            margin-bottom: 40px;
            position: relative;
        }
        
        .story-text {
            font-size: 18px;
            color: #333333;
            line-height: 1.8;
            letter-spacing: 0.02em;
            text-align: center;
        }
        
        .highlight {
            background: linear-gradient(120deg, #FF6B6B 0%, #FF6B6B 100%);
            background-repeat: no-repeat;
            background-size: 100% 0.2em;
            background-position: 0 88%;
            padding: 2px 4px;
            border-radius: 3px;
            color: #333333;
        }
        
        .doodle-elements {
            position: absolute;
        }
        
        .alarm-icon {
            top: 120px;
            right: 80px;
            font-size: 24px;
            color: #FF6B6B;
            transform: rotate(15deg);
        }
        
        .sleep-icon {
            top: 180px;
            left: 60px;
            font-size: 20px;
            color: #4ECDC4;
            transform: rotate(-10deg);
        }
        
        .rush-icon {
            bottom: 200px;
            right: 100px;
            font-size: 22px;
            color: #FF6B6B;
            transform: rotate(8deg);
        }
        
        .annotation {
            position: absolute;
            font-size: 14px;
            color: #666666;
            background: #FFFFFF;
            padding: 5px 10px;
            border-radius: 8px;
            border: 1px dashed #E0E0E0;
            transform: rotate(-3deg);
        }
        
        .annotation-1 {
            top: 280px;
            left: 40px;
        }
        
        .annotation-2 {
            bottom: 150px;
            right: 40px;
            transform: rotate(2deg);
        }
        
        .hand-drawn-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border: 2px solid #4ECDC4;
            border-radius: 50%;
            opacity: 0.3;
        }
        
        .circle-1 {
            top: 100px;
            left: 40px;
            transform: rotate(15deg);
        }
        
        .circle-2 {
            bottom: 100px;
            left: 80px;
            transform: rotate(-20deg);
        }
        
        .footer-note {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #666666;
            text-align: center;
        }
        
        .exclamation {
            color: #FF6B6B;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .question {
            color: #4ECDC4;
            font-weight: bold;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <h1 class="main-title">程序员小王的一天</h1>
            <div class="time-badge">早上8点</div>
            <p class="subtitle">又是一个匆忙的早晨...</p>
        </div>
        
        <div class="story-content">
            <p class="story-text">
                <span class="highlight">闹钟响了三遍</span>才起床<span class="exclamation">！</span><br>
                匆忙洗漱后发现<br>
                <span class="highlight">又要迟到了</span><span class="question">？</span>
            </p>
        </div>
        
        <div class="doodle-elements alarm-icon">⏰</div>
        <div class="doodle-elements sleep-icon">😴</div>
        <div class="doodle-elements rush-icon">🏃‍♂️</div>
        
        <div class="annotation annotation-1">赖床专家</div>
        <div class="annotation annotation-2">时间管理大师</div>
        
        <div class="hand-drawn-circle circle-1"></div>
        <div class="hand-drawn-circle circle-2"></div>
        
        <div class="footer-note">
            每个程序员的日常 ✨
        </div>
    </div>
</body>
</html>