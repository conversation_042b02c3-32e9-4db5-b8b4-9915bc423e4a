"""
快速验证工具注册和基础功能
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入所有工具模块以触发注册
print("导入工具模块...")
try:
    import tools.input.text_input
    print("✅ text_input 导入成功")
except Exception as e:
    print(f"❌ text_input 导入失败: {e}")

try:
    import tools.input.csv_reader
    print("✅ csv_reader 导入成功")
except Exception as e:
    print(f"❌ csv_reader 导入失败: {e}")

try:
    import tools.style_converters.xiaohongshu_style
    print("✅ xiaohongshu_style 导入成功")
except Exception as e:
    print(f"❌ xiaohongshu_style 导入失败: {e}")

try:
    import tools.content_processors.text_segmenter
    print("✅ text_segmenter 导入成功")
except Exception as e:
    print(f"❌ text_segmenter 导入失败: {e}")

try:
    import tools.generators.html_generator
    print("✅ html_generator 导入成功")
except Exception as e:
    print(f"❌ html_generator 导入失败: {e}")

try:
    import tools.output.image_converter
    print("✅ image_converter 导入成功")
except Exception as e:
    print(f"❌ image_converter 导入失败: {e}")

print("\n检查工具注册...")
from tools.base.tool_registry import ToolRegistry

all_tools = ToolRegistry.list_all_tools()
print(f"已注册工具数量: {len(all_tools)}")
for tool_name in all_tools:
    print(f"  - {tool_name}")

print("\n测试简单工具创建...")
# 测试创建文本输入工具
text_tool = ToolRegistry.create_tool("input.text_input")
if text_tool:
    print("✅ 文本输入工具创建成功")
    # 测试简单处理
    result = text_tool.process({"user_request": "测试文本"})
    print(f"  处理结果: {result}")
else:
    print("❌ 文本输入工具创建失败")

print("\n基础测试完成!")