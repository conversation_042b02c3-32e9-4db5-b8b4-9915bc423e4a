#!/usr/bin/env python3
"""
快速测试修复后的代码
"""
import sys
import os
sys.path.append('.')

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入工具模块以触发注册
import tools.processors.story_segmenter
import tools.generators.ai_image_generator
import tools.generators.html_generator
import tools.output.image_converter

from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def test_quick():
    """快速测试"""
    print("🧪 快速测试修复后的代码")
    
    # 简短测试故事
    test_story = """
    程序员小王今天遇到了一个bug，折腾了一整天终于修好了，内心十分满足。
    """
    
    # 创建初始状态
    initial_state = UniversalState(
        user_request=test_story,
        story_text=test_story,
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=test_story, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    # 自定义参数
    custom_params = {
        "max_segments": 2,  # 只分2段，减少处理时间
        "image_style": "humor",
        "html_theme": "story",
        "segment_style": "narrative"
    }
    
    preset = StoryCardsPreset()
    print(f"📝 测试故事长度: {len(test_story)} 字符")
    
    try:
        # 只测试第一步 - 故事分段
        print("\n🔍 测试故事分段...")
        result = preset._execute_segmentation(initial_state, custom_params)
        
        if result.get("error_message"):
            print(f"❌ 分段失败: {result['error_message']}")
        else:
            segments = result.get("segments", [])
            print(f"✅ 分段成功，共 {len(segments)} 段")
            for i, segment in enumerate(segments, 1):
                print(f"  第{i}段: {segment.get('title', 'N/A')}")
            
            # 测试单个段落的图文卡片生成
            if segments:
                print(f"\n🎨 测试单个段落的图文卡片生成...")
                segment_state = initial_state.copy()
                segment_state.update({
                    "segment_content": segments[0]["content"],
                    "segment_title": segments[0]["title"], 
                    "segment_index": 1,
                    "segment_elements": segments[0].get("key_elements", [])
                })
                
                print(f"   开始生成第1段的图文卡片...")
                card_result = preset._generate_segment_card(segment_state, custom_params)
                
                if card_result.get("error_message"):
                    print(f"   ❌ 卡片生成失败: {card_result['error_message']}")
                else:
                    print(f"   ✅ 卡片生成成功！")
                    print(f"   返回字段: {list(card_result.keys())}")
                    
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_quick()