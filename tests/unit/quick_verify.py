#!/usr/bin/env python3
"""
快速验证修复效果
"""
import sys
import os
sys.path.append('.')

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入工具模块以触发注册
import tools.processors.story_segmenter
import tools.generators.ai_image_generator
import tools.generators.html_generator
import tools.output.image_converter

from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def quick_verify():
    """快速验证输出路径"""
    print("🧪 快速验证输出路径修复")
    
    # 创建两个preset实例
    preset1 = StoryCardsPreset()
    preset2 = StoryCardsPreset()
    
    print(f"✅ 第一个实例输出目录: {preset1.output_dir}")
    print(f"✅ 第二个实例输出目录: {preset2.output_dir}")
    
    # 验证路径不同
    if preset1.output_dir != preset2.output_dir:
        print(f"🎉 输出目录不同，避免了文件覆盖！")
        print(f"🎉 路径结构: output/{preset1.name}/{{时间戳_uuid}}/")
    else:
        print(f"⚠️ 输出目录相同，可能存在问题")
        
    # 检查现有文件
    import glob
    existing_files = glob.glob("output/story_cards_preset/**/*.png", recursive=True)
    print(f"📁 现有图片文件数量: {len(existing_files)}")
    
    if existing_files:
        print(f"📝 示例文件:")
        for file in existing_files[:3]:  # 显示前3个
            print(f"   - {file}")

if __name__ == "__main__":
    quick_verify()