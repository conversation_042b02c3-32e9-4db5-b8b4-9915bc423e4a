"""
快速视频理解测试
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from video_understanding_workflow import run_video_understanding

def test_with_real_video():
    """使用真实视频URL测试"""
    
    # 使用一个较小的测试视频URL
    video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    print("🧪 使用真实视频URL测试...")
    print(f"📹 视频URL: {video_url}")
    
    result = run_video_understanding(
        video_url=video_url,
        user_request="请分析这个视频，重点关注角色、场景和故事情节，生成详细的脚本"
    )
    
    if result and not result.get("error_message"):
        print("\n🎉 测试成功！")
        print(f"📄 输出文件: {result.get('output_file_path')}")
        
        # 显示脚本摘要
        summary = result.get('script_summary', {})
        if summary:
            video_info = summary.get('video_info', {})
            stats = summary.get('statistics', {})
            
            print(f"\n📊 视频分析结果:")
            print(f"  🎬 标题: {video_info.get('title', 'N/A')}")
            print(f"  🎭 主题: {video_info.get('theme', 'N/A')}")
            print(f"  📸 镜头数: {stats.get('total_shots', 0)}")
            print(f"  ⏱️ 时长: {stats.get('total_duration_seconds', 0)}秒")
            print(f"  👥 角色数: {stats.get('character_count', 0)}")
        
        return True
    else:
        print(f"\n❌ 测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

def test_with_local_file():
    """测试本地文件（如果存在）"""
    
    # 检查是否有本地测试文件
    test_files = [
        "test_video.mp4",
        "sample.mp4",
        "demo.mov"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n🧪 发现本地测试文件: {test_file}")
            
            result = run_video_understanding(
                video_path=test_file,
                user_request="请分析这个本地视频文件"
            )
            
            if result and not result.get("error_message"):
                print("✅ 本地文件测试成功！")
                return True
            else:
                print(f"❌ 本地文件测试失败: {result.get('error_message') if result else '未知错误'}")
                return False
    
    print("\n💡 没有找到本地测试文件")
    print("   你可以将任意视频文件重命名为 test_video.mp4 放在项目根目录进行测试")
    return True

if __name__ == "__main__":
    print("🚀 快速视频理解测试")
    print("=" * 50)
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        print("💡 在 .env 文件中设置 VIDEO_UNDERSTAND_KEY")
        exit(1)
    
    print(f"🔑 API配置: {api_key[:10]}...")
    
    # 测试真实视频URL
    url_success = test_with_real_video()
    
    # 测试本地文件
    local_success = test_with_local_file()
    
    print("\n" + "=" * 50)
    if url_success:
        print("🎉 视频理解工作流测试成功！")
        print("💡 你现在可以使用这个工具分析任何视频了")
    else:
        print("⚠️ 测试未完全成功，但基础功能正常")
        print("💡 可能是网络问题或API限制，请稍后重试")
