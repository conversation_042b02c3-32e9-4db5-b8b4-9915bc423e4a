"""
测试COS集成功能
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_cos_configuration():
    """检查COS配置"""
    print("🔍 检查COS配置...")
    
    required_vars = [
        "TENCENT_SECRET_ID",
        "TENCENT_SECRET_KEY", 
        "COS_REGION",
        "COS_BUCKET"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
        else:
            print(f"  ✅ {var}: 已配置")
    
    if missing_vars:
        print(f"  ❌ 缺失配置: {missing_vars}")
        print("  💡 请在.env文件中配置COS相关环境变量")
        return False
    
    print("✅ COS配置检查通过")
    return True

def test_cos_uploader():
    """测试COS上传工具"""
    print("\n🧪 测试COS上传工具...")
    
    try:
        from tools.output.cos_uploader import CosUploaderTool
        
        cos_uploader = CosUploaderTool()
        
        # 创建一个测试文件
        test_file_path = "test_cos_upload.json"
        test_content = {
            "test": "COS上传测试",
            "timestamp": "2025-01-25",
            "content": "这是一个测试文件"
        }
        
        import json
        with open(test_file_path, 'w', encoding='utf-8') as f:
            json.dump(test_content, f, ensure_ascii=False, indent=2)
        
        print(f"📄 创建测试文件: {test_file_path}")
        
        # 测试上传
        result = cos_uploader.process({
            "file_path": test_file_path,
            "file_extension": "json"
        })
        
        # 清理测试文件
        try:
            os.remove(test_file_path)
        except:
            pass
        
        if result.get("error_message"):
            print(f"❌ COS上传测试失败: {result['error_message']}")
            return False
        
        cos_url = result.get("cos_url")
        upload_metadata = result.get("upload_metadata", {})
        
        print("✅ COS上传测试成功!")
        print(f"🔗 上传URL: {cos_url}")
        print(f"📊 文件大小: {upload_metadata.get('file_size', 0)} bytes")
        print(f"📅 上传时间: {upload_metadata.get('upload_time', 'unknown')}")
        
        return True
        
    except ImportError:
        print("❌ COS上传工具导入失败")
        return False
    except Exception as e:
        print(f"❌ COS上传测试失败: {str(e)}")
        return False

def test_video_input_cos_integration():
    """测试视频输入工具的COS集成"""
    print("\n🧪 测试视频输入工具COS集成...")
    
    from tools.video_processors.video_input import VideoInputTool
    
    input_tool = VideoInputTool()
    
    # 测试小文件URL（不会触发COS缓存）
    small_video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = input_tool.process({
        "video_url": small_video_url,
        "enable_preprocessing": True
    })
    
    if result.get("error_message"):
        print(f"❌ 视频输入COS集成测试失败: {result['error_message']}")
        return False
    
    processing_info = result.get("processing_info", {})
    
    print("✅ 视频输入COS集成测试成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    
    if processing_info.get("cos_cache_url"):
        print(f"🔗 COS缓存URL: {processing_info['cos_cache_url']}")
    else:
        print("💡 小文件未触发COS缓存（符合预期）")
    
    return True

def test_script_output_cos_integration():
    """测试脚本输出工具的COS集成"""
    print("\n🧪 测试脚本输出工具COS集成...")
    
    from tools.output.video_script_output import VideoScriptOutputTool
    
    output_tool = VideoScriptOutputTool()
    
    # 创建测试脚本数据
    test_script = {
        "videoMetaDataDTO": {
            "videoTitle": "COS集成测试视频",
            "videoTheme": "测试主题",
            "characterSheet": []
        },
        "shots": [
            {
                "shotNumber": 1,
                "startTime": 0,
                "endTime": 5,
                "scene": "测试场景",
                "shotType": "MEDIUM_SHOT"
            }
        ]
    }
    
    test_metadata = {
        "source_type": "test",
        "file_name": "test_video.mp4"
    }
    
    result = output_tool.process({
        "validated_script": test_script,
        "video_metadata": test_metadata,
        "validation_report": {"is_valid": True}
    })
    
    if result.get("error_message"):
        print(f"❌ 脚本输出COS集成测试失败: {result['error_message']}")
        return False
    
    output_file = result.get("output_file_path")
    cos_url = result.get("cos_url")
    
    print("✅ 脚本输出COS集成测试成功!")
    print(f"📄 本地文件: {output_file}")
    
    if cos_url:
        print(f"🔗 COS链接: {cos_url}")
        print("📤 文件已上传到云存储")
    else:
        print("💡 COS未配置或上传失败（可能是正常情况）")
    
    # 清理测试文件
    if output_file and os.path.exists(output_file):
        try:
            os.remove(output_file)
        except:
            pass
    
    return True

def show_cos_integration_benefits():
    """展示COS集成的优势"""
    print("\n💡 COS集成优势:")
    print("=" * 50)
    
    benefits = [
        "📤 自动云存储 - 脚本结果自动上传到云端",
        "🔗 分享链接 - 生成可直接访问的URL",
        "💾 视频缓存 - 大文件URL视频缓存到COS",
        "🚀 加速访问 - 利用CDN加速文件访问",
        "🔒 安全可靠 - 腾讯云COS提供企业级安全",
        "📊 成本优化 - 避免重复下载大文件"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print(f"\n🎯 使用场景:")
    scenarios = [
        "📋 脚本结果分享 - 生成链接直接分享给团队",
        "🎬 视频素材管理 - 大文件自动缓存到云端",
        "🔄 重复分析优化 - 相同URL视频无需重复下载",
        "📱 移动端访问 - 通过链接在手机上查看结果",
        "🌐 跨平台协作 - 不同系统间共享分析结果"
    ]
    
    for scenario in scenarios:
        print(f"  {scenario}")

def main():
    """主测试函数"""
    print("🚀 COS集成功能测试")
    print("=" * 60)
    
    # 检查COS配置
    cos_configured = check_cos_configuration()
    
    test_results = []
    
    if cos_configured:
        # 测试COS上传工具
        test_results.append(("COS上传工具", test_cos_uploader()))
        
        # 测试视频输入COS集成
        test_results.append(("视频输入COS集成", test_video_input_cos_integration()))
        
        # 测试脚本输出COS集成
        test_results.append(("脚本输出COS集成", test_script_output_cos_integration()))
    else:
        print("\n⚠️ COS未配置，跳过相关测试")
        print("💡 配置COS后可享受云存储和分享功能")
    
    # 展示COS集成优势
    show_cos_integration_benefits()
    
    # 输出测试结果
    if test_results:
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有COS集成测试通过！")
            print("📤 云存储功能已准备就绪")
        else:
            print("⚠️ 部分测试失败，请检查COS配置")
    
    print(f"\n🔧 配置说明:")
    print("在.env文件中添加以下配置启用COS功能:")
    print("TENCENT_SECRET_ID=your_secret_id")
    print("TENCENT_SECRET_KEY=your_secret_key")
    print("COS_REGION=your_region")
    print("COS_BUCKET=your_bucket")

if __name__ == "__main__":
    main()
