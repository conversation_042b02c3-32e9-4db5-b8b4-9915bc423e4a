"""
测试视频预处理功能
"""
import os
import subprocess
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def check_ffmpeg():
    """检查ffmpeg是否安装"""
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, timeout=10)
        if result.returncode == 0:
            print("✅ ffmpeg 已安装")
            return True
        else:
            print("❌ ffmpeg 未正确安装")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ 未找到 ffmpeg")
        print("💡 请安装 ffmpeg:")
        print("   macOS: brew install ffmpeg")
        print("   Ubuntu: sudo apt install ffmpeg")
        print("   Windows: 下载并添加到PATH")
        return False

def test_video_preprocessor():
    """测试视频预处理工具"""
    
    from tools.video_processors.video_preprocessor import VideoPreprocessor
    
    print("🧪 测试视频预处理工具...")
    
    preprocessor = VideoPreprocessor()
    
    # 检查ffmpeg可用性
    if not preprocessor.check_ffmpeg_availability():
        print("❌ ffmpeg不可用，跳过预处理测试")
        return False
    
    # 创建一个测试用的大视频文件（模拟）
    test_video_path = "test_large_video.mp4"
    
    # 如果没有测试文件，创建一个
    if not os.path.exists(test_video_path):
        print("📹 创建测试视频文件...")
        create_test_video(test_video_path)
    
    if not os.path.exists(test_video_path):
        print("⚠️ 无法创建测试视频，跳过预处理测试")
        return True
    
    # 测试预处理
    result = preprocessor.process({
        "video_file_path": test_video_path,
        "target_size_mb": 5  # 设置一个很小的目标大小来触发处理
    })
    
    if result.get("error_message"):
        print(f"❌ 预处理测试失败: {result['error_message']}")
        return False
    
    processed_videos = result.get("processed_videos", [])
    processing_info = result.get("processing_info", {})
    
    print(f"✅ 预处理测试成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"📁 生成文件数: {len(processed_videos)}")
    
    if processing_info.get("method") == "compression":
        print(f"🗜️ 压缩比: {processing_info.get('compression_ratio', 0):.2f}")
        print(f"📉 原始大小: {processing_info.get('original_size_mb', 0):.1f}MB")
        print(f"📉 压缩后大小: {processing_info.get('final_size_mb', 0):.1f}MB")
    
    elif processing_info.get("method") == "segmentation":
        print(f"✂️ 分段数: {processing_info.get('total_segments', 0)}")
        print(f"⏱️ 每段时长: {processing_info.get('segment_duration', 0):.1f}秒")
    
    # 清理测试文件
    cleanup_test_files([test_video_path] + [pv["path"] for pv in processed_videos])
    
    return True

def create_test_video(output_path: str):
    """创建一个测试视频文件"""
    try:
        # 创建一个30秒的测试视频
        cmd = [
            "ffmpeg", "-y",
            "-f", "lavfi",
            "-i", "testsrc=duration=30:size=1920x1080:rate=30",
            "-f", "lavfi", 
            "-i", "sine=frequency=1000:duration=30",
            "-c:v", "libx264",
            "-c:a", "aac",
            "-b:v", "5M",  # 高码率确保文件足够大
            output_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            file_size = os.path.getsize(output_path) / 1024 / 1024
            print(f"📹 创建测试视频成功: {file_size:.1f}MB")
        else:
            print(f"❌ 创建测试视频失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 创建测试视频出错: {str(e)}")

def test_segment_merger():
    """测试分段合并工具"""
    
    from tools.video_processors.segment_merger import SegmentMerger
    
    print("\n🧪 测试分段合并工具...")
    
    merger = SegmentMerger()
    
    # 创建模拟的分段结果
    segment_results = [
        {
            "video_script_json": {
                "videoMetaDataDTO": {
                    "videoTitle": "测试视频 - 第1段",
                    "videoTheme": "测试主题",
                    "characterSheet": [
                        {"name": "角色A", "description": "主角"}
                    ]
                },
                "shots": [
                    {
                        "shotNumber": 1,
                        "startTime": 0,
                        "endTime": 5,
                        "duration": 5,
                        "scene": "开场",
                        "shotType": "MEDIUM_SHOT"
                    },
                    {
                        "shotNumber": 2,
                        "startTime": 5,
                        "endTime": 10,
                        "duration": 5,
                        "scene": "介绍",
                        "shotType": "CLOSE_UP"
                    }
                ]
            }
        },
        {
            "video_script_json": {
                "videoMetaDataDTO": {
                    "videoTitle": "测试视频 - 第2段",
                    "videoTheme": "测试主题",
                    "characterSheet": [
                        {"name": "角色B", "description": "配角"}
                    ]
                },
                "shots": [
                    {
                        "shotNumber": 1,
                        "startTime": 0,
                        "endTime": 8,
                        "duration": 8,
                        "scene": "发展",
                        "shotType": "LONG_SHOT"
                    }
                ]
            }
        }
    ]
    
    processing_info = {
        "method": "segmentation",
        "total_segments": 2,
        "segments_info": [
            {"segment": 1, "duration": 10, "size_mb": 8.5},
            {"segment": 2, "duration": 8, "size_mb": 7.2}
        ]
    }
    
    result = merger.process({
        "segment_results": segment_results,
        "processing_info": processing_info
    })
    
    if result.get("error_message"):
        print(f"❌ 合并测试失败: {result['error_message']}")
        return False
    
    merged_script = result.get("merged_script", {})
    merge_summary = result.get("merge_summary", {})
    
    print("✅ 分段合并测试成功!")
    print(f"📊 合并后镜头数: {merge_summary.get('total_shots', 0)}")
    print(f"⏱️ 总时长: {merge_summary.get('total_duration', 0)}秒")
    print(f"👥 角色数: {len(merged_script.get('videoMetaDataDTO', {}).get('characterSheet', []))}")
    
    # 验证时间轴连续性
    shots = merged_script.get("shots", [])
    if shots:
        print(f"🎬 第一个镜头: {shots[0].get('startTime')}s - {shots[0].get('endTime')}s")
        print(f"🎬 最后镜头: {shots[-1].get('startTime')}s - {shots[-1].get('endTime')}s")
    
    return True

def test_integrated_workflow():
    """测试集成的工作流"""
    
    print("\n🧪 测试集成工作流...")
    
    from tools.video_processors.video_input import VideoInputTool
    
    input_tool = VideoInputTool()
    
    # 测试小文件（不需要预处理）
    small_video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = input_tool.process({
        "video_url": small_video_url,
        "enable_preprocessing": True
    })
    
    if result.get("error_message"):
        print(f"❌ 集成测试失败: {result['error_message']}")
        return False
    
    processing_info = result.get("processing_info", {})
    processed_videos = result.get("processed_videos", [])
    
    print("✅ 集成工作流测试成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"📁 处理后文件数: {len(processed_videos)}")
    
    return True

def cleanup_test_files(file_paths):
    """清理测试文件"""
    for file_path in file_paths:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"⚠️ 清理文件失败: {file_path}, {str(e)}")

def main():
    """主测试函数"""
    print("🚀 视频预处理功能测试")
    print("=" * 60)
    
    # 检查ffmpeg
    ffmpeg_ok = check_ffmpeg()
    
    test_results = []
    
    # 测试分段合并（不需要ffmpeg）
    test_results.append(("分段合并工具", test_segment_merger()))
    
    # 测试集成工作流
    test_results.append(("集成工作流", test_integrated_workflow()))
    
    # 如果ffmpeg可用，测试预处理
    if ffmpeg_ok:
        test_results.append(("视频预处理工具", test_video_preprocessor()))
    else:
        print("\n⚠️ 跳过视频预处理测试（需要ffmpeg）")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！视频预处理功能准备就绪")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
