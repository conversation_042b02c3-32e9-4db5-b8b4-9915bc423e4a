"""
测试最终的Ability类型配置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_ability_validation():
    """测试ability类型验证"""
    
    from tools.video_processors.script_validator import ScriptValidatorTool
    
    validator = ScriptValidatorTool()
    
    print("🔍 测试Ability类型验证...")
    print(f"✅ 支持的Ability类型: {len(validator.valid_ability_types)} 种")
    
    for ability_type in sorted(validator.valid_ability_types):
        print(f"  📌 {ability_type}")
    
    # 测试无效ability类型的修复
    test_script = {
        "videoMetaDataDTO": {
            "videoTitle": "测试视频"
        },
        "shots": [
            {
                "shotNumber": 1,
                "startTime": 0,
                "endTime": 5,
                "prompts": {
                    "ability": [
                        {
                            "abilityType": "SUPER_RESOLUTION",  # 已移除的类型
                            "parameters": {}
                        },
                        {
                            "abilityType": "TEXT_TO_IMAGE",     # 有效类型
                            "parameters": {}
                        }
                    ],
                    "cn": "测试",
                    "en": "test"
                }
            }
        ]
    }
    
    result = validator.process({"video_script_json": test_script})
    
    if result.get("error_message"):
        print(f"❌ 验证失败: {result['error_message']}")
        return False
    
    validated_script = result.get("validated_script", {})
    fixes_applied = result.get("fixes_applied", [])
    
    print(f"\n🔧 应用的修复: {len(fixes_applied)} 个")
    for fix in fixes_applied:
        print(f"  🛠️ {fix}")
    
    # 检查修复后的ability类型
    shots = validated_script.get("shots", [])
    if shots:
        abilities = shots[0].get("prompts", {}).get("ability", [])
        print(f"\n📊 修复后的ability类型:")
        for i, ability in enumerate(abilities):
            ability_type = ability.get("abilityType", "UNKNOWN")
            print(f"  {i+1}. {ability_type}")
            
            if ability_type in validator.valid_ability_types:
                print(f"     ✅ 有效")
            else:
                print(f"     ❌ 无效")
    
    return True

def show_final_abilities():
    """显示最终的ability类型列表"""
    
    abilities = {
        "TEXT_TO_IMAGE": "文生图 - 适用于静态场景、角色特写、风景镜头",
        "IMAGE_TO_IMAGE": "图生图 - 适用于风格转换、图像编辑",
        "TEXT_TO_VIDEO": "文生视频 - 适用于动态场景、运动镜头",
        "IMAGE_TO_VIDEO": "图生视频 - 适用于从静态到动态的转换",
        "DIGITAL_HUMAN": "数字人 - 适用于角色对话、表情变化",
        "MOTION_CAPTURE": "动作捕捉 - 适用于复杂动作、舞蹈",
        "FACE_SWAP": "换脸 - 适用于角色替换",
        "VOICE_CLONE": "声音克隆 - 适用于配音生成",
        "STYLE_TRANSFER": "风格迁移 - 适用于艺术风格转换"
    }
    
    print("🎯 最终支持的Ability类型:")
    print("=" * 60)
    
    for i, (ability_type, description) in enumerate(abilities.items(), 1):
        print(f"  {i:2d}. 📌 {ability_type}")
        print(f"      {description}")
        print()
    
    print("❌ 已移除的类型:")
    removed_types = [
        "SUPER_RESOLUTION (超分辨率)",
        "BACKGROUND_REMOVAL (背景移除)", 
        "OBJECT_DETECTION (物体检测)"
    ]
    
    for removed_type in removed_types:
        print(f"  🚫 {removed_type}")

if __name__ == "__main__":
    print("🚀 测试最终Ability类型配置")
    print("=" * 60)
    
    # 显示最终的ability类型
    show_final_abilities()
    
    print("\n" + "=" * 60)
    
    # 测试验证功能
    success = test_ability_validation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Ability类型配置更新成功！")
        print("💡 现在支持9种核心ability类型")
        print("🔧 自动修复功能正常工作")
    else:
        print("❌ 配置测试失败")
