"""
测试URL视频预处理功能
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_url_preprocessing():
    """测试URL视频预处理"""
    
    from tools.video_processors.video_input import VideoInputTool
    
    print("🧪 测试URL视频预处理功能...")
    
    input_tool = VideoInputTool()
    
    # 测试一个较大的视频URL（模拟超过20MB的情况）
    # 这个URL指向一个较大的视频文件
    large_video_url = "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
    
    print(f"📹 测试URL: {large_video_url}")
    
    result = input_tool.process({
        "video_url": large_video_url,
        "enable_preprocessing": True
    })
    
    if result.get("error_message"):
        print(f"❌ URL预处理测试失败: {result['error_message']}")
        return False
    
    processing_info = result.get("processing_info", {})
    processed_videos = result.get("processed_videos", [])
    total_segments = result.get("total_segments", 1)
    
    print("✅ URL预处理测试成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"📁 处理后文件数: {len(processed_videos)}")
    print(f"✂️ 分段数: {total_segments}")
    
    if processing_info.get("method") == "compression":
        print(f"🗜️ 压缩比: {processing_info.get('compression_ratio', 0):.2f}")
        print(f"📉 原始大小: {processing_info.get('original_size_mb', 0):.1f}MB")
        print(f"📉 压缩后大小: {processing_info.get('final_size_mb', 0):.1f}MB")
    
    elif processing_info.get("method") == "segmentation":
        print(f"✂️ 分段数: {processing_info.get('total_segments', 0)}")
        print(f"⏱️ 每段时长: {processing_info.get('segment_duration', 0):.1f}秒")
        
        segments_info = processing_info.get("segments_info", [])
        for i, seg_info in enumerate(segments_info[:3]):  # 显示前3个分段
            print(f"  📁 分段{i+1}: {seg_info.get('size_mb', 0):.1f}MB, {seg_info.get('duration', 0):.1f}s")
    
    # 清理临时文件
    temp_download = processing_info.get("temp_download")
    if temp_download and os.path.exists(temp_download):
        try:
            os.remove(temp_download)
            print("🧹 清理临时下载文件")
        except:
            pass
    
    # 清理处理后的文件
    for video_info in processed_videos:
        video_path = video_info.get("path")
        if video_path and os.path.exists(video_path) and "/tmp/" in video_path:
            try:
                os.remove(video_path)
            except:
                pass
    
    return True

def test_small_url():
    """测试小文件URL（不需要预处理）"""
    
    from tools.video_processors.video_input import VideoInputTool
    
    print("\n🧪 测试小文件URL（不需要预处理）...")
    
    input_tool = VideoInputTool()
    
    # 使用一个小的测试视频
    small_video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = input_tool.process({
        "video_url": small_video_url,
        "enable_preprocessing": True
    })
    
    if result.get("error_message"):
        print(f"❌ 小文件URL测试失败: {result['error_message']}")
        return False
    
    processing_info = result.get("processing_info", {})
    
    print("✅ 小文件URL测试成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"💡 原因: {processing_info.get('reason', 'unknown')}")
    
    return True

def test_url_workflow_integration():
    """测试URL预处理与完整工作流的集成"""
    
    print("\n🧪 测试URL预处理与工作流集成...")
    
    from video_understanding_workflow_v2 import run_video_understanding_v2
    
    # 使用小文件测试完整流程
    test_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = run_video_understanding_v2(
        video_url=test_url,
        user_request="请分析这个URL视频，测试预处理功能"
    )
    
    if result and not result.get("error_message"):
        print("✅ URL工作流集成测试成功!")
        
        processing_info = result.get("processing_info", {})
        print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
        
        script_summary = result.get("script_summary", {})
        if script_summary:
            stats = script_summary.get("statistics", {})
            print(f"🎬 镜头数: {stats.get('total_shots', 0)}")
            print(f"⏱️ 时长: {stats.get('total_duration_seconds', 0)}秒")
        
        return True
    else:
        print(f"❌ URL工作流集成测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

def check_prerequisites():
    """检查前置条件"""
    
    print("🔍 检查前置条件...")
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 未配置API密钥")
        return False
    
    # 检查ffmpeg
    import subprocess
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, timeout=10)
        print("✅ ffmpeg 可用")
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("⚠️ ffmpeg 不可用，大文件预处理将被跳过")
    
    # 检查网络连接
    try:
        import requests
        response = requests.head("https://www.google.com", timeout=5)
        if response.status_code == 200:
            print("✅ 网络连接正常")
        else:
            print("⚠️ 网络连接可能有问题")
    except:
        print("⚠️ 网络连接测试失败")
    
    return True

def main():
    """主测试函数"""
    print("🚀 URL视频预处理功能测试")
    print("=" * 60)
    
    # 检查前置条件
    if not check_prerequisites():
        print("❌ 前置条件检查失败")
        return
    
    test_results = []
    
    # 测试小文件URL（基础功能）
    test_results.append(("小文件URL处理", test_small_url()))
    
    # 测试URL工作流集成
    test_results.append(("URL工作流集成", test_url_workflow_integration()))
    
    # 测试大文件URL预处理（可能较慢）
    print("\n" + "=" * 60)
    print("⚠️ 大文件URL预处理测试可能需要较长时间...")
    user_input = input("是否继续测试大文件URL预处理？(y/N): ").strip().lower()
    
    if user_input == 'y':
        test_results.append(("大文件URL预处理", test_url_preprocessing()))
    else:
        print("⏭️ 跳过大文件URL预处理测试")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！URL视频预处理功能准备就绪")
        print("💡 现在支持URL视频的自动下载和预处理")
    else:
        print("⚠️ 部分测试失败，请检查网络连接和配置")

if __name__ == "__main__":
    main()
