"""
统一测试所有工具
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

def run_all_tool_tests():
    """运行所有工具的测试"""
    print("🚀 开始测试所有工具")
    print("="*60)
    
    test_results = []
    
    # 测试1: 文本输入工具
    try:
        from tools.input.text_input import test_text_input_tool
        test_text_input_tool()
        test_results.append(("TextInputTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("TextInputTool", f"❌ 失败: {e}"))
        print(f"❌ TextInputTool 测试失败: {e}")
    
    # 测试2: CSV读取工具
    try:
        from tools.input.csv_reader import test_csv_reader_tool
        test_csv_reader_tool()
        test_results.append(("CsvReaderTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("CsvReaderTool", f"❌ 失败: {e}"))
        print(f"❌ CsvReaderTool 测试失败: {e}")
    
    # 测试3: 小红书风格转换工具
    try:
        from tools.style_converters.xiaohongshu_style import test_xiaohongshu_style_tool
        test_xiaohongshu_style_tool()
        test_results.append(("XiaohongshuStyleTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("XiaohongshuStyleTool", f"❌ 失败: {e}"))
        print(f"❌ XiaohongshuStyleTool 测试失败: {e}")
    
    # 测试4: 文本分段工具
    try:
        from tools.content_processors.text_segmenter import test_text_segmenter_tool
        test_text_segmenter_tool()
        test_results.append(("TextSegmenterTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("TextSegmenterTool", f"❌ 失败: {e}"))
        print(f"❌ TextSegmenterTool 测试失败: {e}")
    
    # 测试5: HTML生成工具
    try:
        from tools.generators.html_generator import test_html_generator_tool
        test_html_generator_tool()
        test_results.append(("HtmlGeneratorTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("HtmlGeneratorTool", f"❌ 失败: {e}"))
        print(f"❌ HtmlGeneratorTool 测试失败: {e}")
    
    # 测试6: 图片转换工具
    try:
        from tools.output.image_converter import test_image_converter_tool
        test_image_converter_tool()
        test_results.append(("ImageConverterTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("ImageConverterTool", f"❌ 失败: {e}"))
        print(f"❌ ImageConverterTool 测试失败: {e}")
    
    # 测试7: COS上传工具
    try:
        from tools.output.cos_uploader import test_cos_uploader_tool
        test_cos_uploader_tool()
        test_results.append(("CosUploaderTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("CosUploaderTool", f"❌ 失败: {e}"))
        print(f"❌ CosUploaderTool 测试失败: {e}")
    
    # 测试8: AI图像生成工具
    try:
        from tools.generators.ai_image_generator import test_ai_image_generator_tool
        test_ai_image_generator_tool()
        test_results.append(("AiImageGeneratorTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("AiImageGeneratorTool", f"❌ 失败: {e}"))
        print(f"❌ AiImageGeneratorTool 测试失败: {e}")
    
    # 测试9: 图像编辑工具
    try:
        from tools.generators.image_editor import test_image_editor_tool
        test_image_editor_tool()
        test_results.append(("ImageEditorTool", "✅ 通过"))
    except Exception as e:
        test_results.append(("ImageEditorTool", f"❌ 失败: {e}"))
        print(f"❌ ImageEditorTool 测试失败: {e}")
    
    # 显示测试结果汇总
    print("="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    failed = 0
    
    for tool_name, result in test_results:
        print(f"{tool_name:<25} {result}")
        if "✅" in result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed} 个工具")
    print(f"通过: {passed} 个")
    print(f"失败: {failed} 个")
    
    if failed == 0:
        print("\n🎉 所有工具测试通过！")
    else:
        print(f"\n⚠️  有 {failed} 个工具测试失败，请检查错误信息")

def test_tool_registration():
    """测试工具注册情况"""
    print("\n🔧 测试工具注册情况")
    print("="*60)
    
    # 导入所有工具模块以触发注册
    try:
        import tools.input.text_input
        import tools.input.csv_reader
        import tools.style_converters.xiaohongshu_style
        import tools.content_processors.text_segmenter
        import tools.generators.html_generator
        import tools.output.image_converter
        import tools.output.cos_uploader
        import tools.generators.ai_image_generator
        import tools.generators.image_editor
        print("✅ 所有工具模块导入成功")
    except Exception as e:
        print(f"❌ 工具模块导入失败: {e}")
        return
    
    from tools.base.tool_registry import ToolRegistry
    
    expected_tools = [
        "input.text_input",
        "input.csv_reader", 
        "style_converter.xiaohongshu",
        "content_processor.segmenter",
        "generator.html",
        "output.html2image",
        "output.cos_uploader",
        "generator.ai_image",
        "generator.image_editor"
    ]
    
    registered_tools = ToolRegistry.list_all_tools()
    print(f"已注册工具数量: {len(registered_tools)}")
    
    for tool_name in registered_tools:
        print(f"  - {tool_name}")
    
    # 检查预期工具是否都已注册
    missing_tools = []
    for expected in expected_tools:
        if expected not in registered_tools:
            missing_tools.append(expected)
    
    if missing_tools:
        print(f"\n⚠️  缺少以下预期工具: {missing_tools}")
    else:
        print(f"\n✅ 所有预期工具都已注册")

if __name__ == "__main__":
    test_tool_registration()
    run_all_tool_tests()