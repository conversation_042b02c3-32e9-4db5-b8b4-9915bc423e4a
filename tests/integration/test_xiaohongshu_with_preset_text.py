"""
小红书图文生成预设流程测试脚本 - 内置测试文本版本
"""
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from typing import Dict, Any, List
from core.state import UniversalState
from graphs.preset_graphs.xiaohongshu_preset import XiaohongshuPresetGraph

# 预设测试文本
TEST_TEXTS = [
    "分享一个超级简单的减肥早餐！🥗 燕麦片+香蕉+蓝莓，营养丰富还低卡，每天这样吃，一个月瘦了8斤！姐妹们快试试吧～",
    
    "今天去了网红咖啡店☕️ 拿铁拉花超级漂亮，还有限定的樱花味蛋糕🌸 环境也很棒，适合拍照打卡，推荐给爱喝咖啡的小伙伴！",
    
    "最近在读《原子习惯》📚 真的太受启发了！每天进步1%，一年后就是37倍的成长。分享几个我实践的小习惯：早起、运动、读书，坚持就是胜利！",
    
    "周末在家做了韩式炸鸡🍗 外酥内嫩，配上自制的蒜蓉酱，简直绝了！制作方法超简单，材料也很容易买到，有兴趣的朋友可以试试～"
]

def _setup_test_environment():
    """设置测试环境，导入所需工具"""
    import tools.input.text_input
    import tools.style_converters.xiaohongshu_style
    import tools.content_processors.text_segmenter
    import tools.generators.html_generator
    import tools.output.image_converter

def _create_test_state(user_request: str) -> UniversalState:
    """创建测试状态"""
    return UniversalState(
        user_request=user_request,
        user_intent=None,
        execution_plan=None,
        selected_tools=None,
        raw_content=None,
        processed_content=None,
        content_segments=None,
        content_metadata=None,
        html_contents=None,
        image_paths=None,
        final_outputs=None,
        current_step=None,
        execution_history=None,
        error_message=None,
        tool_outputs=None,
        temp_data=None
    )

def _run_test(preset: XiaohongshuPresetGraph, test_state: UniversalState, test_name: str):
    """运行测试并打印结果"""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"📝 测试内容: {test_state['user_request']}")
    print(f"{'='*60}")
    
    try:
        result = preset.execute(test_state)
        
        if result.get("error_message"):
            print(f"❌ 测试失败: {result['error_message']}")
        else:
            print("✅ 测试成功!")
            if result.get("image_paths"):
                print(f"📸 生成图片: {len(result['image_paths'])} 张")
                for i, path in enumerate(result['image_paths'], 1):
                    print(f"   📷 图片 {i}: {path}")
    
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_xiaohongshu_preset_with_builtin_text():
    """使用内置文本测试小红书预设流程"""
    print("🚀 开始小红书预设流程测试（内置文本版本）")
    print(f"📋 共有 {len(TEST_TEXTS)} 个测试用例")
    
    # 设置测试环境
    _setup_test_environment()
    preset = XiaohongshuPresetGraph()
    
    # 运行所有测试用例
    for i, test_text in enumerate(TEST_TEXTS, 1):
        test_name = f"测试用例 {i} - {test_text[:20]}..."
        test_state = _create_test_state(test_text)
        _run_test(preset, test_state, test_name)
    
    print(f"\n🎉 所有测试完成！")

def test_single_case(case_index: int = 0):
    """测试单个用例"""
    if case_index >= len(TEST_TEXTS):
        print(f"❌ 测试用例索引超出范围，可用索引: 0-{len(TEST_TEXTS)-1}")
        return
    
    print(f"🧪 测试单个用例 {case_index + 1}")
    
    _setup_test_environment()
    preset = XiaohongshuPresetGraph()
    
    test_text = TEST_TEXTS[case_index]
    test_state = _create_test_state(test_text)
    _run_test(preset, test_state, f"单个测试用例 {case_index + 1}")

if __name__ == "__main__":
    # 运行所有测试用例
    test_xiaohongshu_preset_with_builtin_text()
    
    # 如果想要测试单个用例，可以取消注释下面的代码
    # test_single_case(0)  # 测试第一个用例