"""
测试小红书完整流程
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入所有工具模块以触发注册
import tools.input.text_input
import tools.input.csv_reader
import tools.style_converters.xiaohongshu_style
import tools.content_processors.text_segmenter
import tools.generators.html_generator
import tools.output.image_converter

from core.assistant import IntelligentImageAssistant

def test_xiaohongshu_flow():
    """测试小红书完整流程"""
    
    print("🚀 测试小红书图文生成完整流程")
    print("="*60)
    
    # 创建助手实例
    assistant = IntelligentImageAssistant()
    
    # 显示可用工具
    print("\n📋 当前可用工具:")
    tools = assistant.get_available_tools()
    for tool_name, tool_info in tools.items():
        print(f"  - {tool_name}: {tool_info.get('description', 'No description')}")
    
    # 测试用例：小红书风格转换
    test_requests = [
        "帮我把这条新闻转换成小红书风格的图文：科技创新推动产业升级，新一代AI芯片性能提升50%，功耗降低30%，将应用于智能手机、自动驾驶等领域。",
        "把这个生活小贴士做成小红书风格的图片：夏天如何选择防晒霜？SPF越高越好吗？不同肤质有何区别？记住这几个要点就够了！",
    ]
    
    for i, request in enumerate(test_requests, 1):
        print(f"\n{'='*60}")
        print(f"🧪 测试用例 {i}: 小红书风格图文生成")
        print(f"{'='*60}")
        print(f"输入: {request}")
        
        try:
            result = assistant.process_request(request)
            
            print(f"\n📊 执行结果:")
            print(f"  - 状态: {'成功' if not result.get('error_message') else '失败'}")
            
            if result.get("error_message"):
                print(f"  - 错误: {result['error_message']}")
            else:
                print(f"  - 用户意图: {result.get('user_intent', {}).get('style_requirement', 'unknown')}")
                print(f"  - 选中工具: {result.get('selected_tools', [])}")
                
                if result.get("image_paths"):
                    print(f"  - 生成图片: {len(result['image_paths'])} 张")
                    for path in result['image_paths']:
                        print(f"    * {path}")
                
                if result.get("html_contents"):
                    print(f"  - HTML文件: {len(result['html_contents'])} 个")
                
        except Exception as e:
            print(f"💥 测试失败: {e}")
            import traceback
            traceback.print_exc()

def test_csv_flow():
    """测试CSV数据源流程"""
    
    print(f"\n{'='*60}")
    print("🧪 测试CSV数据源流程")
    print(f"{'='*60}")
    
    assistant = IntelligentImageAssistant()
    
    # 修改请求让它使用CSV数据源
    request = "从CSV文件中读取ID为1的新闻，转换成小红书风格"
    print(f"输入: {request}")
    
    # 手动构造使用CSV的状态
    from core.state import UniversalState
    
    state = UniversalState(
        user_request=request,
        user_intent={"style_requirement": "csv数据源"},
        execution_plan=None,
        selected_tools=None,
        raw_content=None,
        processed_content=None,
        content_segments=None,
        content_metadata=None,
        html_contents=None,
        image_paths=None,
        final_outputs=None,
        current_step=None,
        execution_history=None,
        error_message=None,
        tool_outputs=None,
        temp_data=None
    )
    
    # 添加CSV需要的参数
    state["source_identifier"] = "1"
    
    try:
        # 手动生成计划
        plan = assistant.planner.generate_execution_plan(state["user_intent"])
        state["execution_plan"] = plan
        
        # 选择工具
        tools = assistant.selector.select_tools(plan)
        state["selected_tools"] = tools
        
        print(f"  - 执行计划: {plan['selected_tools']}")
        print(f"  - 选中工具: {tools}")
        
        # 执行工具链
        result = assistant.executor.execute_tool_chain(tools, state)
        
        print(f"\n📊 执行结果:")
        print(f"  - 状态: {'成功' if not result.get('error_message') else '失败'}")
        
        if result.get("error_message"):
            print(f"  - 错误: {result['error_message']}")
        else:
            if result.get("image_paths"):
                print(f"  - 生成图片: {len(result['image_paths'])} 张")
                for path in result['image_paths']:
                    print(f"    * {path}")
        
    except Exception as e:
        print(f"💥 CSV测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_xiaohongshu_flow()
    test_csv_flow()