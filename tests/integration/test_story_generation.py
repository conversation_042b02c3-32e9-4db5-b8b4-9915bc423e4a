"""
故事图文卡片生成测试脚本 - 使用你的实际故事内容
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def test_your_story():
    """测试你提供的故事内容"""
    print("📚 测试故事图文卡片生成")
    print("="*60)
    
    # 你的故事文本
    your_story = """我们小区有个"空姐"，三十多岁，长得五大三粗又白又胖，目测175cm、180斤以上，大嗓门子说起话来二里地外都能听真亮儿地，活脱脱一个肉弹战车。
大家都叫她"空姐"因为她年轻时候确实当过空姐，那时候身材苗条、个子高，模样也漂亮，天生吃这碗饭的。
那时候我们这小地方出了这么个大美人，还是个空姐，说媒的天天往她家跑，门槛子都快踩平了，但是人家"空姐"一个都没相中。
结果最后谁也没想到，跟一个工地开挖掘机的好上了。爹妈都不知道这俩人是咋搭搁上的。
两个老人坚决反对这俩人在一起，这不自家鲜花插人家牛粪上了吗！
但是老人反对根本不好使，没多长时间俩人就租个房子住一块儿了，"空姐"为了能天天跟这男的腻在一起，就背着家里人把工作辞了不干了，再后来就怀孕了。
这可是把爹妈气得倒仰，但是能咋的？也不能咋的，只能认了，草草办了婚事。
结婚之后没几个月就生了个儿子，那男的工作不稳定，没活干的时候在家一呆就是几个月，俩人就天天在家干待着，儿子全靠娘家的爹妈养。
家里人一瞅，这样过日子不行啊。
"空姐"娘家这边条件不错，她爷爷辈、叔叔辈和表兄妹不少都是本地司法系统工作人员，他爷爷就托人给她安排了在市里法院上班，书记员，有编制的。
结果干了不到一个月，说法院工作氛围太压抑，不想干了，跑回家了。又把他爷爷气够呛，直接跟她们家断绝来往了。她爸本来身体就不好，连生气带上火，不到一年人没了。
后来"空姐"又怀孕了，又生了个儿子，这下娘家妈得自己带俩外孙子。
她家男的又不知道中了什么邪，不干活了天天在家通宵玩网游，白天睡一天，晚上起来继续通宵。
"空姐"就天天在家刷手机，四张嘴全靠娘家妈一个人养活。
过了几年两个孩子越来越大，半大小子吃穷老子，娘家妈养不起了，就在我们小区给她们家租了个一楼的毛坯房住，拿笤帚把"空姐"打出门让她找活干。
"空姐"就在我们小区附近打零工，幼儿园、仓买、快餐店啥的都打过工。
她妈就是认为她出去干点啥活都比在家待着强，起码挣点是点，自己也能少往她们家贴钱。
结果那段时间空姐几乎所有的工资都用来买零食吃了，不干活的时候就是在家炫，体重直接飙到了现在这个样子。一个月的工资几天就都炫进去了，她妈还得照样掏钱养活这一家子。
最后她妈一狠心来了个釜底抽薪，把"空姐"的工资卡没收了，每月到账的工资她妈来管理和养这个家。

每年夏天吃完晚饭时候，我们总能看见"空姐"家俩儿子天天在小区院里疯跑，也不和别的孩子一起玩，穿的衣服都打铁了，大人给零食也不要。
俩孩子屋里屋外的来回窜，一楼本来蚊子就多，俩孩子这么一直鼓捣门，屋里棚顶一层蚊子，一家人晚上睡觉就跟蚊子睡一个屋。"空姐"有时候也会到凉亭里坐一会，小腿和胳膊上都是叮咬的包，还有密密麻麻的深色瘢痕。
小区里的邻居对她都挺好的，在凉亭坐着时候也都来跟她唠家常，还有谁家孩子衣服穿小了都会洗干净送给她，谁家包饺子了也会给她家送一小盆。
但是每当"空姐"不在的时候，邻居们又会讨论起她的一些破事儿来，什么"在外面又搭搁了一个男的"、"工资都给外面的男的花了"之类的。
后来"空姐"果真领着另一个男的出现在了小区里，每天出双入对的也不避讳旁人，而她家里的男的又不知道中了什么邪，直接卷铺盖消失了，只留下了两个儿子。

再后来不知道什么时候开始，好久都没在小区里见过"空姐"和她两个儿子了，邻居慢慢的也不怎么议论她了。
只是突然有一天，小区里传开了一个关于"空姐"的爆炸性消息：她后来跟的那个男的是搞CHUANXIAO的，把她骗进了一个南方的CHUANXIAO组织。在里面人家让她骗钱，她试图逃跑好几次，结果被打个半死，后来又被卖到了一个MAIYIN组织，她拒不配合，被折磨了好久，也没压榨出什么价值。最后可能是看人要不行了，怕出人命，这才联系的家里人，让拿钱赎人。
她妈知道这事的时候吓得不轻，找亲戚借了不少钱，又找的"空姐"一个叔叔，安排人把她算是活着弄了回来。
她回来那天，小区里直接炸锅了。
听老人说她回来的时候都没有衣服穿，披的司机的长外套，从头到脚都是血和泥。从车里下来的时候是爬出来的，腿已经不能走路了，她妈连拖带拽才把她弄进家里，喂了点稀饭，洗涮一下又叫的医院救护车，盖着大棉被拉到医院去的。

第二年开春她和两个儿子又出现在小区里了，邻居所有人都很默契，两个孩子在附近的时候坚决不会议论她的事情。
"空姐"也瘦了好多，头发成了寸头，据说是刚救回来时候全粘在一起根本梳不开，就直接全剃了。天气再热她也是穿的长袖衣裤，唯一没变的就是说话那个大嗓门子。

后来她妈又找了个老头，据说是退休的干部，有不少存款。
所有人都说"空姐"她妈这么精明，怎么女儿就这么虎。

以上，真人真事。"""
    
    print(f"📝 故事长度: {len(your_story)} 字符")
    
    # 导入必要的工具以触发注册
    try:
        import tools.processors.story_segmenter
        import tools.generators.ai_image_generator  
        import tools.generators.html_generator
        import tools.output.html2image_converter
        import tools.output.cos_uploader
        print("✅ 所有工具模块导入成功")
    except ImportError as e:
        print(f"❌ 工具模块导入失败: {e}")
        return
    
    # 检查API配置
    api_keys_status = check_api_configuration()
    
    # 创建预设流程
    preset = StoryCardsPreset()
    
    # 创建初始状态
    initial_state = UniversalState(
        user_request=your_story,
        story_text=your_story,
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=your_story, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    # 配置参数 - 使用你要求的humor风格
    custom_params = {
        "max_segments": 6,           # 分成6段
        "image_style": "humor",      # 幽默风格
        "html_theme": "story",       # 故事主题
        "segment_style": "narrative" # 按情节分段
    }
    
    print(f"\n⚙️ 测试配置:")
    print(f"   分段数量: {custom_params['max_segments']}")
    print(f"   图像风格: {custom_params['image_style']}")
    print(f"   HTML主题: {custom_params['html_theme']}")
    print(f"   分段策略: {custom_params['segment_style']}")
    
    print(f"\n🔧 API配置状态:")
    for api, status in api_keys_status.items():
        print(f"   {api}: {'✅' if status else '❌'}")
    
    # 如果没有API密钥，只做参数演示
    if not any(api_keys_status.values()):
        print(f"\n⚠️ 未配置API密钥，只进行参数演示")
        demo_parameter_preparation(preset, initial_state, custom_params)
    else:
        print(f"\n🚀 开始执行完整流程...")
        try:
            result = preset.execute(initial_state, custom_params)
            
            if result.get("error_message"):
                print(f"❌ 执行失败: {result['error_message']}")
            else:
                print_results(result)
                
        except Exception as e:
            print(f"❌ 执行异常: {e}")
            import traceback
            traceback.print_exc()

def check_api_configuration():
    """检查API配置状态"""
    api_status = {}
    
    # 检查图像生成API
    api_status["Jmeng图像生成"] = bool(os.getenv("JMENG_PROXY_API_KEY"))
    
    # 检查LLM API  
    api_status["LLM服务"] = bool(os.getenv("OPENAI_API_KEY") or 
                                  os.getenv("ANTHROPIC_API_KEY") or
                                  os.getenv("QWEN_API_KEY"))
    
    # 检查COS上传
    api_status["腾讯云COS"] = bool(os.getenv("COS_SECRET_ID") and 
                                    os.getenv("COS_SECRET_KEY"))
    
    return api_status

def demo_parameter_preparation(preset, state, custom_params):
    """演示参数准备过程"""
    print(f"\n🔧 参数准备演示:")
    
    # 演示分段参数
    segmentation_params = {
        "story_text": state.get("story_text", "")[:100] + "...",
        "max_segments": custom_params["max_segments"],
        "segment_style": custom_params["segment_style"]
    }
    print(f"\n📖 分段工具参数:")
    for key, value in segmentation_params.items():
        print(f"   {key}: {value}")
    
    # 演示图像生成参数（模拟第一段）
    mock_segment_content = "我们小区有个空姐，三十多岁，长得五大三粗又白又胖..."
    image_params = {
        "prompt": mock_segment_content,
        "style": custom_params["image_style"],
        "width": 810,
        "height": 1080,
        "enhance_prompt": True,
        "meme_keywords": ["空姐", "小区"],
        "mood": "叙事性场景"
    }
    print(f"\n🖼️ 图像生成参数 (示例段落):")
    for key, value in image_params.items():
        if key == "prompt":
            print(f"   {key}: {str(value)[:50]}...")
        else:
            print(f"   {key}: {value}")
    
    # 演示HTML参数
    html_params = {
        "title": "第1段: 空姐的现状",
        "content": mock_segment_content,
        "layout_type": "story_card",
        "background_color": "#F8F5F0",
        "text_color": "#2C2C2C",
        "border_style": "solid 2px #D4C4A8"
    }
    print(f"\n📄 HTML卡片参数 (示例):")
    for key, value in html_params.items():
        if key == "content":
            print(f"   {key}: {str(value)[:50]}...")
        else:
            print(f"   {key}: {value}")

def print_results(result):
    """打印执行结果"""
    print(f"\n🎉 执行完成!")
    
    total_segments = result.get("total_segments", 0)
    successful_cards = result.get("successful_cards", 0)
    
    print(f"📊 处理统计:")
    print(f"   总段落数: {total_segments}")
    print(f"   成功生成: {successful_cards}")
    print(f"   成功率: {successful_cards/total_segments*100:.1f}%" if total_segments > 0 else "   成功率: 0%")
    
    # 显示每个段落的结果
    cards = result.get("segment_cards", [])
    if cards:
        print(f"\n📋 各段落处理结果:")
        for card in cards:
            status = "✅" if not card.get("error") else "❌"
            title = card.get("segment_title", f"第{card.get('segment_index', '?')}段")
            print(f"   {status} {title}")
            
            if card.get("error"):
                print(f"      错误: {card['error']}")
            else:
                if card.get("image_url"):
                    print(f"      图像: {card['image_url'][:50]}...")
                if card.get("final_card"):
                    print(f"      卡片: {len(card['final_card'])} 张")
    
    # 显示分段结果
    segments = result.get("story_segments", [])
    if segments:
        print(f"\n📚 故事分段结果:")
        for i, segment in enumerate(segments, 1):
            print(f"   第{i}段: {segment.get('title', f'段落{i}')}")
            print(f"         字数: {segment.get('word_count', 0)}")
            content_preview = segment.get('content', '')[:100] + "..." if len(segment.get('content', '')) > 100 else segment.get('content', '')
            print(f"         内容: {content_preview}")

if __name__ == "__main__":
    print("🚀 开始测试故事图文卡片生成系统")
    print("="*60)
    
    test_your_story()
    
    print(f"\n\n💡 如果要使用真实API，请设置以下环境变量:")
    print("   export JMENG_PROXY_API_KEY='your_jmeng_api_key'")
    print("   export OPENAI_API_KEY='your_openai_api_key'  # 或其他LLM API")
    print("   export COS_SECRET_ID='your_cos_secret_id'")
    print("   export COS_SECRET_KEY='your_cos_secret_key'")
    print("   export COS_REGION='ap-guangzhou'")
    print("   export COS_BUCKET='your_bucket_name'")
    
    print(f"\n📝 然后重新运行:")
    print("   python3 test_story_generation.py")