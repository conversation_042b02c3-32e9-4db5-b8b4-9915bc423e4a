"""
测试视频理解工作流 V2
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from video_understanding_workflow_v2 import run_video_understanding_v2

def test_v2_workflow():
    """测试 V2 工作流"""
    
    print("🧪 测试视频理解工作流 V2...")
    
    # 使用测试视频
    video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = run_video_understanding_v2(
        video_url=video_url,
        user_request="请详细分析这个视频，生成完整的脚本，包括多种ability类型的智能选择"
    )
    
    if result and not result.get("error_message"):
        print("\n🎉 V2 工作流测试成功！")
        
        # 显示详细结果
        processing_info = result.get("processing_info", {})
        merge_summary = result.get("merge_summary", {})
        script_summary = result.get("script_summary", {})
        
        print(f"\n📊 详细结果:")
        print(f"  📄 输出文件: {result.get('output_file_path')}")
        print(f"  🔧 处理方法: {processing_info.get('method', 'unknown')}")
        print(f"  📁 处理文件数: {result.get('total_segments', 1)}")
        
        if script_summary:
            stats = script_summary.get("statistics", {})
            print(f"  🎬 镜头数: {stats.get('total_shots', 0)}")
            print(f"  ⏱️ 时长: {stats.get('total_duration_seconds', 0)}秒")
            print(f"  👥 角色数: {stats.get('character_count', 0)}")
        
        return True
    else:
        print(f"\n❌ V2 工作流测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

if __name__ == "__main__":
    print("🚀 测试视频理解工作流 V2（支持大文件预处理）")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        exit(1)
    
    success = test_v2_workflow()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 V2 工作流测试成功！")
        print("💡 现在支持大文件预处理和分段分析")
        print("🔧 支持压缩 + 分段的智能处理策略")
    else:
        print("❌ 测试失败，请检查配置")
