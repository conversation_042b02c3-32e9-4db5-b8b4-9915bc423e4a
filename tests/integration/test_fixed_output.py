#!/usr/bin/env python3
"""
测试修复后的输出路径和文件覆盖问题
"""
import sys
import os
sys.path.append('.')

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入工具模块以触发注册
import tools.processors.story_segmenter
import tools.generators.ai_image_generator
import tools.generators.html_generator
import tools.output.image_converter

from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def test_fixed_output():
    """测试修复后的输出功能"""
    print("🧪 测试修复后的输出路径和文件覆盖问题")
    print("="*60)
    
    # 简短测试故事
    test_story = """
    今天是个好日子，程序员小王发现自己写的代码竟然一次性通过了所有测试。

    他激动地跑去告诉同事，结果发现大家都在忙着修bug，没人理他。
    """
    
    # 创建初始状态
    initial_state = UniversalState(
        user_request=test_story,
        story_text=test_story,
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=test_story, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    # 自定义参数
    custom_params = {
        "max_segments": 2,
        "image_style": "humor",
        "html_theme": "story",
        "segment_style": "narrative"
    }
    
    print(f"📝 测试故事长度: {len(test_story)} 字符")
    
    try:
        # 创建两个不同的preset实例，测试文件不会覆盖
        print(f"\n🚀 第一次执行...")
        preset1 = StoryCardsPreset()
        result1 = preset1.execute(initial_state, custom_params)
        
        print(f"\n🚀 第二次执行...")
        preset2 = StoryCardsPreset()
        result2 = preset2.execute(initial_state, custom_params)
        
        # 检查结果
        if result1.get("error_message"):
            print(f"❌ 第一次执行失败: {result1['error_message']}")
        elif result2.get("error_message"):
            print(f"❌ 第二次执行失败: {result2['error_message']}")
        else:
            print(f"\n✅ 两次执行都成功！")
            
            # 显示输出路径差异
            print(f"\n📊 输出路径比较:")
            print(f"   第一次: {preset1.output_dir}")
            print(f"   第二次: {preset2.output_dir}")
            
            # 检查是否生成了不同的文件
            if result1.get("final_outputs") and result2.get("final_outputs"):
                paths1 = result1["final_outputs"].get("image_paths", [])
                paths2 = result2["final_outputs"].get("image_paths", [])
                
                print(f"\n🖼️ 生成的图片文件:")
                print(f"   第一次: {len(paths1)} 个文件")
                for path in paths1:
                    print(f"     - {path}")
                    
                print(f"   第二次: {len(paths2)} 个文件")
                for path in paths2:
                    print(f"     - {path}")
                    
                # 验证文件路径不同
                if paths1 and paths2 and paths1[0] != paths2[0]:
                    print(f"✅ 文件路径不同，没有覆盖问题！")
                else:
                    print(f"⚠️ 文件路径相同，可能存在覆盖问题")
                    
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fixed_output()