"""
视频理解工作流测试脚本
"""
import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

from video_understanding_workflow import run_video_understanding

def test_video_url():
    """测试视频URL分析"""
    print("🧪 测试视频URL分析...")
    
    # 使用一个公开的测试视频URL
    test_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    
    result = run_video_understanding(
        video_url=test_url,
        user_request="请详细分析这个视频，生成完整的脚本，包括镜头分析和角色描述"
    )
    
    if result and not result.get("error_message"):
        print("✅ URL测试成功")
        print(f"📄 输出文件: {result.get('output_file_path')}")
        return True
    else:
        print(f"❌ URL测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

def test_local_file():
    """测试本地文件分析"""
    print("\n🧪 测试本地文件分析...")
    
    # 检查是否有测试视频文件
    test_files = [
        "test_video.mp4",
        "sample.mp4", 
        "demo.mov",
        "output_images/test.mp4"  # 可能存在的测试文件
    ]
    
    test_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            test_file = file_path
            break
    
    if not test_file:
        print("⚠️ 没有找到测试视频文件，跳过本地文件测试")
        print("💡 提示：将测试视频文件命名为 test_video.mp4 放在项目根目录")
        return True
    
    result = run_video_understanding(
        video_path=test_file,
        user_request="请分析这个本地视频文件，重点关注镜头切换和场景变化"
    )
    
    if result and not result.get("error_message"):
        print("✅ 本地文件测试成功")
        print(f"📄 输出文件: {result.get('output_file_path')}")
        return True
    else:
        print(f"❌ 本地文件测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    # 测试无效URL
    result = run_video_understanding(
        video_url="https://invalid-url-that-does-not-exist.com/video.mp4",
        user_request="测试错误处理"
    )
    
    if result and result.get("error_message"):
        print("✅ 错误处理测试成功 - 正确捕获了无效URL错误")
        return True
    else:
        print("❌ 错误处理测试失败 - 应该返回错误信息")
        return False

def test_individual_tools():
    """测试单个工具"""
    print("\n🧪 测试单个工具...")
    
    try:
        # 测试视频输入工具
        from tools.video_processors.video_input import VideoInputTool
        
        input_tool = VideoInputTool()
        result = input_tool.process({
            "video_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        })
        
        if result.get("error_message"):
            print(f"❌ 视频输入工具测试失败: {result['error_message']}")
            return False
        else:
            print("✅ 视频输入工具测试成功")
        
        # 测试脚本验证工具
        from tools.video_processors.script_validator import ScriptValidatorTool
        
        validator = ScriptValidatorTool()
        test_script = {
            "videoMetaDataDTO": {
                "videoTitle": "测试视频",
                "videoTheme": "测试主题"
            },
            "shots": [
                {
                    "shotNumber": 1,
                    "startTime": 0,
                    "endTime": 5
                }
            ]
        }
        
        result = validator.process({"video_script_json": test_script})
        
        if result.get("error_message"):
            print(f"❌ 脚本验证工具测试失败: {result['error_message']}")
            return False
        else:
            print("✅ 脚本验证工具测试成功")
            
        return True
        
    except Exception as e:
        print(f"❌ 单个工具测试失败: {str(e)}")
        return False

def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    api_url = os.getenv("VIDEO_UNDERSTAND_URL") or os.getenv("OPENAI_BASE_URL")
    
    if not api_key:
        print("⚠️ 警告: 未设置API密钥")
        print("💡 请设置环境变量: VIDEO_UNDERSTAND_KEY 或 OPENAI_API_KEY")
        return False
    
    if not api_url:
        print("⚠️ 警告: 未设置API URL")
        print("💡 请设置环境变量: VIDEO_UNDERSTAND_URL 或 OPENAI_BASE_URL")
        return False
    
    print("✅ API配置检查通过")
    print(f"🔑 API Key: {api_key[:10]}...")
    print(f"🌐 API URL: {api_url}")
    
    # 检查输出目录
    output_dir = "output/video_scripts"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"📁 创建输出目录: {output_dir}")
    else:
        print(f"📁 输出目录存在: {output_dir}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始视频理解工作流测试")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请配置API密钥后重试")
        return
    
    test_results = []
    
    # 运行测试
    test_results.append(("单个工具测试", test_individual_tools()))
    test_results.append(("错误处理测试", test_error_handling()))
    test_results.append(("本地文件测试", test_local_file()))
    
    # 如果有API配置，测试URL分析
    if os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY"):
        test_results.append(("视频URL测试", test_video_url()))
    else:
        print("\n⚠️ 跳过视频URL测试（需要API配置）")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！视频理解工作流准备就绪")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖")

if __name__ == "__main__":
    main()
