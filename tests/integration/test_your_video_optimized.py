"""
测试你的111MB视频 - 使用优化后的完整流程
"""
import os
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_your_large_video():
    """测试你的大视频文件"""
    
    print("🎬 测试你的111MB视频 - 优化版流程")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ API密钥未配置")
        print("💡 请在.env文件中设置:")
        print("   VIDEO_UNDERSTAND_KEY=your_key")
        print("   或 OPENAI_API_KEY=your_key")
        return False
    
    print("✅ API密钥已配置")
    
    # 检查COS配置
    cos_configured = all([
        os.getenv("TENCENT_SECRET_ID"),
        os.getenv("TENCENT_SECRET_KEY"), 
        os.getenv("COS_REGION"),
        os.getenv("COS_BUCKET")
    ])
    
    if cos_configured:
        print("✅ COS配置已启用")
    else:
        print("⚠️ COS未配置，将使用本地文件")
    
    # 你的视频URL
    video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/28297332648-1-192.mp4"
    
    print(f"\n📹 目标视频: {video_url}")
    print("📊 预期: 111.5MB，需要预处理和分段")
    
    # 使用优化后的处理器
    try:
        from optimized_large_video_processor import OptimizedLargeVideoProcessor
        
        processor = OptimizedLargeVideoProcessor()
        
        print("\n🚀 开始优化处理流程...")
        start_time = time.time()
        
        result = processor.process_large_video(
            video_url=video_url,
            user_request="请详细分析这个视频的内容、角色、场景变化，重点关注故事情节和视觉效果"
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n⏱️ 总处理时间: {processing_time:.1f}秒")
        
        if result.get("error_message"):
            print(f"❌ 处理失败: {result['error_message']}")
            return False
        
        # 显示详细结果
        print("\n🎉 处理成功！")
        
        optimization_summary = result.get("optimization_summary", {})
        output_file = result.get("output_file_path")
        cos_url = result.get("cos_url")
        script_summary = result.get("script_summary", {})
        
        print(f"\n📊 优化效果:")
        print(f"  📁 处理分段: {optimization_summary.get('segments_processed', 0)}")
        print(f"  🎬 生成镜头: {optimization_summary.get('total_shots', 0)}")
        print(f"  ⏱️ 视频时长: {optimization_summary.get('total_duration', 0)}秒")
        print(f"  💰 成本优化: {optimization_summary.get('api_calls_saved', 'N/A')}")
        
        print(f"\n📄 输出文件:")
        print(f"  💾 本地文件: {output_file}")
        if cos_url:
            print(f"  🔗 云端链接: {cos_url}")
            print(f"  📤 可直接分享使用")
        
        # 显示脚本摘要
        if script_summary:
            video_info = script_summary.get("video_info", {})
            stats = script_summary.get("statistics", {})
            highlights = script_summary.get("highlights", {})
            
            print(f"\n🎭 内容摘要:")
            print(f"  📺 标题: {video_info.get('title', 'N/A')}")
            print(f"  🎨 主题: {video_info.get('theme', 'N/A')}")
            print(f"  👥 角色数: {stats.get('character_count', 0)}")
            print(f"  💬 对话数: {stats.get('dialogue_count', 0)}")
            
            if highlights.get("best_moment"):
                print(f"  🌟 精彩片段: {highlights['best_moment']}")
            
            if highlights.get("key_insights"):
                print(f"  💡 关键洞察: {highlights['key_insights'][:100]}...")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("💡 请确保所有依赖模块都已正确安装")
        return False
    except Exception as e:
        print(f"❌ 处理异常: {str(e)}")
        return False

def compare_with_previous_result():
    """与之前的结果对比"""
    
    print("\n📊 与之前结果对比:")
    print("=" * 60)
    
    previous_file = "output/video_scripts/video_script_28297332648_1_192_20250725_223749.json"
    
    if os.path.exists(previous_file):
        print("📄 发现之前的处理结果:")
        print(f"  📁 文件: {previous_file}")
        
        try:
            import json
            with open(previous_file, 'r', encoding='utf-8') as f:
                previous_data = json.load(f)
            
            # 分析之前的结果
            script = previous_data.get("script", {})
            metadata = previous_data.get("metadata", {})
            validation = metadata.get("validation_report", {})
            
            print(f"  📊 之前结果:")
            print(f"    ❌ 解析状态: {'失败' if not validation.get('is_valid', True) else '成功'}")
            print(f"    🔧 修复次数: {validation.get('total_fixes', 0)}")
            print(f"    🎬 镜头数: {validation.get('script_stats', {}).get('total_shots', 0)}")
            
            if script.get("parse_error"):
                print(f"    ❌ JSON错误: {script['parse_error']}")
            
            print(f"\n💡 优化版预期改进:")
            print(f"  🔄 重试机制 - 减少解析失败")
            print(f"  🔧 JSON修复 - 自动修复格式错误")
            print(f"  📊 降级处理 - 即使失败也有结果")
            print(f"  💰 成本优化 - 减少API调用次数")
            
        except Exception as e:
            print(f"  ❌ 读取之前结果失败: {str(e)}")
    else:
        print("📄 未找到之前的处理结果")

def main():
    """主测试函数"""
    
    print("🎯 测试你的111MB视频 - 完整优化流程")
    print("=" * 80)
    
    # 对比之前的结果
    compare_with_previous_result()
    
    # 询问是否继续
    print("\n" + "=" * 80)
    print("⚠️ 即将开始处理111MB大视频，可能需要几分钟时间...")
    print("💰 预计API调用: 4次（优化后，原来需要8次）")
    print("⏱️ 预计时间: 3-5分钟（并发处理）")
    
    user_input = input("\n是否继续测试？(y/N): ").strip().lower()
    
    if user_input != 'y':
        print("⏭️ 测试已取消")
        return
    
    # 开始测试
    success = test_your_large_video()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 优化版测试成功！")
        print("💡 主要改进:")
        print("  🔄 智能重试 - 提高成功率")
        print("  🔧 JSON修复 - 自动处理格式错误")
        print("  🚀 并发处理 - 大幅提升速度")
        print("  💰 成本优化 - 减少50%API调用")
        print("  📤 云存储 - 自动生成分享链接")
    else:
        print("❌ 测试失败")
        print("💡 可能的原因:")
        print("  🔑 API密钥配置问题")
        print("  🌐 网络连接问题")
        print("  🔧 依赖模块缺失")
        print("  📊 视频内容过于复杂")

if __name__ == "__main__":
    main()
