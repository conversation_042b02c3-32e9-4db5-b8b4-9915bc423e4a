"""
测试大文件URL修复
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_large_url_preprocessing():
    """测试大文件URL预处理修复"""
    
    print("🧪 测试大文件URL预处理修复...")
    
    from tools.video_processors.video_input import VideoInputTool
    
    input_tool = VideoInputTool()
    
    # 测试大文件URL
    large_video_url = "https://duomotai-1317512395.cos.ap-guangzhou.myqcloud.com/28297332648-1-192.mp4"
    
    print(f"📹 测试URL: {large_video_url}")
    print("🔄 开始处理...")
    
    result = input_tool.process({
        "video_url": large_video_url,
        "enable_preprocessing": True
    })
    
    if result.get("error_message"):
        print(f"❌ 预处理失败: {result['error_message']}")
        return False
    
    processing_info = result.get("processing_info", {})
    processed_videos = result.get("processed_videos", [])
    total_segments = result.get("total_segments", 1)
    
    print("✅ 预处理成功!")
    print(f"📊 处理方法: {processing_info.get('method', 'unknown')}")
    print(f"📁 生成分段数: {len(processed_videos)}")
    print(f"✂️ 总分段数: {total_segments}")
    
    # 检查分段是否有COS URL
    cos_segments = 0
    local_segments = 0
    
    for i, video_info in enumerate(processed_videos):
        video_path = video_info.get("path")
        is_cos_url = video_path and video_path.startswith("https://")
        
        if is_cos_url:
            cos_segments += 1
            print(f"  📤 分段{i+1}: COS URL - {video_path[:50]}...")
        else:
            local_segments += 1
            print(f"  💾 分段{i+1}: 本地文件 - {video_path}")
    
    print(f"\n📊 分段统计:")
    print(f"  📤 COS URL分段: {cos_segments}")
    print(f"  💾 本地文件分段: {local_segments}")
    
    if cos_segments > 0:
        print("✅ COS上传功能正常，可以进行API分析")
    else:
        print("⚠️ 所有分段都是本地文件，可能会导致API调用失败")
    
    return True

def test_single_segment_analysis():
    """测试单个分段的分析（模拟）"""
    
    print("\n🧪 测试单个分段分析...")
    
    # 使用一个小文件URL来模拟分段分析
    test_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    from tools.video_processors.video_script_analyzer import VideoScriptAnalyzer
    
    analyzer = VideoScriptAnalyzer()
    
    result = analyzer.process({
        "video_url": test_url,
        "analysis_requirements": "这是视频的第1段（共8段）。请分析这个片段的内容。"
    })
    
    if result.get("error_message"):
        print(f"❌ 分段分析失败: {result['error_message']}")
        return False
    
    script = result.get("video_script_json", {})
    shots = script.get("shots", [])
    
    print("✅ 分段分析成功!")
    print(f"🎬 生成镜头数: {len(shots)}")
    
    if shots:
        first_shot = shots[0]
        print(f"📋 第一个镜头:")
        print(f"  🎬 镜头号: {first_shot.get('shotNumber')}")
        print(f"  ⏱️ 时间: {first_shot.get('startTime')}s - {first_shot.get('endTime')}s")
        print(f"  🎭 场景: {first_shot.get('scene', '')[:50]}...")
    
    return True

def show_fix_summary():
    """显示修复总结"""
    
    print("\n🔧 修复总结:")
    print("=" * 50)
    
    fixes = [
        "🌐 URL大小检测逻辑修复 - 不再直接返回错误",
        "📤 分段COS上传功能 - 自动上传分段获取URL",
        "🔗 API兼容性改进 - 使用COS URL而非本地路径",
        "⚡ 处理流程优化 - 预处理→上传→分析→合并"
    ]
    
    for fix in fixes:
        print(f"  {fix}")
    
    print(f"\n🎯 预期效果:")
    effects = [
        "✅ 大文件URL自动下载和预处理",
        "✅ 分段文件自动上传到COS",
        "✅ 使用COS URL进行API分析",
        "✅ 完整的分段分析和结果合并"
    ]
    
    for effect in effects:
        print(f"  {effect}")

def main():
    """主测试函数"""
    
    print("🚀 大文件URL修复测试")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        return
    
    # 检查COS配置
    cos_configured = all([
        os.getenv("TENCENT_SECRET_ID"),
        os.getenv("TENCENT_SECRET_KEY"), 
        os.getenv("COS_REGION"),
        os.getenv("COS_BUCKET")
    ])
    
    if not cos_configured:
        print("⚠️ COS未配置，分段将使用本地文件（可能导致API失败）")
    else:
        print("✅ COS已配置，分段将自动上传获取URL")
    
    test_results = []
    
    # 测试大文件URL预处理
    test_results.append(("大文件URL预处理", test_large_url_preprocessing()))
    
    # 测试单个分段分析
    test_results.append(("单个分段分析", test_single_segment_analysis()))
    
    # 显示修复总结
    show_fix_summary()
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 大文件URL修复成功！")
        print("💡 现在可以处理任意大小的URL视频")
        if cos_configured:
            print("📤 COS自动上传功能已启用")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
