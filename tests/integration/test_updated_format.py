"""
测试更新后的完整JSON格式
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from video_understanding_workflow import run_video_understanding

def test_updated_format():
    """测试更新后的完整JSON格式"""
    
    print("🧪 测试更新后的完整JSON格式...")
    
    # 使用一个较小的测试视频
    video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = run_video_understanding(
        video_url=video_url,
        user_request="请生成完整的视频脚本，包含详细的prompts字段，用于AI图像生成"
    )
    
    if result and not result.get("error_message"):
        print("✅ 测试成功！")
        
        # 检查生成的JSON文件
        output_file = result.get('output_file_path')
        if output_file and os.path.exists(output_file):
            import json
            
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            script = data.get('script', {})
            shots = script.get('shots', [])
            
            print(f"\n📊 格式验证结果:")
            print(f"  📄 输出文件: {output_file}")
            print(f"  🎬 镜头数量: {len(shots)}")
            
            # 检查第一个镜头的格式
            if shots:
                first_shot = shots[0]
                print(f"\n🔍 第一个镜头格式检查:")
                
                required_fields = [
                    "shotNumber", "startTime", "endTime", "duration",
                    "scene", "characters", "shotType", "cameraMovement",
                    "description", "bgmDescription", "bgmSource",
                    "dialogue", "onScreenText", "sfxDescription", "prompts"
                ]
                
                missing_fields = []
                for field in required_fields:
                    if field not in first_shot:
                        missing_fields.append(field)
                    else:
                        print(f"    ✅ {field}: 存在")
                
                if missing_fields:
                    print(f"    ❌ 缺失字段: {missing_fields}")
                else:
                    print("    🎉 所有必需字段都存在！")
                
                # 检查prompts字段结构
                prompts = first_shot.get('prompts', {})
                if prompts:
                    print(f"\n🎨 Prompts字段检查:")
                    
                    if 'ability' in prompts:
                        print(f"    ✅ ability: {len(prompts['ability'])} 项")
                        if prompts['ability']:
                            ability = prompts['ability'][0]
                            print(f"        - abilityType: {ability.get('abilityType', 'N/A')}")
                            print(f"        - parameters: {len(ability.get('parameters', {}))} 个参数")
                    
                    if 'cn' in prompts:
                        cn_prompt = prompts['cn']
                        print(f"    ✅ cn: {len(cn_prompt)} 字符")
                        if cn_prompt:
                            print(f"        预览: {cn_prompt[:50]}...")
                    
                    if 'en' in prompts:
                        en_prompt = prompts['en']
                        print(f"    ✅ en: {len(en_prompt)} 字符")
                        if en_prompt:
                            print(f"        预览: {en_prompt[:50]}...")
                
                print(f"\n📋 第一个镜头完整信息:")
                print(f"    🎬 镜头号: {first_shot.get('shotNumber')}")
                print(f"    ⏱️ 时间: {first_shot.get('startTime')}s - {first_shot.get('endTime')}s")
                print(f"    🎭 场景: {first_shot.get('scene', '')[:50]}...")
                print(f"    📸 镜头类型: {first_shot.get('shotType')}")
                print(f"    🎥 摄像机运动: {first_shot.get('cameraMovement')}")
        
        return True
    else:
        print(f"❌ 测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

if __name__ == "__main__":
    print("🚀 测试更新后的完整JSON格式")
    print("=" * 60)
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        exit(1)
    
    success = test_updated_format()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 完整JSON格式测试成功！")
        print("💡 现在生成的JSON完全符合你的接口规范")
    else:
        print("❌ 测试失败，请检查配置")
