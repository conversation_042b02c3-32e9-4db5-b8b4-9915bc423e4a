"""
端到端测试 - 完整的小红书图文生成流程
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入所有工具模块以触发注册
import tools.input.text_input
import tools.input.csv_reader
import tools.style_converters.xiaohongshu_style
import tools.content_processors.text_segmenter
import tools.generators.html_generator
import tools.output.image_converter

from core.assistant import IntelligentImageAssistant

def test_end_to_end_xiaohongshu():
    """端到端测试小红书流程"""
    print("🚀 端到端测试：小红书图文生成")
    print("="*60)
    
    assistant = IntelligentImageAssistant()
    
    # 测试文本
    test_text = "科技创新推动产业升级，新一代AI芯片性能提升50%，功耗降低30%，将应用于智能手机、自动驾驶等领域。这标志着人工智能技术进入新的发展阶段。"
    
    print(f"📝 输入文本: {test_text}")
    print()
    
    try:
        # 执行完整流程
        result = assistant.process_request(f"把这段新闻转换成小红书风格的图文：{test_text}")
        
        print("📊 执行结果:")
        print(f"  状态: {'✅ 成功' if not result.get('error_message') else '❌ 失败'}")
        
        if result.get("error_message"):
            print(f"  错误: {result['error_message']}")
            return False
        
        # 检查各个阶段的输出
        print(f"  🧠 意图识别: {result.get('user_intent', {}).get('style_requirement', 'N/A')}")
        print(f"  🔧 使用工具: {result.get('selected_tools', [])}")
        
        if result.get("raw_content"):
            print(f"  📄 原始内容: {result['raw_content'][:50]}...")
        
        if result.get("processed_content"):
            print(f"  ✨ 风格转换: {result['processed_content'][:50]}...")
        
        if result.get("content_segments"):
            print(f"  📝 内容分段: {len(result['content_segments'])} 个片段")
            for i, segment in enumerate(result["content_segments"][:2], 1):
                print(f"    片段{i}: {segment[:30]}...")
        
        if result.get("html_contents"):
            print(f"  🎨 HTML生成: {len(result['html_contents'])} 个HTML文件")
        
        if result.get("image_paths"):
            print(f"  🖼️  图片生成: {len(result['image_paths'])} 张图片")
            for i, path in enumerate(result["image_paths"], 1):
                print(f"    图片{i}: {path}")
                # 检查文件是否真实存在
                if os.path.exists(path):
                    size = os.path.getsize(path)
                    print(f"      ✅ 文件存在，大小: {size} bytes")
                else:
                    print(f"      ❌ 文件不存在")
        
        print("\n🎉 端到端测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_workflow():
    """测试CSV数据源工作流"""
    print("\n" + "="*60)
    print("🗂️  端到端测试：CSV数据源流程")
    print("="*60)
    
    # 确保CSV文件存在
    csv_path = "mock_rss_news.csv"
    if not os.path.exists(csv_path):
        print(f"❌ CSV文件不存在: {csv_path}")
        return False
    
    assistant = IntelligentImageAssistant()
    
    # 手动构造CSV流程
    from core.state import UniversalState
    
    initial_state = UniversalState(
        user_request="从CSV读取ID为1的新闻并转换为小红书风格",
        user_intent={"style_requirement": "csv数据源"},
        execution_plan=None,
        selected_tools=None,
        source_identifier="1",  # 添加CSV需要的ID
        raw_content=None,
        processed_content=None,
        content_segments=None,
        content_metadata=None,
        html_contents=None,
        image_paths=None,
        final_outputs=None,
        current_step=None,
        execution_history=None,
        error_message=None,
        tool_outputs=None,
        temp_data=None
    )
    
    try:
        # 生成执行计划
        plan = assistant.planner.generate_execution_plan(initial_state["user_intent"])
        initial_state["execution_plan"] = plan
        
        # 选择工具
        tools = assistant.selector.select_tools(plan)
        initial_state["selected_tools"] = tools
        
        print(f"📋 执行计划: {tools}")
        
        # 执行工具链
        result = assistant.executor.execute_tool_chain(tools, initial_state)
        
        print("📊 CSV流程结果:")
        print(f"  状态: {'✅ 成功' if not result.get('error_message') else '❌ 失败'}")
        
        if result.get("error_message"):
            print(f"  错误: {result['error_message']}")
            return False
        
        if result.get("content_metadata"):
            print(f"  📄 数据源: {result['content_metadata'].get('source', 'N/A')}")
            print(f"  🔑 数据ID: {result['content_metadata'].get('id', 'N/A')}")
        
        if result.get("image_paths"):
            print(f"  🖼️  生成图片: {len(result['image_paths'])} 张")
            for path in result["image_paths"]:
                print(f"    -> {path}")
        
        print("\n🎉 CSV流程测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ CSV流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试入口"""
    print("🧪 开始端到端测试")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 确保输出目录存在
    os.makedirs("output/images", exist_ok=True)
    
    success_count = 0
    total_count = 2
    
    # 测试1: 文本输入流程
    if test_end_to_end_xiaohongshu():
        success_count += 1
    
    # 测试2: CSV数据源流程
    if test_csv_workflow():
        success_count += 1
    
    # 总结
    print("\n" + "="*60)
    print("📈 测试总结")
    print("="*60)
    print(f"总测试数: {total_count}")
    print(f"成功: {success_count}")
    print(f"失败: {total_count - success_count}")
    
    if success_count == total_count:
        print("\n🎉 所有端到端测试通过！智能助手工作正常！")
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个测试失败，需要检查")

if __name__ == "__main__":
    main()