#!/usr/bin/env python3
"""
逐步测试每个工具的执行情况
"""
import sys
import os
sys.path.append('.')

from core.state import UniversalState
from tools.base.tool_registry import ToolRegistry

# 导入工具以触发注册
import tools.processors.story_segmenter
import tools.generators.ai_image_generator
import tools.generators.html_generator
import tools.output.image_converter

def test_story_segmenter():
    """测试故事分段工具"""
    print("🔍 测试故事分段工具...")
    
    tool = ToolRegistry.create_tool("content_processor.story_segmenter")
    if not tool:
        print("❌ 无法创建故事分段工具")
        return None
    
    # 测试数据
    test_state = UniversalState(
        user_request="程序员小王的一天：早上8点起床。到公司写代码。中午吃饭。下午加班。",
        story_text="程序员小王的一天：早上8点起床。到公司写代码。中午吃饭。下午加班。",
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=None, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    test_state.update({
        "max_segments": 4,
        "segment_style": "narrative"
    })
    
    try:
        result = tool.execute(test_state)
        if result.get("error_message"):
            print(f"❌ 分段失败: {result['error_message']}")
            return None
        else:
            segments = result.get("segments", [])
            print(f"✅ 分段成功，共 {len(segments)} 段")
            for i, seg in enumerate(segments, 1):
                print(f"  第{i}段: {seg['content'][:30]}...")
            return result
    except Exception as e:
        print(f"❌ 分段异常: {e}")
        return None

def test_ai_image_generator():
    """测试AI图像生成工具"""
    print("\n🖼️ 测试AI图像生成工具...")
    
    tool = ToolRegistry.create_tool("generator.ai_image")
    if not tool:
        print("❌ 无法创建AI图像生成工具")
        return None
    
    # 测试数据
    test_state = UniversalState(
        user_request="程序员小王早上起床",
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=None, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    test_state.update({
        "prompt": "程序员小王早上起床，匆忙洗漱",
        "style": "humor",
        "width": 810,
        "height": 1080
    })
    
    try:
        result = tool.execute(test_state)
        if result.get("error_message"):
            print(f"❌ 图像生成失败: {result['error_message']}")
            return None
        else:
            print(f"✅ 图像生成成功: {result.get('cos_url', 'No URL')[:50]}...")
            return result
    except Exception as e:
        print(f"❌ 图像生成异常: {e}")
        return None

def test_html_generator():
    """测试HTML生成工具"""
    print("\n📄 测试HTML生成工具...")
    
    tool = ToolRegistry.create_tool("generator.html")
    if not tool:
        print("❌ 无法创建HTML生成工具")
        return None
    
    # 测试数据
    test_state = UniversalState(
        user_request="程序员小王早上起床",
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=None, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    test_state.update({
        "title": "第1段",
        "content": "程序员小王的一天：早上8点起床，匆忙洗漱",
        "cos_url": "https://example.com/test.jpg",
        "layout_type": "story_card"
    })
    
    try:
        result = tool.execute(test_state)
        if result.get("error_message"):
            print(f"❌ HTML生成失败: {result['error_message']}")
            return None
        else:
            print(f"✅ HTML生成成功")
            return result
    except Exception as e:
        print(f"❌ HTML生成异常: {e}")
        return None

def test_image_converter():
    """测试图像转换工具"""
    print("\n📸 测试图像转换工具...")
    
    tool = ToolRegistry.create_tool("output.html2image")
    if not tool:
        print("❌ 无法创建图像转换工具")
        return None
    
    # 测试数据
    test_state = UniversalState(
        user_request="程序员小王早上起床",
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=None, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    test_state.update({
        "html_content": "<html><body><h1>测试</h1></body></html>",
        "output_filename": "test_card.png"
    })
    
    try:
        result = tool.execute(test_state)
        if result.get("error_message"):
            print(f"❌ 图像转换失败: {result['error_message']}")
            return None
        else:
            print(f"✅ 图像转换成功: {result.get('image_paths', [])}")
            return result
    except Exception as e:
        print(f"❌ 图像转换异常: {e}")
        return None

if __name__ == "__main__":
    print("🧪 开始逐步测试各个工具...")
    print("="*60)
    
    # 测试每个工具
    seg_result = test_story_segmenter()
    img_result = test_ai_image_generator()  
    html_result = test_html_generator()
    conv_result = test_image_converter()
    
    print("\n" + "="*60)
    print("📊 测试总结:")
    print(f"  分段工具: {'✅' if seg_result else '❌'}")
    print(f"  图像生成: {'✅' if img_result else '❌'}")
    print(f"  HTML生成: {'✅' if html_result else '❌'}")
    print(f"  图像转换: {'✅' if conv_result else '❌'}")