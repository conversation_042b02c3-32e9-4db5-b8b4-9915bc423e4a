"""
测试多种ability类型支持
"""
import os
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from video_understanding_workflow import run_video_understanding

def test_multiple_abilities():
    """测试多种ability类型的智能选择"""
    
    print("🧪 测试多种ability类型的智能选择...")
    
    # 使用测试视频
    video_url = "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    
    result = run_video_understanding(
        video_url=video_url,
        user_request="""请分析这个视频并为每个镜头选择最合适的ability类型：
        - 静态场景使用TEXT_TO_IMAGE
        - 有动作的镜头使用TEXT_TO_VIDEO或IMAGE_TO_VIDEO
        - 角色特写和对话使用DIGITAL_HUMAN
        - 复杂动作使用MOTION_CAPTURE
        - 可以组合多种ability类型
        
        请根据镜头内容智能选择，并在parameters中设置合适的参数。"""
    )
    
    if result and not result.get("error_message"):
        print("✅ 测试成功！")
        
        # 分析生成的ability类型
        output_file = result.get('output_file_path')
        if output_file and os.path.exists(output_file):
            
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            script = data.get('script', {})
            shots = script.get('shots', [])
            
            print(f"\n📊 Ability类型分析:")
            print(f"  📄 输出文件: {output_file}")
            print(f"  🎬 总镜头数: {len(shots)}")
            
            # 统计ability类型使用情况
            ability_stats = {}
            ability_combinations = []
            
            for i, shot in enumerate(shots):
                prompts = shot.get('prompts', {})
                abilities = prompts.get('ability', [])
                
                shot_abilities = []
                for ability in abilities:
                    ability_type = ability.get('abilityType', 'UNKNOWN')
                    shot_abilities.append(ability_type)
                    
                    if ability_type in ability_stats:
                        ability_stats[ability_type] += 1
                    else:
                        ability_stats[ability_type] = 1
                
                ability_combinations.append({
                    'shot': i + 1,
                    'abilities': shot_abilities,
                    'scene': shot.get('scene', '')[:50] + '...'
                })
            
            print(f"\n📈 Ability类型统计:")
            for ability_type, count in sorted(ability_stats.items()):
                print(f"  🎯 {ability_type}: {count} 次")
            
            print(f"\n🔍 前5个镜头的ability选择:")
            for combo in ability_combinations[:5]:
                abilities_str = ', '.join(combo['abilities'])
                print(f"  镜头{combo['shot']}: {abilities_str}")
                print(f"    场景: {combo['scene']}")
            
            # 检查是否有多样化的ability类型
            unique_abilities = set(ability_stats.keys())
            print(f"\n🎨 使用的ability类型数量: {len(unique_abilities)}")
            
            if len(unique_abilities) > 1:
                print("✅ 成功使用了多种ability类型！")
            else:
                print("⚠️ 只使用了单一ability类型，可能需要优化提示词")
            
            # 显示一个详细的ability示例
            if shots:
                first_shot = shots[0]
                prompts = first_shot.get('prompts', {})
                abilities = prompts.get('ability', [])
                
                print(f"\n📋 第一个镜头详细ability信息:")
                print(f"  🎬 镜头: {first_shot.get('shotNumber')}")
                print(f"  🎭 场景: {first_shot.get('scene', '')}")
                print(f"  📸 镜头类型: {first_shot.get('shotType')}")
                print(f"  🎥 摄像机运动: {first_shot.get('cameraMovement')}")
                
                for j, ability in enumerate(abilities):
                    print(f"  🎯 Ability {j+1}:")
                    print(f"    类型: {ability.get('abilityType', 'N/A')}")
                    
                    parameters = ability.get('parameters', {})
                    if parameters:
                        print(f"    参数:")
                        for key, value in parameters.items():
                            print(f"      {key}: {value}")
                    
                    print()
        
        return True
    else:
        print(f"❌ 测试失败: {result.get('error_message') if result else '未知错误'}")
        return False

def show_supported_abilities():
    """显示支持的ability类型"""
    
    abilities = {
        "TEXT_TO_IMAGE": "文生图 - 适用于静态场景、角色特写、风景镜头",
        "IMAGE_TO_IMAGE": "图生图 - 适用于风格转换、图像编辑",
        "TEXT_TO_VIDEO": "文生视频 - 适用于动态场景、运动镜头",
        "IMAGE_TO_VIDEO": "图生视频 - 适用于从静态到动态的转换",
        "DIGITAL_HUMAN": "数字人 - 适用于角色对话、表情变化",
        "MOTION_CAPTURE": "动作捕捉 - 适用于复杂动作、舞蹈",
        "FACE_SWAP": "换脸 - 适用于角色替换",
        "VOICE_CLONE": "声音克隆 - 适用于配音生成",
        "STYLE_TRANSFER": "风格迁移 - 适用于艺术风格转换"
    }
    
    print("🎯 支持的Ability类型:")
    print("=" * 60)
    
    for ability_type, description in abilities.items():
        print(f"  📌 {ability_type}")
        print(f"     {description}")
        print()

if __name__ == "__main__":
    print("🚀 测试多种Ability类型支持")
    print("=" * 60)
    
    # 显示支持的ability类型
    show_supported_abilities()
    
    # 检查API配置
    api_key = os.getenv("VIDEO_UNDERSTAND_KEY") or os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ 请先配置API密钥")
        exit(1)
    
    success = test_multiple_abilities()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 多种Ability类型测试成功！")
        print("💡 AI现在可以根据镜头内容智能选择合适的ability类型")
    else:
        print("❌ 测试失败，请检查配置")
