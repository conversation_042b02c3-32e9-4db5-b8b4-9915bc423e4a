#!/usr/bin/env python3
"""
完整流程测试 - 测试修复后的故事卡片生成
"""
import sys
import os
sys.path.append('.')

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入工具模块以触发注册
import tools.processors.story_segmenter
import tools.generators.ai_image_generator
import tools.generators.html_generator
import tools.output.image_converter

from core.state import UniversalState
from graphs.preset_graphs.story_cards_preset import StoryCardsPreset

def test_complete():
    """完整流程测试"""
    print("🧪 完整流程测试")
    print("="*60)
    
    # 测试故事
    test_story = """
程序员小王的一天：早上8点，闹钟响了三遍才起床，匆忙洗漱后发现又要迟到了。

到公司后，打开电脑第一件事就是查看昨天的代码，结果发现测试服务器挂了，一堆bug等着修复。

中午吃饭时，产品经理又提出了新的需求变更，要求今天下午就要完成，小王内心万分崩溃。

下午加班到很晚，终于把bug修完了，小王拖着疲惫的身体回到家，发誓明天一定要早睡。
"""
    
    # 创建初始状态
    initial_state = UniversalState(
        user_request=test_story,
        story_text=test_story,
        user_intent=None, execution_plan=None, selected_tools=None,
        raw_content=test_story, processed_content=None, content_segments=None,
        content_metadata=None, html_contents=None, image_paths=None,
        final_outputs=None, current_step=None, execution_history=None,
        error_message=None, tool_outputs=None, temp_data=None
    )
    
    # 自定义参数
    custom_params = {
        "max_segments": 4,
        "image_style": "humor",
        "html_theme": "story",
        "segment_style": "narrative"
    }
    
    preset = StoryCardsPreset()
    print(f"📝 测试故事长度: {len(test_story)} 字符")
    print(f"⚙️ 测试参数: {custom_params}")
    
    try:
        print(f"\n🚀 开始执行完整流程...")
        
        # 执行完整流程
        result = preset.execute(initial_state, custom_params)
        
        if result.get("error_message"):
            print(f"❌ 执行失败: {result['error_message']}")
        else:
            print(f"✅ 执行成功!")
            
            # 显示结果
            print(f"\n📊 生成结果:")
            print(f"   - 总段数: {result.get('total_segments', 0)}")
            print(f"   - 成功卡片数: {result.get('successful_cards', 0)}")
            
            if result.get("final_outputs"):
                outputs = result["final_outputs"]
                print(f"   - 生成图片数: {len(outputs.get('image_paths', []))}")
                print(f"   - HTML文件数: {len(outputs.get('html_files', []))}")
                
                # 显示具体文件路径
                if outputs.get('image_paths'):
                    print(f"\n🖼️ 生成的图片文件:")
                    for i, path in enumerate(outputs['image_paths'], 1):
                        print(f"   {i}. {path}")
                
                if outputs.get('segments'):
                    print(f"\n📝 故事分段:")
                    for i, segment in enumerate(outputs['segments'], 1):
                        print(f"   {i}. {segment.get('title', 'N/A')}")
                        
    except Exception as e:
        print(f"❌ 执行异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete()