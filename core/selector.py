"""
工具选择器 - 智能选择和验证工具组合
"""
from typing import List, Dict, Any, Optional

class HybridSelector:
    """混合工具选择器，结合规则和智能选择"""
    
    def __init__(self):
        self.rule_matcher = RuleBasedMatcher()
        self.validator = ToolChainValidator()
    
    def select_tools(self, execution_plan: Dict[str, Any]) -> List[str]:
        """选择工具链"""
        
        # 延迟导入避免循环依赖
        from tools.base.tool_registry import ToolRegistry
        
        # 从执行计划中获取工具列表
        selected_tools = execution_plan.get("selected_tools", [])
        
        # 验证工具是否存在
        validated_tools = []
        for tool_name in selected_tools:
            if ToolRegistry.get_tool(tool_name):
                validated_tools.append(tool_name)
            else:
                print(f"警告: 工具 {tool_name} 不存在，尝试查找替代品")
                alternative = self._find_alternative(tool_name)
                if alternative:
                    validated_tools.append(alternative)
        
        # 验证工具链的有效性
        if self.validator.validate_chain(validated_tools):
            return validated_tools
        else:
            print("工具链验证失败，使用回退方案")
            return execution_plan.get("fallback_plan", ["input.text_input", "output.image"])
    
    def _find_alternative(self, tool_name: str) -> Optional[str]:
        """查找替代工具"""
        from tools.base.tool_registry import ToolRegistry
        from tools.base.tool_interface import ToolCategory
        
        category_mapping = {
            "input": ToolCategory.INPUT,
            "analyzer": ToolCategory.ANALYZER,
            "style_converter": ToolCategory.STYLE_CONVERTER,
            "content_processor": ToolCategory.CONTENT_PROCESSOR,
            "generator": ToolCategory.GENERATOR,
            "output": ToolCategory.OUTPUT
        }
        
        # 从工具名称中提取类别
        if "." in tool_name:
            category_name = tool_name.split(".")[0]
            category = category_mapping.get(category_name)
            
            if category:
                # 获取该类别的所有工具
                available_tools = ToolRegistry.get_tools_by_category(category)
                if available_tools:
                    # 返回第一个可用工具
                    return list(available_tools.keys())[0]
        
        return None

class RuleBasedMatcher:
    """基于规则的工具匹配器"""
    
    def __init__(self):
        self.rules = {
            # 可以后续扩展更多规则
        }
    
    def match(self, intent: Dict[str, Any]) -> Optional[List[str]]:
        """规则匹配"""
        # 暂时返回None，让系统使用计划中的工具
        return None

class ToolChainValidator:
    """工具链验证器"""
    
    def validate_chain(self, tools: List[str]) -> bool:
        """验证工具链的有效性"""
        
        from tools.base.tool_registry import ToolRegistry
        
        if not tools:
            return False
        
        # 检查所有工具是否存在
        for tool_name in tools:
            if not ToolRegistry.get_tool(tool_name):
                print(f"验证失败: 工具 {tool_name} 不存在")
                return False
        
        # 检查工具链的逻辑顺序
        # 至少需要一个输入工具和一个输出工具
        has_input = any(tool.startswith("input.") for tool in tools)
        has_output = any(tool.startswith("output.") for tool in tools)
        
        if not has_input:
            print("验证失败: 缺少输入工具")
            return False
        
        if not has_output:
            print("验证失败: 缺少输出工具")
            return False
        
        return True