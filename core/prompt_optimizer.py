"""
专业绘画提示词优化器 - 使用LLM生成高质量的绘画提示词
"""
import logging
from typing import Dict, Any, Optional
from llm_services import get_llm
from core.style_config import get_style_config_manager

logger = logging.getLogger(__name__)

class PromptOptimizer:
    """专业绘画提示词优化器"""
    
    def __init__(self):
        self.style_manager = get_style_config_manager()
        
        # 绘画提示词优化模板
        self.optimization_templates = {
            "humor": """
你是一位专业的AI绘画提示词工程师，专门擅长创作幽默搞笑风格的插画提示词。

用户内容：{content}
风格要求：{style_description}
视觉指导：{visual_guide}

请根据以上信息，生成一个专业的英文绘画提示词，要求：

1. **主题描述**：将用户内容转化为具体的视觉场景描述
2. **风格特征**：严格遵循极简手绘幽默风格的视觉要求
3. **技术参数**：包含专业的绘画技法描述
4. **质量控制**：添加高质量、高分辨率等质量要求

提示词结构应包含：
- 主要场景和角色
- 具体的动作和表情
- 风格关键词（minimalist, hand-drawn, doodle style）
- 色彩要求（white background, simple colors）
- 技术要求（clean lines, sketch style）
- 质量标准（high quality, professional illustration）

请直接返回优化后的英文提示词，不要其他说明：
""",
            
            "meme_classic": """
你是一位专业的meme表情包提示词专家。

用户内容：{content}
风格要求：{style_description}

请生成经典meme格式的绘画提示词，要求：

1. **经典meme风格**：黑色粗体字、白色背景、高对比度
2. **简单直接**：避免复杂装饰，保持meme的直接性
3. **表情夸张**：强调夸张的面部表情和肢体语言
4. **布局经典**：适合上下文字的meme格式

关键要素：
- classic internet meme style
- bold typography layout
- exaggerated expressions
- high contrast
- simple composition
- white background

请直接返回英文提示词：
""",
            
            "kawaii": """
你是一位日式可爱插画专家，擅长kawaii风格的绘画提示词。

用户内容：{content}
风格要求：{style_description}

请生成可爱kawaii风格的提示词，包含：

1. **可爱元素**：大眼睛、圆润造型、柔和线条
2. **色彩搭配**：粉色、淡紫色、薄荷绿等治愈色彩
3. **装饰元素**：星星、爱心、花朵、彩虹等
4. **日式美学**：kawaii文化的核心特征

关键词汇：
- kawaii cute style
- soft pastel colors
- big round eyes
- adorable characters
- healing atmosphere
- Japanese cute aesthetic

请直接返回英文提示词：
""",
            
            "xiaohongshu": """
你是小红书风格插画专家，了解年轻人喜欢的视觉风格。

用户内容：{content}
风格要求：{style_description}

请生成适合小红书的现代插画提示词，特点：

1. **现代时尚**：符合年轻人审美的现代设计
2. **温暖治愈**：暖色调、舒适的视觉感受
3. **生活化**：贴近日常生活的场景和元素
4. **社交媒体**：适合在社交平台分享的视觉效果

设计要素：
- modern illustration style
- warm and cozy atmosphere
- lifestyle oriented
- social media friendly
- clean and trendy design

请直接返回英文提示词：
""",
            
            "business": """
你是商务插画专家，专门创作专业商务风格的视觉内容。

用户内容：{content}
风格要求：{style_description}

请生成专业商务风格的提示词，要求：

1. **专业性**：体现商务场景的正式和专业
2. **现代感**：符合当代商务美学的设计语言
3. **信任感**：传达可靠、稳重的视觉印象
4. **国际化**：适合全球商务环境的视觉风格

商务元素：
- professional business illustration
- modern corporate style
- clean and sophisticated
- trustworthy visual language
- international business aesthetic

请直接返回英文提示词：
"""
        }
    
    def optimize_prompt(self, content: str, style_name: str, 
                       additional_requirements: Dict[str, Any] = None) -> str:
        """
        使用LLM优化绘画提示词
        
        Args:
            content: 用户原始内容
            style_name: 风格名称 (humor, meme_classic, kawaii, etc.)
            additional_requirements: 额外要求，如特定元素、色彩等
            
        Returns:
            优化后的专业绘画提示词
        """
        try:
            # 获取风格配置
            style_config = self.style_manager.get_style(style_name)
            
            # 构建LLM优化请求
            template = self.optimization_templates.get(style_name, self.optimization_templates["humor"])
            
            # 准备模板变量
            template_vars = {
                "content": content,
                "style_description": style_config.description,
                "visual_guide": style_config.visual_style.replace('\n', ' '),
            }
            
            # 添加额外要求
            if additional_requirements:
                extra_info = self._format_additional_requirements(additional_requirements)
                template += f"\n\n额外要求：{extra_info}"
            
            # 格式化请求
            optimization_prompt = template.format(**template_vars)
            
            # 调用LLM
            llm = get_llm()
            response = llm.invoke(optimization_prompt)
            optimized_prompt = response.content.strip()
            
            # 后处理：确保提示词质量
            final_prompt = self._post_process_prompt(optimized_prompt, style_name)
            
            logger.info(f"提示词优化成功 - 风格: {style_name}")
            logger.debug(f"原始内容: {content}")
            logger.debug(f"优化结果: {final_prompt[:200]}...")
            
            return final_prompt
            
        except Exception as e:
            logger.error(f"LLM提示词优化失败: {e}")
            # 回退到基础模板
            return self._fallback_prompt(content, style_name)
    
    def _format_additional_requirements(self, requirements: Dict[str, Any]) -> str:
        """格式化额外要求"""
        formatted = []
        
        if "keywords" in requirements:
            formatted.append(f"必须包含关键词: {', '.join(requirements['keywords'])}")
        
        if "colors" in requirements:
            formatted.append(f"色彩要求: {requirements['colors']}")
        
        if "mood" in requirements:
            formatted.append(f"情绪氛围: {requirements['mood']}")
        
        if "elements" in requirements:
            formatted.append(f"必须包含元素: {', '.join(requirements['elements'])}")
        
        if "avoid" in requirements:
            formatted.append(f"避免元素: {', '.join(requirements['avoid'])}")
        
        return "; ".join(formatted)
    
    def _post_process_prompt(self, prompt: str, style_name: str) -> str:
        """后处理提示词，确保质量"""
        # 清理格式
        prompt = prompt.replace('\n', ' ').replace('\r', ' ')
        prompt = ' '.join(prompt.split())  # 移除多余空格
        
        # 确保包含基础质量要求
        quality_terms = "high quality, professional illustration, detailed, masterpiece"
        if "high quality" not in prompt.lower():
            prompt += f", {quality_terms}"
        
        # 根据风格添加必要的技术参数
        if style_name == "humor" and "hand-drawn" not in prompt.lower():
            prompt += ", hand-drawn style, doodle aesthetic"
        elif style_name == "meme_classic" and "meme" not in prompt.lower():
            prompt += ", classic internet meme format"
        elif style_name == "kawaii" and "kawaii" not in prompt.lower():
            prompt += ", kawaii cute style, adorable"
        
        # 限制长度
        if len(prompt) > 500:
            prompt = prompt[:497] + "..."
        
        return prompt
    
    def _fallback_prompt(self, content: str, style_name: str) -> str:
        """LLM失败时的回退提示词"""
        logger.warning("使用回退提示词模板")
        
        style_config = self.style_manager.get_style(style_name)
        
        fallback_templates = {
            "humor": f"{content}, minimalist hand-drawn illustration, simple doodle style, white background, funny cartoon, clean lines, sketch aesthetic, high quality",
            "meme_classic": f"{content}, classic internet meme style, bold text layout, white background, high contrast, simple composition, exaggerated expressions",
            "kawaii": f"{content}, kawaii cute style, soft pastel colors, adorable characters, big eyes, Japanese cute aesthetic, healing atmosphere",
            "xiaohongshu": f"{content}, modern lifestyle illustration, warm colors, trendy design, social media style, clean and cozy",
            "business": f"{content}, professional business illustration, modern corporate style, clean design, sophisticated, trustworthy"
        }
        
        return fallback_templates.get(style_name, fallback_templates["humor"])
    
    def batch_optimize(self, contents: list, style_name: str) -> list:
        """批量优化提示词"""
        results = []
        for content in contents:
            try:
                optimized = self.optimize_prompt(content, style_name)
                results.append(optimized)
            except Exception as e:
                logger.error(f"批量优化失败: {e}")
                results.append(self._fallback_prompt(content, style_name))
        return results

# 全局提示词优化器实例
prompt_optimizer = PromptOptimizer()

def get_prompt_optimizer() -> PromptOptimizer:
    """获取全局提示词优化器"""
    return prompt_optimizer