"""
动态执行引擎 - 负责执行工具链
"""
from typing import List, Dict, Any
from core.state import UniversalState

class DynamicExecutor:
    """动态执行引擎"""
    
    def __init__(self):
        pass
    
    def execute_tool_chain(self, tools: List[str], state: UniversalState) -> UniversalState:
        """执行工具链"""
        
        from tools.base.tool_registry import ToolRegistry
        
        current_state = state.copy()
        
        for tool_name in tools:
            print(f"执行工具: {tool_name}")
            
            # 创建工具实例
            tool = ToolRegistry.create_tool(tool_name)
            if not tool:
                error_msg = f"无法创建工具实例: {tool_name}"
                print(error_msg)
                current_state["error_message"] = error_msg
                break
            
            # 执行工具
            try:
                result = tool.execute(current_state)
                
                # 更新状态
                current_state.update(result)
                
                # 检查是否有错误
                if result.get("error_message"):
                    print(f"工具执行出错: {result['error_message']}")
                    break
                    
            except Exception as e:
                error_msg = f"工具 {tool_name} 执行异常: {str(e)}"
                print(error_msg)
                current_state["error_message"] = error_msg
                break
        
        return current_state